.unauthorized-access {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 2rem;
}

.unauthorized-container {
    background: white;
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    width: 100%;
}

.unauthorized-icon {
    margin-bottom: 2rem;
    display: flex;
    justify-content: center;
}

.unauthorized-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.unauthorized-message {
    margin-bottom: 2.5rem;
}

.unauthorized-message p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.role-info {
    background: #f9fafb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    border-left: 4px solid #F0B21B;
}

.role-info p {
    margin: 0.5rem 0;
    font-size: 0.95rem;
    text-align: left;
}

.role-info strong {
    color: #374151;
    font-weight: 600;
}

.unauthorized-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.unauthorized-actions .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.btn-primary {
    background: #F0B21B;
    color: white;
}

.btn-primary:hover {
    background: #d4a017;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: #6b7280;
    border: 2px solid #d1d5db;
}

.btn-outline:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
}

.admin-contact {
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.admin-contact p {
    font-size: 0.9rem;
    color: #9ca3af;
    font-style: italic;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .unauthorized-access {
        padding: 1rem;
    }
    
    .unauthorized-container {
        padding: 2rem;
    }
    
    .unauthorized-title {
        font-size: 2rem;
    }
    
    .unauthorized-actions {
        gap: 0.75rem;
    }
}

/* Loading Animation */
.auth-loading {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f9fafb;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #F0B21B;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.auth-loading p {
    color: #6b7280;
    font-size: 1.1rem;
    margin: 0;
}
