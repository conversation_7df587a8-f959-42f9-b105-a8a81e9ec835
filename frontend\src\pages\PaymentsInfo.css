/* Payments Information Page Styles */
.payments-info-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
}

.payments-info-page .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Section */
.payments-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.payments-header h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #F0B21B, #e6a617);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.payments-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Navigation Tabs */
.payments-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    background: white;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-weight: 500;
    color: #6b7280;
}

.tab-button:hover {
    background: #f3f4f6;
    color: #374151;
}

.tab-button.active {
    background: linear-gradient(135deg, #F0B21B, #e6a617);
    color: white;
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.tab-icon {
    font-size: 1.25rem;
}

.tab-label {
    font-size: 1rem;
}

/* Tab Content */
.tab-content {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.tab-panel h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Payment Methods Grid */
.payment-methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.payment-method-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.payment-method-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: #F0B21B;
}

.method-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.method-icon {
    flex-shrink: 0;
}

.method-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.method-info p {
    color: #6b7280;
    font-size: 0.875rem;
}

.method-details {
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-weight: 500;
    color: #374151;
}

.detail-value {
    font-weight: 600;
    color: #F0B21B;
}

.method-features h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.method-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.method-features li {
    padding: 0.25rem 0;
    color: #6b7280;
    font-size: 0.875rem;
    position: relative;
    padding-left: 1rem;
}

.method-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

/* Banks Grid */
.banks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.bank-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.bank-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.1);
    border-color: #F0B21B;
}

.bank-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.bank-logo {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.75rem;
    text-align: center;
}

.bank-info h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.bank-info p {
    font-size: 0.875rem;
    color: #6b7280;
}

.bank-details {
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
}

.bank-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.bank-features li {
    padding: 0.25rem 0;
    color: #6b7280;
    font-size: 0.875rem;
    position: relative;
    padding-left: 1rem;
}

.bank-features li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #F0B21B;
    font-weight: bold;
}

/* PayMongo Info */
.paymongo-info {
    margin-top: 2rem;
}

.gateway-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.overview-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
}

.overview-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.overview-card p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.overview-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.overview-card li {
    padding: 0.5rem 0;
    color: #6b7280;
    position: relative;
    padding-left: 1.5rem;
}

.overview-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

/* Fee Structure */
.fee-structure {
    margin: 2rem 0;
}

.fee-structure h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.fees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.fee-card {
    background: linear-gradient(135deg, #F0B21B, #e6a617);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.fee-card h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.fee-amount {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.fee-card p {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Payment Flow */
.payment-flow {
    margin: 2rem 0;
}

.payment-flow h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.flow-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.flow-step {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #F0B21B, #e6a617);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payments-header h1 {
        font-size: 2rem;
    }
    
    .payments-subtitle {
        font-size: 1rem;
    }
    
    .payments-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .tab-button {
        justify-content: center;
        padding: 0.75rem 1rem;
    }
    
    .tab-content {
        padding: 1.5rem;
    }
    
    .payment-methods-grid,
    .banks-grid {
        grid-template-columns: 1fr;
    }
    
    .gateway-overview {
        grid-template-columns: 1fr;
    }
    
    .fees-grid {
        grid-template-columns: 1fr;
    }
    
    .flow-steps {
        grid-template-columns: 1fr;
    }
}

/* Security Features */
.security-features {
    margin-top: 2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.security-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.security-feature:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.1);
    border-color: #F0B21B;
}

.feature-icon {
    flex-shrink: 0;
}

.feature-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Trust Indicators */
.trust-indicators {
    margin: 3rem 0;
}

.trust-indicators h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.indicators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.indicator {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.indicator:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #F0B21B;
}

.indicator-badge {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #F0B21B, #e6a617);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    margin: 0 auto 1rem;
}

.indicator p {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Security Tips */
.security-tips {
    margin: 3rem 0;
}

.security-tips h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.tips-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.tip {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.tip:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #F0B21B;
}

.tip-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.tip-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.tip-content p {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Additional responsive adjustments */
@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
    }

    .indicators-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .tips-list {
        grid-template-columns: 1fr;
    }

    .security-feature,
    .tip {
        flex-direction: column;
        text-align: center;
    }

    .feature-icon,
    .tip-icon {
        align-self: center;
    }
}

@media (max-width: 480px) {
    .indicators-grid {
        grid-template-columns: 1fr;
    }

    .indicator-badge {
        width: 50px;
        height: 50px;
        font-size: 0.75rem;
    }
}

/* Animation for tab transitions */
.tab-panel {
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
