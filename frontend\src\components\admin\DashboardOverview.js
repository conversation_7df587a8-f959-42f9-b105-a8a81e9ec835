import React, { useState, useEffect } from 'react';
import apiClient from '../../services/apiClient';
import websocketService from '../../services/websocketService';
import { toast } from 'react-toastify';
import './DashboardOverview.css';
import './AdminComponents.css';
import {
  InventoryIcon,
  OrdersIcon,
  UsersIcon,
  WarningIcon,
  InfoIcon,
  AnalyticsIcon
} from '../admin/icons/AdminIcons';

const DashboardOverview = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [inventoryAlerts, setInventoryAlerts] = useState([]);
  const [realtimeUpdates, setRealtimeUpdates] = useState([]);

  useEffect(() => {
    fetchDashboardData();
    fetchInventoryAlerts();
    setupWebSocketListeners();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Try to fetch real dashboard data from API
      try {
        const response = await apiClient.get('/api/admin/dashboard');
        if (response.success) {
          setDashboardData(response.data);
          setLoading(false);
          return;
        }
      } catch (apiError) {
        console.warn('Dashboard API not available, using mock data:', apiError);
      }

      // Fallback to mock data if API is not available
      const mockData = {
        overview: {
          totalProducts: 150,
          totalOrders: 1250,
          totalRevenue: 125000.00,
          totalCustomers: 450,
          lowStockItems: 8,
          pendingOrders: 25
        },
        recentOrders: [
          {
            id: 'ORD001',
            orderNumber: 'ORD-2024-001',
            customerName: 'John Doe',
            total: 696.98,
            status: 'Processing',
            date: new Date().toISOString()
          },
          {
            id: 'ORD002',
            orderNumber: 'ORD-2024-002',
            customerName: 'Jane Smith',
            total: 1299.99,
            status: 'Shipped',
            date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
          }
        ],
        topProducts: [
          {
            id: 'PROD001',
            name: 'Executive Office Chair',
            sales: 45,
            revenue: 13499.55
          },
          {
            id: 'PROD002',
            name: 'Standing Desk',
            sales: 32,
            revenue: 19199.68
          }
        ],
        inventoryAlerts: [
          {
            productName: 'Standing Desk',
            currentStock: 8,
            reorderLevel: 15,
            status: 'Low Stock'
          },
          {
            productName: 'Conference Table',
            currentStock: 3,
            reorderLevel: 5,
            status: 'Critical'
          }
        ]
      };

      setDashboardData(mockData);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setLoading(false);
    }
  };

  const fetchInventoryAlerts = async () => {
    try {
      const response = await apiClient.get('/api/inventory/alerts/low-stock');
      if (response.success) {
        setInventoryAlerts(response.data.alerts);
      }
    } catch (error) {
      console.error('Error fetching inventory alerts:', error);
      // Use mock data as fallback
      setInventoryAlerts([
        {
          productName: 'Standing Desk',
          currentStock: 8,
          reorderLevel: 15,
          status: 'Low Stock'
        },
        {
          productName: 'Conference Table',
          currentStock: 3,
          reorderLevel: 5,
          status: 'Critical'
        }
      ]);
    }
  };

  const setupWebSocketListeners = () => {
    // Subscribe to order updates
    websocketService.subscribeToOrders();
    websocketService.subscribeToDashboard();
    websocketService.subscribeToInventory();

    // Handle new order created
    const handleOrderCreated = (data) => {
      const { order, customerName } = data;

      // Update dashboard statistics
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          overview: {
            ...prevData.overview,
            totalOrders: prevData.overview.totalOrders + 1,
            totalRevenue: prevData.overview.totalRevenue + (order.TotalAmount || 0),
            pendingOrders: prevData.overview.pendingOrders + 1
          },
          recentOrders: [
            {
              id: order.OrderID,
              orderNumber: order.OrderNumber,
              customerName: order.CustomerName || customerName,
              total: order.TotalAmount,
              status: order.OrderStatus,
              date: order.CreatedAt || new Date().toISOString()
            },
            ...prevData.recentOrders.slice(0, 4) // Keep only 5 recent orders
          ]
        };
      });

      // Show notification
      toast.success(`New order received: ${order.OrderNumber}`, {
        position: "top-right",
        autoClose: 5000,
      });
    };

    // Handle order status updates
    const handleOrderStatusUpdate = (data) => {
      const { orderId, newStatus } = data;

      // Update recent orders list
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          recentOrders: prevData.recentOrders.map(order =>
            order.id === orderId
              ? { ...order, status: newStatus }
              : order
          )
        };
      });
    };

    // Handle inventory updates
    const handleInventoryUpdate = (data) => {
      const { item, type } = data;

      // Add to realtime updates
      setRealtimeUpdates(prev => [
        {
          id: Date.now(),
          type: 'inventory_update',
          message: `${item.productName} stock updated`,
          timestamp: new Date().toISOString(),
          data: item
        },
        ...prev.slice(0, 9) // Keep only last 10 updates
      ]);

      toast.info(`Inventory updated: ${item.productName}`, {
        position: "top-right",
        autoClose: 3000,
      });
    };

    // Handle low stock alerts
    const handleLowStockAlert = (data) => {
      const { item, alertLevel } = data;

      toast.warning(`${alertLevel}: ${item.productName}`, {
        position: "top-right",
        autoClose: 5000,
      });

      // Update inventory alerts
      setInventoryAlerts(prev => {
        const existingIndex = prev.findIndex(alert => alert.sku === item.sku);
        const newAlert = {
          productName: item.productName,
          sku: item.sku,
          currentStock: item.currentStock,
          reorderLevel: item.reorderLevel,
          status: alertLevel
        };

        if (existingIndex >= 0) {
          const updated = [...prev];
          updated[existingIndex] = newAlert;
          return updated;
        } else {
          return [newAlert, ...prev];
        }
      });

      // Update dashboard overview
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          overview: {
            ...prevData.overview,
            lowStockItems: prevData.overview.lowStockItems + 1
          }
        };
      });
    };

    // Set up event listeners
    if (websocketService.socket) {
      websocketService.socket.on('order:created', handleOrderCreated);
      websocketService.socket.on('order:status-updated', handleOrderStatusUpdate);
      websocketService.socket.on('inventory:updated', handleInventoryUpdate);
      websocketService.socket.on('inventory:low-stock-alert', handleLowStockAlert);
    }

    // Cleanup function
    return () => {
      if (websocketService.socket) {
        websocketService.socket.off('order:created', handleOrderCreated);
        websocketService.socket.off('order:status-updated', handleOrderStatusUpdate);
        websocketService.socket.off('inventory:updated', handleInventoryUpdate);
        websocketService.socket.off('inventory:low-stock-alert', handleLowStockAlert);
      }
    };
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-PH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'processing':
        return '#f39c12';
      case 'shipped':
        return '#3498db';
      case 'delivered':
        return '#27ae60';
      case 'cancelled':
        return '#e74c3c';
      case 'low stock':
        return '#f39c12';
      case 'critical':
        return '#e74c3c';
      default:
        return '#95a5a6';
    }
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="dashboard-error">
        <p>Failed to load dashboard data</p>
      </div>
    );
  }

  return (
    <div className="dashboard-overview">
      <div className="dashboard-header">
        <h1>Dashboard Overview</h1>
        <p>Welcome to your inventory management dashboard</p>
      </div>

      {/* Key Metrics */}
      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-icon">
            <InventoryIcon color="#F0B21B" />
          </div>
          <div className="metric-content">
            <h3>{dashboardData.overview.totalProducts}</h3>
            <p>Total Products</p>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <OrdersIcon color="#3B82F6" />
          </div>
          <div className="metric-content">
            <h3>{dashboardData.overview.totalOrders}</h3>
            <p>Total Orders</p>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <AnalyticsIcon color="#10B981" />
          </div>
          <div className="metric-content">
            <h3>{formatCurrency(dashboardData.overview.totalRevenue)}</h3>
            <p>Total Revenue</p>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <UsersIcon color="#8B5CF6" />
          </div>
          <div className="metric-content">
            <h3>{dashboardData.overview.totalCustomers}</h3>
            <p>Total Customers</p>
          </div>
        </div>

        <div className="metric-card alert">
          <div className="metric-icon">
            <WarningIcon color="#F59E0B" />
          </div>
          <div className="metric-content">
            <h3>{dashboardData.overview.lowStockItems}</h3>
            <p>Low Stock Items</p>
          </div>
        </div>

        <div className="metric-card pending">
          <div className="metric-icon">
            <InfoIcon color="#6B7280" />
          </div>
          <div className="metric-content">
            <h3>{dashboardData.overview.pendingOrders}</h3>
            <p>Pending Orders</p>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        {/* Recent Orders */}
        <div className="admin-card">
          <div className="admin-card-header">
            <h2 className="admin-card-title">Recent Orders</h2>
            <button className="admin-btn admin-btn-primary">View All</button>
          </div>
          <div className="table-container">
            <table className="admin-table">
              <thead>
                <tr>
                  <th>Order Number</th>
                  <th>Customer</th>
                  <th>Total</th>
                  <th>Status</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                {dashboardData.recentOrders.map(order => (
                  <tr key={order.id}>
                    <td>{order.orderNumber}</td>
                    <td>{order.customerName}</td>
                    <td>{formatCurrency(order.total)}</td>
                    <td>
                      <span 
                        className="status-badge"
                        style={{ backgroundColor: getStatusColor(order.status) }}
                      >
                        {order.status}
                      </span>
                    </td>
                    <td>{formatDate(order.date)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Top Products */}
        <div className="admin-card">
          <div className="admin-card-header">
            <h2 className="admin-card-title">Top Selling Products</h2>
          </div>
          <div className="products-list">
            {dashboardData.topProducts.map(product => (
              <div key={product.id} className="product-item">
                <div className="product-info">
                  <h4>{product.name}</h4>
                  <p>{product.sales} units sold</p>
                </div>
                <div className="product-revenue">
                  {formatCurrency(product.revenue)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Inventory Alerts */}
        <div className="admin-card">
          <div className="admin-card-header">
            <h2 className="admin-card-title">Inventory Alerts</h2>
            <button className="admin-btn admin-btn-secondary">Manage Inventory</button>
          </div>
          <div className="alerts-list">
            {(inventoryAlerts.length > 0 ? inventoryAlerts : dashboardData.inventoryAlerts).map((alert, index) => (
              <div key={index} className="alert-item">
                <div className="alert-info">
                  <h4>{alert.productName}</h4>
                  <p>Current Stock: {alert.currentStock} | Reorder Level: {alert.reorderLevel}</p>
                </div>
                <span 
                  className="status-badge"
                  style={{ backgroundColor: getStatusColor(alert.status) }}
                >
                  {alert.status}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Real-time Updates */}
        {realtimeUpdates.length > 0 && (
          <div className="admin-card">
            <div className="admin-card-header">
              <h2 className="admin-card-title">Recent Updates</h2>
              <button
                className="admin-btn admin-btn-secondary"
                onClick={() => setRealtimeUpdates([])}
              >
                Clear
              </button>
            </div>
            <div className="updates-list">
              {realtimeUpdates.map((update) => (
                <div key={update.id} className="update-item">
                  <div className="update-info">
                    <div className="update-type">
                      {update.type === 'inventory_update' && '📦'}
                      {update.type === 'stock_change' && '📊'}
                      {update.type === 'order_update' && '🛒'}
                    </div>
                    <div className="update-content">
                      <p className="update-message">{update.message}</p>
                      <span className="update-time">
                        {formatDate(update.timestamp)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardOverview;
