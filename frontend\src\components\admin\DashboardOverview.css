/* Dashboard Overview Styles */
.dashboard-overview {
  width: 100%;
}

.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.dashboard-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1.1rem;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.metric-card.alert {
  border-left: 4px solid #f39c12;
}

.metric-card.pending {
  border-left: 4px solid #3498db;
}

.metric-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #F0B21B, #d4a017);
  border-radius: 12px;
  color: white;
}

.metric-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.25rem 0;
}

.metric-content p {
  color: #7f8c8d;
  margin: 0;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Products List */
.products-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.product-item:hover {
  background: #e9ecef;
}

.product-info h4 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.product-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.product-revenue {
  font-size: 1.1rem;
  font-weight: 600;
  color: #27ae60;
}

/* Alerts List */
.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.alert-item:hover {
  background: #fef2f2;
  border-color: #fca5a5;
}

.alert-info h4 {
  margin: 0 0 0.25rem 0;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 600;
}

.alert-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Loading and Error States */
.dashboard-loading,
.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #7f8c8d;
}

.dashboard-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #F0B21B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .metric-card {
    padding: 1rem;
  }

  .metric-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
  }

  .metric-content h3 {
    font-size: 1.5rem;
  }

  .dashboard-header h1 {
    font-size: 1.5rem;
  }

  .dashboard-header p {
    font-size: 1rem;
  }

  .product-item,
  .alert-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .product-revenue {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .metric-card {
    padding: 1rem;
    gap: 0.75rem;
  }

  .metric-icon {
    font-size: 1.8rem;
    width: 45px;
    height: 45px;
  }

  .metric-content h3 {
    font-size: 1.3rem;
  }

  .metric-content p {
    font-size: 0.85rem;
  }

  .admin-card {
    padding: 1rem;
  }

  .admin-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .admin-btn {
    width: 100%;
  }
}

/* Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Card hover effects */
.admin-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.admin-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Button hover effects */
.admin-btn {
  transition: all 0.3s ease;
}

.admin-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Real-time Updates */
.updates-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.update-item {
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.update-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.update-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.update-type {
  font-size: 20px;
  flex-shrink: 0;
}

.update-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.update-message {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.update-time {
  font-size: 12px;
  color: #64748b;
}
