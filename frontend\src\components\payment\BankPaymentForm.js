import React, { useState, useEffect } from 'react';
import BankSelection from './BankSelection';
import paymentService from '../../services/paymentService';
import './BankPaymentForm.css';

const BankPaymentForm = ({ 
    orderData, 
    onPaymentSubmit, 
    onBack, 
    isLoading = false,
    fees 
}) => {
    const [selectedBank, setSelectedBank] = useState('');
    const [bankFees, setBankFees] = useState(null);
    const [formData, setFormData] = useState({
        accountName: '',
        accountNumber: '',
        email: '',
        phone: ''
    });
    const [errors, setErrors] = useState({});
    const [isFormValid, setIsFormValid] = useState(false);

    useEffect(() => {
        // Calculate bank-specific fees when bank is selected
        if (selectedBank && orderData?.totalAmount) {
            const calculateBankFees = async () => {
                try {
                    const feeData = await paymentService.calculateFees(
                        orderData.totalAmount,
                        'bank',
                        selectedBank
                    );
                    setBankFees(feeData.data);
                } catch (error) {
                    console.error('Error calculating bank fees:', error);
                }
            };
            calculateBankFees();
        }
    }, [selectedBank, orderData?.totalAmount]);

    useEffect(() => {
        // Validate form
        const requiredFields = ['accountName', 'email'];
        const hasRequiredFields = requiredFields.every(field => 
            formData[field] && formData[field].trim() !== ''
        );
        const hasSelectedBank = selectedBank !== '';
        const hasNoErrors = Object.keys(errors).length === 0;
        
        setIsFormValid(hasRequiredFields && hasSelectedBank && hasNoErrors);
    }, [formData, selectedBank, errors]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear error for this field
        if (errors[field]) {
            setErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors[field];
                return newErrors;
            });
        }

        // Validate field
        validateField(field, value);
    };

    const validateField = (field, value) => {
        const newErrors = { ...errors };

        switch (field) {
            case 'accountName':
                if (!value.trim()) {
                    newErrors.accountName = 'Account name is required';
                } else if (value.trim().length < 2) {
                    newErrors.accountName = 'Account name must be at least 2 characters';
                }
                break;

            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!value.trim()) {
                    newErrors.email = 'Email is required';
                } else if (!emailRegex.test(value)) {
                    newErrors.email = 'Please enter a valid email address';
                }
                break;

            case 'phone':
                if (value && !/^[\d\s\-\+\(\)]+$/.test(value)) {
                    newErrors.phone = 'Please enter a valid phone number';
                }
                break;

            case 'accountNumber':
                if (value && !/^\d+$/.test(value.replace(/\s/g, ''))) {
                    newErrors.accountNumber = 'Account number should contain only digits';
                }
                break;

            default:
                break;
        }

        setErrors(newErrors);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!isFormValid) {
            return;
        }

        const bank = paymentService.getBankById(selectedBank);
        if (!bank) {
            setErrors({ general: 'Please select a valid bank' });
            return;
        }

        const paymentData = {
            ...orderData,
            paymentMethod: 'bank',
            bankCode: selectedBank,
            bankName: bank.name,
            customerInfo: {
                accountName: formData.accountName.trim(),
                email: formData.email.trim(),
                phone: formData.phone.trim(),
                accountNumber: formData.accountNumber.trim()
            },
            fees: bankFees
        };

        try {
            await onPaymentSubmit(paymentData);
        } catch (error) {
            setErrors({ general: error.message || 'Payment submission failed' });
        }
    };

    const formatAmount = (amount) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP'
        }).format(amount);
    };

    return (
        <div className="bank-payment-form">
            <div className="form-header">
                <button 
                    type="button" 
                    className="back-button"
                    onClick={onBack}
                    disabled={isLoading}
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Back to Payment Methods
                </button>
                
                <div className="form-title">
                    <h2>Bank Transfer Payment</h2>
                    <p>Complete your payment using online banking</p>
                </div>
            </div>

            <form onSubmit={handleSubmit} className="payment-form">
                {/* Bank Selection */}
                <div className="form-section">
                    <BankSelection
                        selectedBank={selectedBank}
                        onBankChange={setSelectedBank}
                        fees={bankFees}
                        showFees={true}
                    />
                </div>

                {/* Customer Information */}
                {selectedBank && (
                    <div className="form-section">
                        <h3>Customer Information</h3>
                        <p className="section-description">
                            Please provide your information for the bank transfer
                        </p>

                        <div className="form-grid">
                            <div className="form-group">
                                <label htmlFor="accountName" className="form-label">
                                    Account Holder Name *
                                </label>
                                <input
                                    type="text"
                                    id="accountName"
                                    value={formData.accountName}
                                    onChange={(e) => handleInputChange('accountName', e.target.value)}
                                    className={`form-input ${errors.accountName ? 'error' : ''}`}
                                    placeholder="Enter account holder name"
                                    disabled={isLoading}
                                />
                                {errors.accountName && (
                                    <span className="error-message">{errors.accountName}</span>
                                )}
                            </div>

                            <div className="form-group">
                                <label htmlFor="email" className="form-label">
                                    Email Address *
                                </label>
                                <input
                                    type="email"
                                    id="email"
                                    value={formData.email}
                                    onChange={(e) => handleInputChange('email', e.target.value)}
                                    className={`form-input ${errors.email ? 'error' : ''}`}
                                    placeholder="Enter your email address"
                                    disabled={isLoading}
                                />
                                {errors.email && (
                                    <span className="error-message">{errors.email}</span>
                                )}
                            </div>

                            <div className="form-group">
                                <label htmlFor="phone" className="form-label">
                                    Phone Number (Optional)
                                </label>
                                <input
                                    type="tel"
                                    id="phone"
                                    value={formData.phone}
                                    onChange={(e) => handleInputChange('phone', e.target.value)}
                                    className={`form-input ${errors.phone ? 'error' : ''}`}
                                    placeholder="Enter your phone number"
                                    disabled={isLoading}
                                />
                                {errors.phone && (
                                    <span className="error-message">{errors.phone}</span>
                                )}
                            </div>

                            <div className="form-group">
                                <label htmlFor="accountNumber" className="form-label">
                                    Account Number (Optional)
                                </label>
                                <input
                                    type="text"
                                    id="accountNumber"
                                    value={formData.accountNumber}
                                    onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                                    className={`form-input ${errors.accountNumber ? 'error' : ''}`}
                                    placeholder="Enter your account number"
                                    disabled={isLoading}
                                />
                                {errors.accountNumber && (
                                    <span className="error-message">{errors.accountNumber}</span>
                                )}
                            </div>
                        </div>
                    </div>
                )}

                {/* Payment Summary */}
                {selectedBank && bankFees && (
                    <div className="form-section">
                        <div className="payment-summary">
                            <h3>Payment Summary</h3>
                            
                            <div className="summary-details">
                                <div className="summary-row">
                                    <span>Subtotal:</span>
                                    <span>{formatAmount(bankFees.amount)}</span>
                                </div>
                                <div className="summary-row">
                                    <span>Processing Fee:</span>
                                    <span>{formatAmount(bankFees.totalFee)}</span>
                                </div>
                                <div className="summary-row total">
                                    <span>Total Amount:</span>
                                    <span>{formatAmount(bankFees.totalAmount)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Error Display */}
                {errors.general && (
                    <div className="error-banner">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                            <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
                            <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                        {errors.general}
                    </div>
                )}

                {/* Submit Button */}
                <div className="form-actions">
                    <button
                        type="submit"
                        className={`submit-button ${!isFormValid ? 'disabled' : ''}`}
                        disabled={!isFormValid || isLoading}
                    >
                        {isLoading ? (
                            <>
                                <div className="loading-spinner"></div>
                                Processing...
                            </>
                        ) : (
                            <>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="currentColor"/>
                                    <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                                Proceed to Bank Payment
                            </>
                        )}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default BankPaymentForm;
