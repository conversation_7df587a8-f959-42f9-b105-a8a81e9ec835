import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import './UnauthorizedAccess.css';

/**
 * UnauthorizedAccess Component
 * Displays when user doesn't have permission to access a route
 */
const UnauthorizedAccess = ({ 
    userRole, 
    requiredRoles = [], 
    isAdminArea = false 
}) => {
    const navigate = useNavigate();
    const { logout } = useAuth();

    const handleGoHome = () => {
        navigate('/');
    };

    const handleGoBack = () => {
        navigate(-1);
    };

    const handleLogout = () => {
        logout();
        navigate('/login');
    };

    const getRoleDisplayName = (role) => {
        switch (role) {
            case 'Admin': return 'Administrator';
            case 'Employee': return 'Employee';
            case 'Customer': return 'Customer';
            default: return role;
        }
    };

    return (
        <div className="unauthorized-access">
            <div className="unauthorized-container">
                <div className="unauthorized-icon">
                    <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path 
                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" 
                            fill="#EF4444"
                        />
                        <path 
                            d="M15 9H9v6h6V9z" 
                            fill="white"
                        />
                    </svg>
                </div>
                
                <h1 className="unauthorized-title">Access Denied</h1>
                
                <div className="unauthorized-message">
                    <p>
                        You don't have permission to access this {isAdminArea ? 'admin' : ''} area.
                    </p>
                    <div className="role-info">
                        <p><strong>Your Role:</strong> {getRoleDisplayName(userRole)}</p>
                        <p><strong>Required Role(s):</strong> {requiredRoles.map(getRoleDisplayName).join(', ')}</p>
                    </div>
                </div>

                <div className="unauthorized-actions">
                    <button 
                        className="btn btn-primary" 
                        onClick={handleGoHome}
                    >
                        Go to Homepage
                    </button>
                    <button 
                        className="btn btn-secondary" 
                        onClick={handleGoBack}
                    >
                        Go Back
                    </button>
                    <button 
                        className="btn btn-outline" 
                        onClick={handleLogout}
                    >
                        Switch Account
                    </button>
                </div>

                {isAdminArea && (
                    <div className="admin-contact">
                        <p>
                            Need admin access? Contact your system administrator.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default UnauthorizedAccess;
