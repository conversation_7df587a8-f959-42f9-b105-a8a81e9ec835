// Frontend-Only API Configuration
// This service provides configuration for frontend-only operation

class ApiConfig {
  constructor() {
    // Frontend-only mode - no backend required
    this.frontendOnly = true;
    this.baseURL = null; // No backend URL needed
    this.apiVersion = 'frontend-only';
    this.websocketURL = null; // No WebSocket needed

    // Environment settings
    this.environment = process.env.REACT_APP_ENVIRONMENT || 'development';
    this.debugMode = process.env.REACT_APP_DEBUG_MODE === 'true' || true; // Enable debug by default
    this.logLevel = process.env.REACT_APP_LOG_LEVEL || 'info';
    
    // Feature flags
    this.features = {
      configurator3D: process.env.REACT_APP_ENABLE_3D_CONFIGURATOR === 'true',
      realTimeUpdates: process.env.REACT_APP_ENABLE_REAL_TIME_UPDATES === 'true',
      adminDashboard: process.env.REACT_APP_ENABLE_ADMIN_DASHBOARD === 'true',
      paymentProcessing: process.env.REACT_APP_ENABLE_PAYMENT_PROCESSING === 'true'
    };
    
    // Payment configuration
    this.payment = {
      paymongoPublicKey: process.env.REACT_APP_PAYMONGO_PUBLIC_KEY || ''
    };
    
    // Asset paths
    this.assets = {
      modelsPath: process.env.REACT_APP_MODELS_PATH || '/models',
      imagesPath: process.env.REACT_APP_IMAGES_PATH || '/images',
      assetsURL: process.env.REACT_APP_ASSETS_URL || '/'
    };
    
    // API endpoints
    this.endpoints = {
      auth: '/api/auth',
      products: '/api/products',
      inventory: '/api/inventory',
      orders: '/api/orders',
      suppliers: '/api/suppliers',
      admin: '/api/admin',
      payments: '/api/payments',
      health: '/health'
    };
  }

  // Get full API URL
  getApiUrl(endpoint = '') {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${this.baseURL}${cleanEndpoint}`;
  }

  // Get WebSocket URL
  getWebSocketUrl() {
    return this.websocketURL;
  }

  // Get endpoint URL
  getEndpoint(name) {
    const endpoint = this.endpoints[name];
    if (!endpoint) {
      throw new Error(`Unknown endpoint: ${name}`);
    }
    return this.getApiUrl(endpoint);
  }

  // Check if feature is enabled
  isFeatureEnabled(featureName) {
    return this.features[featureName] || false;
  }

  // Get asset URL
  getAssetUrl(path, type = 'assets') {
    const basePath = this.assets[`${type}Path`] || this.assets.assetsURL;
    const cleanPath = path.startsWith('/') ? path.substring(1) : path;
    return `${basePath}/${cleanPath}`;
  }

  // Get payment configuration
  getPaymentConfig() {
    return this.payment;
  }

  // Log configuration (for debugging)
  logConfig() {
    if (this.debugMode) {
      console.group('🔧 API Configuration');
      console.log('Base URL:', this.baseURL);
      console.log('WebSocket URL:', this.websocketURL);
      console.log('Environment:', this.environment);
      console.log('Features:', this.features);
      console.log('Endpoints:', this.endpoints);
      console.groupEnd();
    }
  }

  // Validate configuration (frontend-only)
  validateConfig() {
    const warnings = [];

    if (this.features.paymentProcessing && !this.payment.paymongoPublicKey) {
      warnings.push('REACT_APP_PAYMONGO_PUBLIC_KEY not set - PayMongo integration will use mock responses');
    }

    if (warnings.length > 0 && this.debugMode) {
      console.warn('⚠️ API Configuration Warnings:', warnings);
    }

    if (this.debugMode) {
      console.log('✅ Frontend-only configuration validated');
    }

    return true;
  }
}

// Create singleton instance
const apiConfig = new ApiConfig();

// Validate configuration on startup
apiConfig.validateConfig();

// Log configuration in development
if (apiConfig.debugMode) {
  apiConfig.logConfig();
}

export default apiConfig;
