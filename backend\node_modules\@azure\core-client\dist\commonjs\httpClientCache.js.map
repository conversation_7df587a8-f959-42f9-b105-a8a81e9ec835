{"version": 3, "file": "httpClientCache.js", "sourceRoot": "", "sources": ["../../src/httpClientCache.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAOlC,gEAMC;AAVD,kEAAoE;AAEpE,IAAI,gBAAwC,CAAC;AAE7C,SAAgB,0BAA0B;IACxC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,gBAAgB,GAAG,IAAA,4CAAuB,GAAE,CAAC;IAC/C,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"@azure/core-rest-pipeline\";\nimport { createDefaultHttpClient } from \"@azure/core-rest-pipeline\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\nexport function getCachedDefaultHttpClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = createDefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n"]}