$connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;User Id=DesignXcel;Password=****************;TrustServerCertificate=True;"

$sqlStatements = @(
    "CREATE TABLE ProductMaterials (ProductMaterialID INT PRIMARY KEY IDENTITY, ProductID INT FOREIGN KEY REFERENCES Products(ProductID), MaterialID INT FOREIGN KEY REFERENCES RawMaterials(MaterialID), QuantityRequired INT);",
    "ALTER TABLE Products ADD Dimensions NVARCHAR(50);",
    "CREATE TABLE Customers (CustomerID INT PRIMARY KEY IDENTITY, FullName NVARCHAR(100), Email NVARCHAR(100) UNIQUE, PhoneNumber NVARCHAR(20), PasswordHash NVARCHAR(255), CreatedAt DATETIME DEFAULT GETDATE(), IsActive BIT DEFAULT 1);",
    "CREATE TABLE CustomerAddresses (AddressID INT PRIMARY KEY IDENTITY, CustomerID INT FOREIGN KEY REFERENCES Customers(CustomerID), Label NVARCHAR(50), HouseNumber NVARCHAR(50), Street NVARCHAR(100), Barangay NVARCHAR(100), City NVARCHAR(100), Province NVARCHAR(100), Region NVARCHAR(100), PostalCode NVARCHAR(20), Country NVARCHAR(100) DEFAULT 'Philippines', IsDefault BIT DEFAULT 0, CreatedAt DATETIME DEFAULT GETDATE());",
    "CREATE TABLE ActivityLogs (LogID INT PRIMARY KEY IDENTITY, UserID INT FOREIGN KEY REFERENCES Users(UserID), Action NVARCHAR(50), TableAffected NVARCHAR(100), RecordID INT, Description NVARCHAR(MAX), Timestamp DATETIME DEFAULT GETDATE());",
    "CREATE TABLE ProductStockAdjustments (AdjustmentID INT PRIMARY KEY IDENTITY, ProductID INT FOREIGN KEY REFERENCES Products(ProductID), QuantityAdded INT, Reason NVARCHAR(255), PerformedBy INT FOREIGN KEY REFERENCES Users(UserID), Timestamp DATETIME DEFAULT GETDATE());",
    "CREATE TABLE UserPermissions (PermissionID INT PRIMARY KEY IDENTITY, UserID INT FOREIGN KEY REFERENCES Users(UserID), Section NVARCHAR(50), CanAccess BIT DEFAULT 0);",
    "ALTER TABLE UserPermissions ADD CONSTRAINT UQ_UserSection UNIQUE (UserID, Section);",
    "CREATE TABLE Orders (OrderID INT PRIMARY KEY IDENTITY, CustomerID INT FOREIGN KEY REFERENCES Customers(CustomerID), OrderDate DATETIME DEFAULT GETDATE(), Status NVARCHAR(50), TotalAmount DECIMAL(10,2), ShippingAddressID INT FOREIGN KEY REFERENCES CustomerAddresses(AddressID));",
    "CREATE TABLE OrderItems (OrderItemID INT PRIMARY KEY IDENTITY, OrderID INT FOREIGN KEY REFERENCES Orders(OrderID), ProductID INT FOREIGN KEY REFERENCES Products(ProductID), Quantity INT, PriceAtPurchase DECIMAL(10,2));"
)

$connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
$connection.Open()

$successCount = 0
$errorCount = 0

foreach ($sql in $sqlStatements) {
    try {
        $command = $connection.CreateCommand()
        $command.CommandText = $sql
        $command.ExecuteNonQuery()
        Write-Host "✓ SQL executed successfully" -ForegroundColor Green
        $successCount++
    } catch {
        Write-Host "✗ Error: $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

$connection.Close()
Write-Host "Batch 1 completed. Success: $successCount, Errors: $errorCount" -ForegroundColor Cyan
