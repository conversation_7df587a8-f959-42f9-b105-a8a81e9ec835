import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';

const SearchSuggestions = ({ 
    suggestions, 
    selectedIndex, 
    onSelect, 
    onClose, 
    formatPrice, 
    isLoading 
}) => {
    const { t } = useLanguage();

    if (isLoading) {
        return (
            <div className="search-suggestions">
                <div className="suggestions-loading">
                    <div className="loading-spinner"></div>
                    <span>{t('loading')}...</span>
                </div>
            </div>
        );
    }

    if (suggestions.length === 0) {
        return (
            <div className="search-suggestions">
                <div className="no-suggestions">
                    <span>{t('noProductsFound')}</span>
                </div>
            </div>
        );
    }

    return (
        <div className="search-suggestions">
            
            <div className="suggestions-list">
                {suggestions.map((product, index) => (
                    <div
                        key={product.id}
                        className={`suggestion-item ${index === selectedIndex ? 'selected' : ''}`}
                        onClick={() => onSelect(product)}
                        onMouseEnter={() => {
                            // Optional: Update selected index on hover
                        }}
                    >
                        <div className="suggestion-image">
                            <img
                                src={product.images?.[0] || '/placeholder-image.jpg'}
                                alt={product.name}
                                loading="lazy"
                            />
                        </div>
                        
                        <div className="suggestion-content">
                            <h4 className="suggestion-name">{product.name}</h4>
                            <div className="suggestion-category">{product.categoryName}</div>

                            <div className="suggestion-pricing">
                                {product.discountPrice && product.discountPrice < product.price ? (
                                    <>
                                        <span className="suggestion-price current">
                                            {formatPrice(product.discountPrice)}
                                        </span>
                                        <span className="suggestion-price original">
                                            {formatPrice(product.price)}
                                        </span>
                                    </>
                                ) : (
                                    <span className="suggestion-price current">
                                        {formatPrice(product.price)}
                                    </span>
                                )}
                            </div>
                        </div>
                        
                        <div className="suggestion-arrow">
                            <svg
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M9 18L15 12L9 6"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </div>
                    </div>
                ))}
            </div>
            
            <div className="suggestions-footer">
                <div className="keyboard-hint">
                    <span>Use ↑↓ to navigate, Enter to select, Esc to close</span>
                </div>
            </div>
        </div>
    );
};

export default SearchSuggestions;
