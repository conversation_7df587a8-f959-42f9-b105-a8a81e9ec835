import React from 'react';
import paymentService from '../../services/paymentService';
import './PaymentMethods.css';

const PaymentMethods = ({ selectedMethod, onMethodChange, fees }) => {
    const paymentMethods = [
        {
            id: 'card',
            name: 'Credit/Debit Card',
            description: 'Visa, Mastercard, American Express',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="2" y="4" width="20" height="16" rx="3" fill="#F0B21B"/>
                    <rect x="2" y="8" width="20" height="2" fill="white"/>
                    <rect x="4" y="12" width="4" height="1" fill="white"/>
                    <rect x="4" y="14" width="6" height="1" fill="white"/>
                </svg>
            ),
            features: ['Instant processing', '3D Secure protection', 'Save for future use'],
            popular: true
        },
        {
            id: 'gcash',
            name: 'GCash',
            description: 'Pay with your GCash wallet',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="3" width="18" height="18" rx="4" fill="#007DFF"/>
                    <path d="M8 12L10 14L16 8" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <circle cx="12" cy="12" r="2" fill="white" fillOpacity="0.3"/>
                </svg>
            ),
            features: ['Mobile wallet', 'QR code payment', 'Instant confirmation']
        },
        {
            id: 'grabpay',
            name: 'GrabPay',
            description: 'Pay with your GrabPay wallet',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="3" width="18" height="18" rx="4" fill="#00B14F"/>
                    <path d="M7 12L12 17L17 7" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            features: ['Digital wallet', 'Secure payments', 'Loyalty rewards']
        },
        {
            id: 'paymaya',
            name: 'PayMaya',
            description: 'Pay with your PayMaya account',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="3" width="18" height="18" rx="4" fill="#FF6B35"/>
                    <circle cx="12" cy="12" r="6" stroke="white" strokeWidth="2"/>
                    <circle cx="12" cy="12" r="2" fill="white"/>
                </svg>
            ),
            features: ['Virtual payments', 'Bank integration', 'Cashback rewards']
        },
        {
            id: 'bank',
            name: 'Online Banking',
            description: 'Direct bank transfer from your bank account',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 21H21M5 21V7L12 3L19 7V21M9 9H15M9 13H15M9 17H15" stroke="#2c3e50" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            features: ['Bank-level security', 'No card required', 'Direct transfer', 'Lower fees'],
            recommended: true,
            processingTime: '1-3 business days',
            supportedBanks: ['BPI', 'BDO', 'Metrobank', 'UnionBank', 'LandBank', 'PNB', 'RCBC', 'Security Bank']
        }
    ];

    const formatFee = (method) => {
        if (!fees || fees.paymentMethod !== method) return null;

        if (fees.totalFee === 0) {
            return <span className="fee-text free">No fees</span>;
        }

        return (
            <span className="fee-text">
                +{paymentService.formatAmount(fees.totalFee)} fee
            </span>
        );
    };

    const getBankFeeRange = () => {
        // Get fee range for bank transfers
        const banks = paymentService.getSupportedBanks();
        const feeRanges = banks.map(bank => {
            const bankFees = paymentService.getBankTransferFees(bank.id);
            return bankFees.percentage * 100;
        });

        const minFee = Math.min(...feeRanges);
        const maxFee = Math.max(...feeRanges);

        if (minFee === maxFee) {
            return `${minFee.toFixed(1)}%`;
        }
        return `${minFee.toFixed(1)}% - ${maxFee.toFixed(1)}%`;
    };

    return (
        <div className="payment-methods-section">
            <h3>Choose Payment Method</h3>
            <p className="section-description">
                Select your preferred payment method. All transactions are secured with industry-standard encryption.
            </p>

            <div className="payment-methods-grid">
                {paymentMethods.map((method) => (
                    <div
                        key={method.id}
                        className={`payment-method-card ${selectedMethod === method.id ? 'selected' : ''}`}
                        onClick={() => onMethodChange(method.id)}
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                onMethodChange(method.id);
                            }
                        }}
                        aria-label={`Select ${method.name} payment method`}
                    >
                        {method.popular && (
                            <div className="popular-badge">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
                                </svg>
                                Popular
                            </div>
                        )}

                        <div className="method-header">
                            <div className="method-icon">
                                {method.icon}
                            </div>
                            <div className="method-info">
                                <h4 className="method-name">
                                    {method.name}
                                    {method.recommended && <span className="recommended-badge">Recommended</span>}
                                </h4>
                                <p className="method-description">{method.description}</p>
                                {method.processingTime && (
                                    <p className="processing-time">
                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="10" stroke="#666" strokeWidth="2"/>
                                            <path d="M12 6V12L16 14" stroke="#666" strokeWidth="2" strokeLinecap="round"/>
                                        </svg>
                                        Processing: {method.processingTime}
                                    </p>
                                )}
                                {method.id === 'bank' ? (
                                    <span className="fee-text bank-fee">
                                        {getBankFeeRange()} fee
                                    </span>
                                ) : (
                                    formatFee(method.id)
                                )}
                            </div>
                            <div className="method-selector">
                                <div className={`radio-button ${selectedMethod === method.id ? 'checked' : ''}`}>
                                    {selectedMethod === method.id && (
                                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="8" fill="currentColor"/>
                                        </svg>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="method-features">
                            {method.features.map((feature, index) => (
                                <div key={index} className="feature-item">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 12L11 14L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                    <span>{feature}</span>
                                </div>
                            ))}
                        </div>

                        {/* Bank-specific information */}
                        {method.id === 'bank' && method.supportedBanks && (
                            <div className="supported-banks">
                                <h5 className="banks-title">Supported Banks:</h5>
                                <div className="banks-list">
                                    {method.supportedBanks.slice(0, 4).map((bank, index) => (
                                        <span key={index} className="bank-tag">{bank}</span>
                                    ))}
                                    {method.supportedBanks.length > 4 && (
                                        <span className="bank-tag more">+{method.supportedBanks.length - 4} more</span>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                ))}
            </div>

            {/* Payment Security Notice */}
            <div className="payment-security-notice">
                <div className="security-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#10B981"/>
                        <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </div>
                <div className="security-text">
                    <h4>Your payment is secure</h4>
                    <p>All transactions are encrypted and processed through PayMongo's secure payment gateway, compliant with PCI DSS standards.</p>
                </div>
            </div>

            {/* Supported Cards */}
            {selectedMethod === 'card' && (
                <div className="supported-cards">
                    <h4>Supported Cards</h4>
                    <div className="card-brands">
                        <div className="card-brand">
                            <svg width="40" height="24" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="24" rx="4" fill="#1A1F71"/>
                                <text x="20" y="15" textAnchor="middle" fill="white" fontSize="8" fontWeight="bold">VISA</text>
                            </svg>
                        </div>
                        <div className="card-brand">
                            <svg width="40" height="24" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="24" rx="4" fill="#EB001B"/>
                                <circle cx="15" cy="12" r="8" fill="#EB001B"/>
                                <circle cx="25" cy="12" r="8" fill="#FF5F00"/>
                            </svg>
                        </div>
                        <div className="card-brand">
                            <svg width="40" height="24" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="24" rx="4" fill="#006FCF"/>
                                <text x="20" y="15" textAnchor="middle" fill="white" fontSize="6" fontWeight="bold">AMEX</text>
                            </svg>
                        </div>
                        <div className="card-brand">
                            <svg width="40" height="24" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="24" rx="4" fill="#FF6000"/>
                                <text x="20" y="15" textAnchor="middle" fill="white" fontSize="5" fontWeight="bold">DISCOVER</text>
                            </svg>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default PaymentMethods;
