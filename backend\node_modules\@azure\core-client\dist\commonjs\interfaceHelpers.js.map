{"version": 3, "file": "interfaceHelpers.js", "sourceRoot": "", "sources": ["../../src/interfaceHelpers.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AASlC,0EAYC;AAQD,gEAWC;AArCD,mDAAkD;AAElD;;;GAGG;AACH,SAAgB,+BAA+B,CAAC,aAA4B;IAC1E,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;IACjC,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;QACjD,MAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC9D,IACE,iBAAiB,CAAC,UAAU;YAC5B,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,+BAAe,CAAC,MAAM,EACjE,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,SAAgB,0BAA0B,CAAC,SAA6B;IACtE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;IAC5C,IAAI,MAAc,CAAC;IACnB,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;QACtC,MAAM,GAAG,aAAa,CAAC;IACzB,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;QACxC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,MAAM,CAAC,cAAe,CAAC;IAClC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationParameter, OperationSpec } from \"./interfaces.js\";\nimport { MapperTypeNames } from \"./serializer.js\";\n\n/**\n * Gets the list of status codes for streaming responses.\n * @internal\n */\nexport function getStreamingResponseStatusCodes(operationSpec: OperationSpec): Set<number> {\n  const result = new Set<number>();\n  for (const statusCode in operationSpec.responses) {\n    const operationResponse = operationSpec.responses[statusCode];\n    if (\n      operationResponse.bodyMapper &&\n      operationResponse.bodyMapper.type.name === MapperTypeNames.Stream\n    ) {\n      result.add(Number(statusCode));\n    }\n  }\n  return result;\n}\n\n/**\n * Get the path to this parameter's value as a dotted string (a.b.c).\n * @param parameter - The parameter to get the path string for.\n * @returns The path to this parameter's value as a dotted string.\n * @internal\n */\nexport function getPathStringFromParameter(parameter: OperationParameter): string {\n  const { parameterPath, mapper } = parameter;\n  let result: string;\n  if (typeof parameterPath === \"string\") {\n    result = parameterPath;\n  } else if (Array.isArray(parameterPath)) {\n    result = parameterPath.join(\".\");\n  } else {\n    result = mapper.serializedName!;\n  }\n  return result;\n}\n"]}