import React, { useState, useEffect } from 'react';

const SupplierManagement = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data for now
    setTimeout(() => {
      setSuppliers([
        {
          id: '1',
          name: 'Office Furniture Co.',
          contact: 'John Manager',
          email: '<EMAIL>',
          phone: '+63 ************',
          rating: 4.5,
          status: 'Active'
        },
        {
          id: '2',
          name: 'Desk Solutions Inc.',
          contact: '<PERSON>',
          email: '<EMAIL>',
          phone: '+63 ************',
          rating: 4.2,
          status: 'Active'
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="admin-loading">
        <div className="loading-spinner"></div>
        <p>Loading suppliers...</p>
      </div>
    );
  }

  return (
    <div className="supplier-management">
      <div className="admin-card-header">
        <h1 className="admin-card-title">Supplier Management</h1>
        <button className="admin-btn admin-btn-primary">Add New Supplier</button>
      </div>

      <div className="admin-card">
        <div className="table-container">
          <table className="admin-table">
            <thead>
              <tr>
                <th>Supplier Name</th>
                <th>Contact Person</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Rating</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {suppliers.map(supplier => (
                <tr key={supplier.id}>
                  <td>{supplier.name}</td>
                  <td>{supplier.contact}</td>
                  <td>{supplier.email}</td>
                  <td>{supplier.phone}</td>
                  <td>⭐ {supplier.rating}</td>
                  <td>
                    <span className="status-badge" style={{ backgroundColor: '#27ae60' }}>
                      {supplier.status}
                    </span>
                  </td>
                  <td>
                    <button className="admin-btn admin-btn-secondary btn-small">Edit</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SupplierManagement;
