import React, { useState } from 'react';
import paymentService from '../../services/paymentService';

const OrderSummary = ({ orderData, fees, paymentMethod }) => {
    const [showDetails, setShowDetails] = useState(false);

    if (!orderData) return null;

    const subtotal = orderData.subtotal || 0;
    const tax = orderData.tax || 0;
    const shipping = orderData.shipping || 0;
    const paymentFee = fees?.totalFee || 0;
    const total = fees?.totalAmount || orderData.total || 0;

    return (
        <div className="order-summary">
            <div className="summary-header">
                <h3>Order Summary</h3>
                <button
                    type="button"
                    className="toggle-details"
                    onClick={() => setShowDetails(!showDetails)}
                    aria-expanded={showDetails}
                    aria-label={showDetails ? 'Hide order details' : 'Show order details'}
                >
                    {showDetails ? (
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 15L12 9L6 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    ) : (
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    )}
                </button>
            </div>

            {/* Order Items */}
            {showDetails && orderData.items && (
                <div className="order-items">
                    <h4>Items ({orderData.items.length})</h4>
                    <div className="items-list">
                        {orderData.items.map((item, index) => (
                            <div key={index} className="order-item">
                                <div className="item-image">
                                    {item.image ? (
                                        <img src={item.image} alt={item.name} />
                                    ) : (
                                        <div className="item-placeholder">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" fill="none"/>
                                                <circle cx="8.5" cy="8.5" r="1.5" fill="currentColor"/>
                                                <path d="M21 15L16 10L5 21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                        </div>
                                    )}
                                </div>
                                <div className="item-details">
                                    <h5 className="item-name">{item.name}</h5>
                                    <p className="item-quantity">Qty: {item.quantity}</p>
                                    {item.variant && (
                                        <p className="item-variant">{item.variant}</p>
                                    )}
                                </div>
                                <div className="item-price">
                                    {paymentService.formatAmount(item.price * item.quantity)}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Price Breakdown */}
            <div className="price-breakdown">
                <div className="price-row">
                    <span>Subtotal</span>
                    <span>{paymentService.formatAmount(subtotal)}</span>
                </div>
                
                {shipping > 0 && (
                    <div className="price-row">
                        <span>Shipping</span>
                        <span>{paymentService.formatAmount(shipping)}</span>
                    </div>
                )}
                
                {tax > 0 && (
                    <div className="price-row">
                        <span>Tax</span>
                        <span>{paymentService.formatAmount(tax)}</span>
                    </div>
                )}
                
                {paymentFee > 0 && (
                    <div className="price-row fee-row">
                        <span>
                            Payment Fee ({paymentService.getPaymentMethodDisplayName(paymentMethod)})
                            <span className="fee-info" title="Processing fee charged by payment provider">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                                    <path d="M9.09 9A3 3 0 0 1 12 6C13.66 6 15 7.34 15 9C15 10.66 13.66 12 12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                    <circle cx="12" cy="16" r="1" fill="currentColor"/>
                                </svg>
                            </span>
                        </span>
                        <span>{paymentService.formatAmount(paymentFee)}</span>
                    </div>
                )}
                
                <div className="price-row total-row">
                    <span>Total</span>
                    <span className="total-amount">{paymentService.formatAmount(total)}</span>
                </div>
            </div>

            {/* Payment Method Display */}
            {paymentMethod && (
                <div className="payment-method-display">
                    <h4>Payment Method</h4>
                    <div className="selected-method">
                        <span className="method-icon">
                            {paymentService.getPaymentMethodIcon(paymentMethod)}
                        </span>
                        <span className="method-name">
                            {paymentService.getPaymentMethodDisplayName(paymentMethod)}
                        </span>
                    </div>
                </div>
            )}

            {/* Savings Display */}
            {shipping === 0 && subtotal > 0 && (
                <div className="savings-notice">
                    <div className="savings-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="#10B981"/>
                        </svg>
                    </div>
                    <span>Free shipping applied!</span>
                </div>
            )}

            {/* Estimated Delivery */}
            <div className="delivery-info">
                <h4>Estimated Delivery</h4>
                <div className="delivery-details">
                    <div className="delivery-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M16 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V8L16 3Z" stroke="currentColor" strokeWidth="2" fill="none"/>
                            <path d="M16 3V8H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            <path d="M7 13H17M7 17H13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                        </svg>
                    </div>
                    <div className="delivery-text">
                        <p className="delivery-date">3-5 business days</p>
                        <p className="delivery-note">After payment confirmation</p>
                    </div>
                </div>
            </div>

            {/* Order ID */}
            {orderData.orderId && (
                <div className="order-id">
                    <span className="order-id-label">Order ID:</span>
                    <span className="order-id-value">{orderData.orderId}</span>
                </div>
            )}
        </div>
    );
};

export default OrderSummary;
