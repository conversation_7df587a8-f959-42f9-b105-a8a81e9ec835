import React, { useState } from 'react';
import ThreeJSPreview from '../components/ThreeJSPreview';
import './ProductDetailsModal.css';

const ProductDetailsModal = ({ product, onClose, onEdit }) => {
  const [activeTab, setActiveTab] = useState('details');

  if (!product) return null;

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case 'Active': return '#27ae60';
      case 'Draft': return '#f39c12';
      case 'Inactive': return '#95a5a6';
      case 'Discontinued': return '#e74c3c';
      case 'Pending Review': return '#3498db';
      default: return '#95a5a6';
    }
  };

  const tabs = [
    { id: 'details', label: 'Product Details', icon: '📝' },
    { id: 'files', label: 'Files & Media', icon: '📁' },
    { id: 'components', label: 'Components', icon: '🔧' },
    { id: 'audit', label: 'Audit Trail', icon: '📊' }
  ];

  return (
    <div className="modal-overlay">
      <div className="product-details-modal">
        <div className="modal-header">
          <div className="header-content">
            <h2>{product.product?.ProductName || 'Product Details'}</h2>
            <span 
              className="status-badge" 
              style={{ backgroundColor: getStatusBadgeColor(product.product?.Status) }}
            >
              {product.product?.Status}
            </span>
          </div>
          <div className="header-actions">
            <button className="btn btn-secondary" onClick={onEdit}>
              ✏️ Edit
            </button>
            <button className="modal-close" onClick={onClose}>×</button>
          </div>
        </div>

        <div className="modal-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="tab-icon">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        <div className="modal-body">
          {activeTab === 'details' && (
            <div className="tab-content">
              <div className="details-grid">
                <div className="detail-section">
                  <h3>Basic Information</h3>
                  <div className="detail-group">
                    <div className="detail-item">
                      <label>Product Code:</label>
                      <span>{product.product?.ProductCode || 'N/A'}</span>
                    </div>
                    <div className="detail-item">
                      <label>Product Name:</label>
                      <span>{product.product?.ProductName || 'N/A'}</span>
                    </div>
                    <div className="detail-item">
                      <label>Category:</label>
                      <span>{product.product?.CategoryName || 'N/A'}</span>
                    </div>
                    <div className="detail-item">
                      <label>Base Price:</label>
                      <span>{product.product?.BasePrice ? formatCurrency(product.product.BasePrice) : 'N/A'}</span>
                    </div>
                  </div>
                </div>

                <div className="detail-section">
                  <h3>Physical Properties</h3>
                  <div className="detail-group">
                    <div className="detail-item">
                      <label>Weight:</label>
                      <span>{product.product?.Weight ? `${product.product.Weight} kg` : 'N/A'}</span>
                    </div>
                    <div className="detail-item">
                      <label>Dimensions:</label>
                      <span>{product.product?.Dimensions || 'N/A'}</span>
                    </div>
                    <div className="detail-item">
                      <label>Material:</label>
                      <span>{product.product?.Material || 'N/A'}</span>
                    </div>
                    <div className="detail-item">
                      <label>Color:</label>
                      <span>{product.product?.Color || 'N/A'}</span>
                    </div>
                  </div>
                </div>

                <div className="detail-section full-width">
                  <h3>Description</h3>
                  <div className="description-content">
                    {product.product?.Description || 'No description available.'}
                  </div>
                </div>

                <div className="detail-section">
                  <h3>Settings</h3>
                  <div className="detail-group">
                    <div className="detail-item">
                      <label>Customizable:</label>
                      <span className={`boolean-badge ${product.product?.IsCustomizable ? 'true' : 'false'}`}>
                        {product.product?.IsCustomizable ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="detail-item">
                      <label>Active:</label>
                      <span className={`boolean-badge ${product.product?.IsActive ? 'true' : 'false'}`}>
                        {product.product?.IsActive ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="detail-section">
                  <h3>Timestamps</h3>
                  <div className="detail-group">
                    <div className="detail-item">
                      <label>Created:</label>
                      <span>{formatDate(product.product?.CreatedAt)}</span>
                    </div>
                    <div className="detail-item">
                      <label>Updated:</label>
                      <span>{formatDate(product.product?.UpdatedAt)}</span>
                    </div>
                    <div className="detail-item">
                      <label>Created By:</label>
                      <span>{product.product?.CreatedBy || 'N/A'}</span>
                    </div>
                    <div className="detail-item">
                      <label>Updated By:</label>
                      <span>{product.product?.UpdatedBy || 'N/A'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'files' && (
            <div className="tab-content">
              <div className="files-section">
                <div className="file-category">
                  <h3>3D Models ({product.models?.length || 0})</h3>
                  {product.models && product.models.length > 0 ? (
                    <div className="files-grid">
                      {product.models.map((model, index) => (
                        <div key={index} className="file-card">
                          <div className="file-icon">🎯</div>
                          <div className="file-info">
                            <div className="file-name">{model.FileName}</div>
                            <div className="file-details">
                              <span>{(model.FileSize / 1024 / 1024).toFixed(2)} MB</span>
                              <span>{model.IsPrimary ? 'Primary' : 'Secondary'}</span>
                            </div>
                          </div>
                          <div className="file-actions">
                            <button className="btn-icon" title="Download">⬇️</button>
                            <button className="btn-icon" title="Preview">👁️</button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="no-files">No 3D models uploaded</div>
                  )}
                </div>

                <div className="file-category">
                  <h3>Images ({product.images?.length || 0})</h3>
                  {product.images && product.images.length > 0 ? (
                    <div className="images-grid">
                      {product.images.map((image, index) => (
                        <div key={index} className="image-card">
                          <div className="image-preview">
                            <img 
                              src={`/api/files/images/${image.FileName}`} 
                              alt={image.AltText || 'Product image'}
                              onError={(e) => {
                                e.target.src = '/placeholder-image.png';
                              }}
                            />
                          </div>
                          <div className="image-info">
                            <div className="image-name">{image.FileName}</div>
                            <div className="image-details">
                              <span>{(image.FileSize / 1024).toFixed(1)} KB</span>
                              <span>{image.IsPrimary ? 'Primary' : 'Secondary'}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="no-files">No images uploaded</div>
                  )}
                </div>

                {/* 3D Preview Section */}
                {product.models && product.models.length > 0 && (
                  <div className="file-category">
                    <h3>3D Preview</h3>
                    <div className="preview-container">
                      <ThreeJSPreview 
                        modelUrl={`/api/files/models/${product.models[0].FileName}`}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'components' && (
            <div className="tab-content">
              <div className="components-section">
                <h3>Product Components ({product.components?.length || 0})</h3>
                {product.components && product.components.length > 0 ? (
                  <div className="components-table">
                    <table>
                      <thead>
                        <tr>
                          <th>Component Name</th>
                          <th>Quantity</th>
                          <th>Unit Cost</th>
                          <th>Total Cost</th>
                          <th>Supplier</th>
                        </tr>
                      </thead>
                      <tbody>
                        {product.components.map((component, index) => (
                          <tr key={index}>
                            <td>{component.ComponentName}</td>
                            <td>{component.Quantity}</td>
                            <td>{formatCurrency(component.UnitCost)}</td>
                            <td>{formatCurrency(component.Quantity * component.UnitCost)}</td>
                            <td>{component.Supplier || 'N/A'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="no-data">No components defined for this product</div>
                )}

                <div className="colors-section">
                  <h3>Available Colors ({product.colors?.length || 0})</h3>
                  {product.colors && product.colors.length > 0 ? (
                    <div className="colors-grid">
                      {product.colors.map((color, index) => (
                        <div key={index} className="color-card">
                          <div 
                            className="color-swatch" 
                            style={{ backgroundColor: color.HexCode }}
                          ></div>
                          <div className="color-info">
                            <div className="color-name">{color.ColorName}</div>
                            <div className="color-code">{color.HexCode}</div>
                            {color.PriceModifier !== 0 && (
                              <div className="price-modifier">
                                {color.PriceModifier > 0 ? '+' : ''}{formatCurrency(color.PriceModifier)}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="no-data">No color options defined</div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'audit' && (
            <div className="tab-content">
              <div className="audit-section">
                <h3>Audit Trail</h3>
                {product.auditTrail && product.auditTrail.length > 0 ? (
                  <div className="audit-timeline">
                    {product.auditTrail.map((entry, index) => (
                      <div key={index} className="audit-entry">
                        <div className="audit-timestamp">
                          {formatDate(entry.ChangeDate)}
                        </div>
                        <div className="audit-content">
                          <div className="audit-action">{entry.Action}</div>
                          <div className="audit-user">by {entry.ChangedBy}</div>
                          {entry.ChangeDetails && (
                            <div className="audit-details">{entry.ChangeDetails}</div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="no-data">No audit trail available</div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            Close
          </button>
          <button className="btn btn-primary" onClick={onEdit}>
            Edit Product
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsModal;
