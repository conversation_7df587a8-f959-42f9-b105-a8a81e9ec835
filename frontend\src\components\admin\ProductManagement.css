/* Product Management Styles */
.product-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Filters Section */
.product-filters {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 24px;
}

.filter-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.admin-input,
.admin-select {
  padding: 10px 12px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #ffffff;
}

.admin-input:focus,
.admin-select:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  background: #ffffff;
}

.admin-table th {
  background: #f8f9fa;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e1e8ed;
  font-size: 14px;
}

.admin-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.admin-table th.sortable:hover {
  background: #e9ecef;
}

.admin-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #e1e8ed;
  font-size: 14px;
  vertical-align: middle;
}

.admin-table tr:hover {
  background: #f8f9fa;
}

/* Product Info */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-info strong {
  color: #2c3e50;
  font-weight: 600;
}

.product-info small {
  color: #7f8c8d;
  font-size: 12px;
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* File Counts */
.file-counts {
  display: flex;
  gap: 8px;
}

.file-count {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #6c757d;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 6px;
}

.btn-small {
  padding: 6px 10px !important;
  font-size: 12px !important;
  min-width: auto !important;
}

.admin-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  min-width: 100px;
  justify-content: center;
}

.admin-btn-primary {
  background: #F0B21B;
  color: white;
}

.admin-btn-primary:hover {
  background: #d4a017;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.admin-btn-secondary {
  background: #6c757d;
  color: white;
}

.admin-btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.admin-btn-danger {
  background: #dc3545;
  color: white;
}

.admin-btn-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.admin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-icon {
  font-size: 16px;
  font-weight: bold;
}

/* No Data State */
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-style: italic;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-top: 1px solid #e1e8ed;
}

.pagination-info {
  font-size: 14px;
  color: #6c757d;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-info {
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

/* Loading State */
.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e1e8ed;
  border-top: 4px solid #F0B21B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .filter-row {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .product-management {
    padding: 16px;
  }

  .filter-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .admin-card-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .admin-btn {
    width: 100%;
  }

  .table-container {
    font-size: 12px;
  }

  .admin-table th,
  .admin-table td {
    padding: 8px 6px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .pagination-controls {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .btn-small {
    padding: 4px 6px !important;
    font-size: 10px !important;
  }

  .file-counts {
    flex-direction: column;
    gap: 2px;
  }

  .product-info strong {
    font-size: 13px;
  }

  .product-info small {
    font-size: 11px;
  }
}
