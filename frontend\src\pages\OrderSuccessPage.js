import React, { useEffect, useState } from 'react';
import { useParams, useLocation, Link } from 'react-router-dom';
import paymentService from '../services/paymentService';
import './OrderSuccessPage.css';

const OrderSuccessPage = () => {
    const { orderId } = useParams();
    const location = useLocation();
    const { order, message, paymentStatus, paymentMethod, paymentReference, estimatedDelivery } = location.state || {};
    const [paymentDetails, setPaymentDetails] = useState(null);
    const [orderTrackingInfo, setOrderTrackingInfo] = useState(null);

    useEffect(() => {
        // Track successful order completion
        if (order) {
            console.log('Order completed successfully:', {
                orderId: order.id || orderId,
                orderNumber: order.order_number,
                amount: order.total_amount,
                paymentStatus,
                paymentMethod,
                customerEmail: order.customer_email
            });

            // Set payment details for display
            if (paymentStatus && paymentMethod) {
                setPaymentDetails({
                    status: paymentStatus,
                    method: paymentMethod,
                    reference: paymentReference,
                    completedAt: new Date().toISOString()
                });
            }

            // Set order tracking information
            setOrderTrackingInfo({
                orderNumber: order.order_number,
                estimatedDelivery: estimatedDelivery || '3-5 business days',
                trackingEmail: order.customer_email,
                orderDate: order.created_at
            });
        }
    }, [order, orderId, paymentStatus, paymentMethod, paymentReference, estimatedDelivery]);

    const formatPrice = (price) => {
        return paymentService.formatAmount(price, order?.currency || 'PHP');
    };

    const getPaymentMethodIcon = () => {
        if (paymentMethod === 'PayMongo') {
            return '💳';
        }
        return paymentService.getPaymentMethodIcon(paymentMethod || 'card');
    };

    const getPaymentMethodDisplay = () => {
        if (paymentMethod === 'PayMongo') {
            return 'PayMongo Payment Gateway';
        }
        return paymentService.getPaymentMethodDisplayName(paymentMethod || 'card');
    };

    const getPaymentStatusDisplay = () => {
        const statusMap = {
            'completed': { text: 'Payment Successful', color: '#059669', icon: '✅' },
            'pending': { text: 'Payment Pending', color: '#F0B21B', icon: '⏳' },
            'failed': { text: 'Payment Failed', color: '#DC2626', icon: '❌' },
            'cancelled': { text: 'Payment Cancelled', color: '#6B7280', icon: '🚫' }
        };
        return statusMap[paymentStatus] || statusMap['completed'];
    };



    if (!order) {
        return (
            <div className="order-success-page">
                <div className="success-container">
                    <div className="error-state">
                        <h2>Order Not Found</h2>
                        <p>We couldn't find the details for this order.</p>
                        <Link to="/products" className="btn btn-primary">
                            Continue Shopping
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="order-success-page">
            <div className="success-container">
                {/* Success Header */}
                <div className="success-header">
                    <div className="success-icon">
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="32" cy="32" r="30" fill="#059669" stroke="#047857" strokeWidth="2"/>
                            <path d="M20 32L28 40L44 24" stroke="white" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    </div>
                    <h1 className="success-title">Order Confirmed!</h1>
                    <p className="success-subtitle">
                        {message || 'Thank you for your order. We will process your order shortly.'}
                    </p>
                </div>

                {/* Order Details */}
                <div className="order-details">
                    <div className="detail-section">
                        <h3>Order Information</h3>
                        <div className="detail-grid">
                            <div className="detail-item">
                                <span className="detail-label">Order Number</span>
                                <span className="detail-value">{order.order_number}</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">Order Date</span>
                                <span className="detail-value">
                                    {new Date().toLocaleDateString('en-PH', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric'
                                    })}
                                </span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">Total Amount</span>
                                <span className="detail-value total-amount">{formatPrice(order.total_amount)}</span>
                            </div>
                            <div className="detail-item">
                                <span className="detail-label">Payment Method</span>
                                <span className="detail-value payment-method">
                                    <span className="payment-icon">{getPaymentMethodIcon()}</span>
                                    {getPaymentMethodDisplay()}
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* PayMongo Payment Details */}
                    {paymentDetails && (
                        <div className="detail-section payment-section">
                            <h3>Payment Details</h3>
                            <div className="payment-status-card">
                                <div className="payment-status-header">
                                    <span className="payment-status-icon">
                                        {getPaymentStatusDisplay().icon}
                                    </span>
                                    <span
                                        className="payment-status-text"
                                        style={{ color: getPaymentStatusDisplay().color }}
                                    >
                                        {getPaymentStatusDisplay().text}
                                    </span>
                                </div>
                                <div className="payment-details-grid">
                                    <div className="payment-detail-item">
                                        <span className="payment-detail-label">Payment Method</span>
                                        <span className="payment-detail-value">
                                            <span className="payment-method-icon">{getPaymentMethodIcon()}</span>
                                            {getPaymentMethodDisplay()}
                                        </span>
                                    </div>
                                    <div className="payment-detail-item">
                                        <span className="payment-detail-label">Payment Date</span>
                                        <span className="payment-detail-value">
                                            {new Date(paymentDetails.completedAt).toLocaleDateString('en-PH', {
                                                year: 'numeric',
                                                month: 'long',
                                                day: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit'
                                            })}
                                        </span>
                                    </div>
                                    <div className="payment-detail-item">
                                        <span className="payment-detail-label">Transaction Status</span>
                                        <span className="payment-detail-value">
                                            <span className="status-badge success">Confirmed</span>
                                        </span>
                                    </div>
                                </div>
                                <div className="payment-security-notice">
                                    <span className="security-icon">🔐</span>
                                    <span>Payment processed securely through PayMongo</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Shipping Information */}
                    {order.shipping_address && (
                        <div className="detail-section shipping-section">
                            <h3>Shipping Information</h3>
                            <div className="shipping-details">
                                <div className="shipping-address">
                                    <h4>Delivery Address</h4>
                                    <div className="address-details">
                                        <p><strong>{order.customer_name}</strong></p>
                                        <p>{order.shipping_address.address}</p>
                                        <p>{order.shipping_address.city}, {order.shipping_address.province} {order.shipping_address.postalCode}</p>
                                        <p>{order.shipping_address.country}</p>
                                        {order.customer_phone && <p>Phone: {order.customer_phone}</p>}
                                    </div>
                                </div>
                                {orderTrackingInfo && (
                                    <div className="delivery-info">
                                        <h4>Delivery Information</h4>
                                        <div className="delivery-details">
                                            <div className="delivery-item">
                                                <span className="delivery-label">Estimated Delivery</span>
                                                <span className="delivery-value">{orderTrackingInfo.estimatedDelivery}</span>
                                            </div>
                                            <div className="delivery-item">
                                                <span className="delivery-label">Tracking Updates</span>
                                                <span className="delivery-value">Will be sent to {orderTrackingInfo.trackingEmail}</span>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Order Items */}
                    {order.items && order.items.length > 0 && (
                        <div className="detail-section items-section">
                            <h3>Order Items</h3>
                            <div className="order-items">
                                {order.items.map((item, index) => (
                                    <div key={index} className="order-item">
                                        <div className="item-info">
                                            <h4 className="item-name">{item.name}</h4>
                                            <p className="item-details">Quantity: {item.quantity}</p>
                                        </div>
                                        <div className="item-price">
                                            {formatPrice(item.price * item.quantity)}
                                        </div>
                                    </div>
                                ))}

                                {/* Order Summary */}
                                <div className="order-summary">
                                    {order.subtotal && (
                                        <div className="summary-row">
                                            <span>Subtotal:</span>
                                            <span>{formatPrice(order.subtotal)}</span>
                                        </div>
                                    )}
                                    {order.shipping && (
                                        <div className="summary-row">
                                            <span>Shipping:</span>
                                            <span>{formatPrice(order.shipping)}</span>
                                        </div>
                                    )}
                                    {order.tax && (
                                        <div className="summary-row">
                                            <span>Tax:</span>
                                            <span>{formatPrice(order.tax)}</span>
                                        </div>
                                    )}
                                    <div className="summary-row total">
                                        <span><strong>Total:</strong></span>
                                        <span><strong>{formatPrice(order.total_amount)}</strong></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Next Steps */}
                <div className="next-steps">
                    <h3>What's Next?</h3>
                    <div className="steps-grid">
                        <div className="step-item">
                            <div className="step-icon">📧</div>
                            <div className="step-content">
                                <h4>Order Confirmation</h4>
                                <p>You'll receive an email confirmation with your order details shortly.</p>
                            </div>
                        </div>
                        <div className="step-item">
                            <div className="step-icon">📦</div>
                            <div className="step-content">
                                <h4>Processing</h4>
                                <p>We'll start processing your order and prepare it for shipment.</p>
                            </div>
                        </div>
                        <div className="step-item">
                            <div className="step-icon">🚚</div>
                            <div className="step-content">
                                <h4>Shipping</h4>
                                <p>You'll receive tracking information once your order ships.</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="success-actions">
                    <Link to="/products" className="btn btn-primary">
                        Continue Shopping
                    </Link>
                    <Link to="/orders" className="btn btn-secondary">
                        View Orders
                    </Link>
                </div>

                {/* Support Information */}
                <div className="support-info">
                    <p>
                        Need help with your order? 
                        <Link to="/contact" className="support-link"> Contact our support team</Link>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default OrderSuccessPage;
