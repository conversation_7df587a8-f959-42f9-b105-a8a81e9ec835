import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import FileUploadZone from './FileUploadZone';
import ThreeJSPreview from './ThreeJSPreview';
import { productsApi } from '../../../services/api';
import './ProductForm.css';

const ProductForm = ({ product, categories, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    sku: '',
    category: '',
    description: '',
    basePrice: '',
    costPrice: '',
    weight: '',
    dimensions: { width: 0, depth: 0, height: 0 },
    materials: [],
    colors: [],
    isActive: true
  });

  const [uploadedFiles, setUploadedFiles] = useState({
    models: [],
    images: []
  });

  const [previewModel, setPreviewModel] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  // Initialize form data when product changes
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        sku: product.sku || '',
        category: product.category || '',
        description: product.description || '',
        basePrice: product.basePrice?.toString() || '',
        costPrice: product.costPrice?.toString() || '',
        weight: product.weight?.toString() || '',
        dimensions: product.dimensions || { width: 0, depth: 0, height: 0 },
        materials: product.materials || [],
        colors: product.colors || [],
        isActive: product.isActive !== undefined ? product.isActive : true
      });
    } else {
      // Reset form for new product
      setFormData({
        name: '',
        sku: '',
        category: '',
        description: '',
        basePrice: '',
        costPrice: '',
        weight: '',
        dimensions: { width: 0, depth: 0, height: 0 },
        materials: [],
        colors: [],
        isActive: true
      });
    }
    setUploadedFiles({ models: [], images: [] });
    setPreviewModel(null);
  }, [product]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleDimensionChange = (dimension, value) => {
    setFormData(prev => ({
      ...prev,
      dimensions: {
        ...prev.dimensions,
        [dimension]: parseFloat(value) || 0
      }
    }));
  };

  const handleArrayChange = (field, value) => {
    const array = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      [field]: array
    }));
  };

  const handleFileUpload = (files, type) => {
    setUploadedFiles(prev => ({
      ...prev,
      [type]: [...prev[type], ...files]
    }));

    // Set preview model if it's a 3D model
    if (type === 'models' && files.length > 0) {
      setPreviewModel(files[0]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name || !formData.sku || !formData.category || !formData.basePrice) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);

    try {
      // Create or update product
      const productData = {
        ...formData,
        basePrice: parseFloat(formData.basePrice),
        costPrice: parseFloat(formData.costPrice || formData.basePrice * 0.6),
        weight: formData.weight ? parseFloat(formData.weight) : 0
      };

      if (product) {
        productData.ProductID = product.id || product.ProductID;
      }

      const response = await productsApi.createOrUpdateProduct(productData);
      
      if (!response.success) {
        throw new Error(response.message || 'Failed to save product');
      }

      const savedProduct = response.data;

      // Upload files if any
      if (uploadedFiles.models.length > 0) {
        await uploadFiles(savedProduct.id, uploadedFiles.models, 'models');
      }

      if (uploadedFiles.images.length > 0) {
        await uploadFiles(savedProduct.id, uploadedFiles.images, 'images');
      }

      toast.success(product ? 'Product updated successfully' : 'Product created successfully');
      onSave(savedProduct);

    } catch (error) {
      console.error('Error saving product:', error);
      toast.error(error.message || 'Failed to save product');
    } finally {
      setLoading(false);
    }
  };

  const uploadFiles = async (productId, files, fileType) => {
    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      if (fileType === 'models') {
        await productsApi.uploadModel(productId, formData);
      } else {
        await productsApi.uploadImages(productId, formData);
      }
    } catch (error) {
      console.error(`Error uploading ${fileType}:`, error);
      throw error;
    }
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: '📝' },
    { id: 'files', label: 'Files & Media', icon: '📁' },
    { id: 'preview', label: '3D Preview', icon: '🎯' }
  ];

  return (
    <div className="product-form">
      <div className="pf-header">
        <h2 className="pf-title">
          {product ? 'Edit Product' : 'Add New Product'}
        </h2>
        <div className="pf-header-actions">
          <button
            type="button"
            className="pf-btn pf-btn-secondary"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="pf-btn pf-btn-primary"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : (product ? 'Update Product' : 'Create Product')}
          </button>
        </div>
      </div>

      <div className="pf-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`pf-tab-btn ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
            type="button"
          >
            <span className="pf-tab-icon">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      <form className="pf-content" onSubmit={handleSubmit}>
        {activeTab === 'basic' && (
          <div className="pf-tab-content">
            <div className="pf-form-grid">
              <div className="pf-form-group">
                <label className="pf-label" htmlFor="name">
                  Product Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  className="pf-input"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter product name"
                  required
                />
              </div>

              <div className="pf-form-group">
                <label className="pf-label" htmlFor="sku">
                  SKU *
                </label>
                <input
                  type="text"
                  id="sku"
                  name="sku"
                  className="pf-input"
                  value={formData.sku}
                  onChange={handleInputChange}
                  placeholder="Enter SKU"
                  required
                />
              </div>

              <div className="pf-form-group">
                <label className="pf-label" htmlFor="category">
                  Category *
                </label>
                <select
                  id="category"
                  name="category"
                  className="pf-select"
                  value={formData.category}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select Category</option>
                  {categories.map(category => (
                    <option key={category.id || category.name || category} value={category.name || category}>
                      {category.name || category}
                    </option>
                  ))}
                </select>
              </div>

              <div className="pf-form-group">
                <label className="pf-label" htmlFor="basePrice">
                  Base Price (PHP) *
                </label>
                <input
                  type="number"
                  id="basePrice"
                  name="basePrice"
                  className="pf-input"
                  value={formData.basePrice}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  required
                />
              </div>

              <div className="pf-form-group">
                <label className="pf-label" htmlFor="costPrice">
                  Cost Price (PHP)
                </label>
                <input
                  type="number"
                  id="costPrice"
                  name="costPrice"
                  className="pf-input"
                  value={formData.costPrice}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                />
              </div>

              <div className="pf-form-group">
                <label className="pf-label" htmlFor="weight">
                  Weight (kg)
                </label>
                <input
                  type="number"
                  id="weight"
                  name="weight"
                  className="pf-input"
                  value={formData.weight}
                  onChange={handleInputChange}
                  placeholder="0.0"
                  min="0"
                  step="0.1"
                />
              </div>
            </div>

            <div className="pf-form-group pf-full-width">
              <label className="pf-label" htmlFor="description">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                className="pf-textarea"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter product description"
                rows="4"
              />
            </div>

            <div className="pf-form-grid">
              <div className="pf-form-group">
                <label className="pf-label">
                  Materials (comma-separated)
                </label>
                <input
                  type="text"
                  className="pf-input"
                  value={formData.materials.join(', ')}
                  onChange={(e) => handleArrayChange('materials', e.target.value)}
                  placeholder="e.g., Wood, Metal, Fabric"
                />
              </div>

              <div className="pf-form-group">
                <label className="pf-label">
                  Colors (comma-separated)
                </label>
                <input
                  type="text"
                  className="pf-input"
                  value={formData.colors.join(', ')}
                  onChange={(e) => handleArrayChange('colors', e.target.value)}
                  placeholder="e.g., Black, White, Brown"
                />
              </div>
            </div>

            <div className="pf-dimensions-group">
              <label className="pf-label">Dimensions (cm)</label>
              <div className="pf-dimensions-grid">
                <div className="pf-dimension-input">
                  <label>Width</label>
                  <input
                    type="number"
                    className="pf-input"
                    value={formData.dimensions.width}
                    onChange={(e) => handleDimensionChange('width', e.target.value)}
                    min="0"
                    step="0.1"
                  />
                </div>
                <div className="pf-dimension-input">
                  <label>Depth</label>
                  <input
                    type="number"
                    className="pf-input"
                    value={formData.dimensions.depth}
                    onChange={(e) => handleDimensionChange('depth', e.target.value)}
                    min="0"
                    step="0.1"
                  />
                </div>
                <div className="pf-dimension-input">
                  <label>Height</label>
                  <input
                    type="number"
                    className="pf-input"
                    value={formData.dimensions.height}
                    onChange={(e) => handleDimensionChange('height', e.target.value)}
                    min="0"
                    step="0.1"
                  />
                </div>
              </div>
            </div>

            <div className="pf-form-group pf-checkbox-group">
              <label className="pf-checkbox-label">
                <input
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="pf-checkbox"
                />
                <span className="pf-checkbox-text">Product is active</span>
              </label>
            </div>
          </div>
        )}

        {activeTab === 'files' && (
          <div className="pf-tab-content">
            <div className="pf-upload-section">
              <h3 className="pf-section-title">3D Models</h3>
              <FileUploadZone
                accept=".glb,.gltf"
                multiple={true}
                onFilesSelected={(files) => handleFileUpload(files, 'models')}
                fileType="3D Model"
              />
              {uploadedFiles.models.length > 0 && (
                <div className="pf-uploaded-files">
                  <h4>Uploaded Models ({uploadedFiles.models.length})</h4>
                  <div className="pf-file-list">
                    {uploadedFiles.models.map((file, index) => (
                      <div key={index} className="pf-file-item">
                        <span className="pf-file-icon">🎯</span>
                        <span className="pf-file-name">{file.name}</span>
                        <button
                          type="button"
                          className="pf-file-remove"
                          onClick={() => {
                            const newFiles = uploadedFiles.models.filter((_, i) => i !== index);
                            setUploadedFiles(prev => ({ ...prev, models: newFiles }));
                          }}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="pf-upload-section">
              <h3 className="pf-section-title">Product Images</h3>
              <FileUploadZone
                accept="image/*"
                multiple={true}
                onFilesSelected={(files) => handleFileUpload(files, 'images')}
                fileType="Image"
              />
              {uploadedFiles.images.length > 0 && (
                <div className="pf-uploaded-files">
                  <h4>Uploaded Images ({uploadedFiles.images.length})</h4>
                  <div className="pf-file-list">
                    {uploadedFiles.images.map((file, index) => (
                      <div key={index} className="pf-file-item">
                        <span className="pf-file-icon">🖼️</span>
                        <span className="pf-file-name">{file.name}</span>
                        <button
                          type="button"
                          className="pf-file-remove"
                          onClick={() => {
                            const newFiles = uploadedFiles.images.filter((_, i) => i !== index);
                            setUploadedFiles(prev => ({ ...prev, images: newFiles }));
                          }}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'preview' && (
          <div className="pf-tab-content">
            <div className="pf-preview-section">
              {previewModel ? (
                <ThreeJSPreview modelFile={previewModel} />
              ) : (
                <div className="pf-no-preview">
                  <div className="pf-no-preview-icon">🎯</div>
                  <h3>No 3D Model Selected</h3>
                  <p>Upload a GLB or GLTF file in the Files tab to see the 3D preview</p>
                </div>
              )}
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default ProductForm;
