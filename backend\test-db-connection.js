const sql = require('mssql');
require('dotenv').config();

const dbConfig = {
    server: process.env.DB_SERVER,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true
    }
};

// Only add port if it's explicitly set (for default instances)
if (process.env.DB_PORT && !process.env.DB_PORT.startsWith('#')) {
    dbConfig.port = parseInt(process.env.DB_PORT);
}

async function testDatabaseConnection() {
    console.log('Testing database connection...');
    console.log('Configuration:');
    console.log(`- Server: ${dbConfig.server}`);
    console.log(`- Port: ${dbConfig.port}`);
    console.log(`- Database: ${dbConfig.database}`);
    console.log(`- User: ${dbConfig.user}`);
    console.log('- Password: [HIDDEN]');
    console.log('');

    let pool;
    try {
        // Test connection
        console.log('Attempting to connect...');
        pool = await sql.connect(dbConfig);
        console.log('✅ Successfully connected to SQL Server database!');
        
        // Test a simple query
        console.log('\nTesting basic query...');
        const result = await pool.request().query('SELECT @@VERSION as Version, DB_NAME() as DatabaseName, GETDATE() as CurrentTime');
        
        if (result.recordset && result.recordset.length > 0) {
            console.log('✅ Query executed successfully!');
            console.log('\nDatabase Information:');
            console.log(`- Database Name: ${result.recordset[0].DatabaseName}`);
            console.log(`- Current Time: ${result.recordset[0].CurrentTime}`);
            console.log(`- SQL Server Version: ${result.recordset[0].Version.substring(0, 100)}...`);
        }

        // Test if key tables exist
        console.log('\nChecking for key tables...');
        const tablesResult = await pool.request().query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE' 
            AND TABLE_NAME IN ('Users', 'Products', 'Customers', 'Orders')
            ORDER BY TABLE_NAME
        `);
        
        if (tablesResult.recordset && tablesResult.recordset.length > 0) {
            console.log('✅ Found key tables:');
            tablesResult.recordset.forEach(table => {
                console.log(`  - ${table.TABLE_NAME}`);
            });
        } else {
            console.log('⚠️  No key application tables found. Database may need to be initialized.');
        }

        console.log('\n🎉 Database connection test completed successfully!');
        
    } catch (err) {
        console.error('❌ Database connection failed!');
        console.error('Error details:', err.message);
        
        if (err.code) {
            console.error('Error code:', err.code);
        }
        
        // Provide specific troubleshooting suggestions
        if (err.message.includes('ECONNREFUSED')) {
            console.error('\n💡 Troubleshooting suggestions:');
            console.error('- Ensure SQL Server is running');
            console.error('- Check if SQL Server Express is installed and started');
            console.error('- Verify the server name and port are correct');
        } else if (err.message.includes('Login failed')) {
            console.error('\n💡 Troubleshooting suggestions:');
            console.error('- Check username and password');
            console.error('- Ensure the user has access to the database');
            console.error('- Verify SQL Server authentication is enabled');
        } else if (err.message.includes('Cannot open database')) {
            console.error('\n💡 Troubleshooting suggestions:');
            console.error('- Check if the database exists');
            console.error('- Verify the user has access to the specified database');
        }
        
        process.exit(1);
    } finally {
        if (pool) {
            try {
                await pool.close();
                console.log('\nConnection closed.');
            } catch (closeErr) {
                console.error('Error closing connection:', closeErr.message);
            }
        }
    }
}

// Run the test
testDatabaseConnection();
