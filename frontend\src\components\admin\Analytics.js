import React, { useState, useEffect } from 'react';
import './AdminComponents.css';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut, Pie } from 'react-chartjs-2';
import {
  AnalyticsIcon,
  InventoryIcon,
  ProductsIcon
} from '../admin/icons/AdminIcons';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const Analytics = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data for now - Enhanced with chart data
    setTimeout(() => {
      setAnalyticsData({
        salesData: {
          totalSales: 125000,
          monthlyGrowth: 12.5,
          topSellingCategory: 'Chairs',
          averageOrderValue: 850
        },
        inventoryData: {
          totalProducts: 150,
          lowStockItems: 8,
          outOfStockItems: 2,
          inventoryValue: 450000
        },
        chartData: {
          monthlySales: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
              label: 'Sales (PHP)',
              data: [85000, 92000, 78000, 105000, 118000, 125000, 132000, 128000, 145000, 138000, 155000, 162000],
              borderColor: '#F0B21B',
              backgroundColor: 'rgba(240, 178, 27, 0.1)',
              tension: 0.4,
              fill: true
            }]
          },
          categoryDistribution: {
            labels: ['Chairs', 'Tables', 'Cabinets', 'Workstations', 'Accessories'],
            datasets: [{
              data: [35, 25, 20, 15, 5],
              backgroundColor: [
                '#F0B21B',
                '#10B981',
                '#3B82F6',
                '#8B5CF6',
                '#EF4444'
              ],
              borderWidth: 2,
              borderColor: '#ffffff'
            }]
          },
          inventoryStatus: {
            labels: ['In Stock', 'Low Stock', 'Out of Stock'],
            datasets: [{
              data: [140, 8, 2],
              backgroundColor: [
                '#10B981',
                '#F59E0B',
                '#EF4444'
              ],
              borderWidth: 2,
              borderColor: '#ffffff'
            }]
          },
          weeklyOrders: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
              label: 'Orders',
              data: [12, 19, 15, 25, 22, 18, 8],
              backgroundColor: 'rgba(240, 178, 27, 0.8)',
              borderColor: '#F0B21B',
              borderWidth: 1
            }]
          }
        }
      });
      setLoading(false);
    }, 1000);
  }, []);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#F0B21B',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      },
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      }
    }
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#F0B21B',
        borderWidth: 1,
        cornerRadius: 8
      }
    },
    cutout: '60%'
  };

  if (loading) {
    return (
      <div className="admin-loading">
        <div className="loading-spinner"></div>
        <p>Loading analytics...</p>
      </div>
    );
  }

  return (
    <div className="analytics">
      <div className="admin-card-header">
        <h1 className="admin-card-title">Analytics & Reports</h1>
        <button className="admin-btn admin-btn-primary">Generate Report</button>
      </div>

      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-icon">
            <AnalyticsIcon color="#10B981" />
          </div>
          <div className="metric-content">
            <h3>{formatCurrency(analyticsData.salesData.totalSales)}</h3>
            <p>Total Sales</p>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <AnalyticsIcon color="#3B82F6" />
          </div>
          <div className="metric-content">
            <h3>{analyticsData.salesData.monthlyGrowth}%</h3>
            <p>Monthly Growth</p>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">
            <ProductsIcon color="#F0B21B" />
          </div>
          <div className="metric-content">
            <h3>{analyticsData.salesData.topSellingCategory}</h3>
            <p>Top Category</p>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-icon">🛒</div>
          <div className="metric-content">
            <h3>{formatCurrency(analyticsData.salesData.averageOrderValue)}</h3>
            <p>Avg Order Value</p>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="charts-grid">
        {/* Monthly Sales Trend */}
        <div className="chart-card">
          <div className="chart-header">
            <h3>Monthly Sales Trend</h3>
            <span className="chart-subtitle">Revenue over the past 12 months</span>
          </div>
          <div className="chart-container">
            <Line data={analyticsData.chartData.monthlySales} options={chartOptions} />
          </div>
        </div>

        {/* Category Distribution */}
        <div className="chart-card">
          <div className="chart-header">
            <h3>Sales by Category</h3>
            <span className="chart-subtitle">Product category distribution</span>
          </div>
          <div className="chart-container">
            <Doughnut data={analyticsData.chartData.categoryDistribution} options={doughnutOptions} />
          </div>
        </div>

        {/* Weekly Orders */}
        <div className="chart-card">
          <div className="chart-header">
            <h3>Weekly Orders</h3>
            <span className="chart-subtitle">Orders by day of the week</span>
          </div>
          <div className="chart-container">
            <Bar data={analyticsData.chartData.weeklyOrders} options={chartOptions} />
          </div>
        </div>

        {/* Inventory Status */}
        <div className="chart-card">
          <div className="chart-header">
            <h3>Inventory Status</h3>
            <span className="chart-subtitle">Stock level distribution</span>
          </div>
          <div className="chart-container">
            <Pie data={analyticsData.chartData.inventoryStatus} options={doughnutOptions} />
          </div>
        </div>
      </div>

      <div className="admin-card">
        <h2 className="admin-card-title">Inventory Summary</h2>
        <div className="analytics-content">
          <div className="analytics-item">
            <span>Total Products:</span>
            <span>{analyticsData.inventoryData.totalProducts}</span>
          </div>
          <div className="analytics-item">
            <span>Low Stock Items:</span>
            <span className="warning">{analyticsData.inventoryData.lowStockItems}</span>
          </div>
          <div className="analytics-item">
            <span>Out of Stock:</span>
            <span className="danger">{analyticsData.inventoryData.outOfStockItems}</span>
          </div>
          <div className="analytics-item">
            <span>Total Inventory Value:</span>
            <span>{formatCurrency(analyticsData.inventoryData.inventoryValue)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
