import apiClient from './apiClient';
import { toast } from 'react-toastify';

class CartService {
  constructor() {
    this.cart = this.loadCart();
    this.listeners = [];
  }

  // Load cart from localStorage
  loadCart() {
    try {
      const savedCart = localStorage.getItem('shopping_cart');
      return savedCart ? JSON.parse(savedCart) : { items: [], total: 0 };
    } catch (error) {
      console.error('Error loading cart:', error);
      return { items: [], total: 0 };
    }
  }

  // Save cart to localStorage
  saveCart() {
    try {
      localStorage.setItem('shopping_cart', JSON.stringify(this.cart));
      this.notifyListeners();
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  }

  // Add listener for cart changes
  addListener(callback) {
    this.listeners.push(callback);
  }

  // Remove listener
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // Notify all listeners of cart changes
  notifyListeners() {
    this.listeners.forEach(callback => callback(this.cart));
  }

  // Get current cart
  getCart() {
    return this.cart;
  }

  // Check inventory availability for an item
  async checkInventoryAvailability(variantId, quantity) {
    try {
      const response = await apiClient.get(`/api/inventory/check/${variantId}?quantity=${quantity}`);
      return response.success ? response.data : null;
    } catch (error) {
      console.error('Error checking inventory:', error);
      return null;
    }
  }

  // Bulk check inventory for multiple items
  async bulkCheckInventory(items) {
    try {
      const response = await apiClient.post('/api/inventory/bulk-check', { items });
      return response.success ? response.data : null;
    } catch (error) {
      console.error('Error bulk checking inventory:', error);
      return null;
    }
  }

  // Add item to cart with inventory validation
  async addToCart(item) {
    try {
      const { variantId, quantity = 1, productName, variantName, price, imageUrl } = item;

      // Check if item already exists in cart
      const existingItemIndex = this.cart.items.findIndex(cartItem => cartItem.variantId === variantId);
      const currentQuantity = existingItemIndex >= 0 ? this.cart.items[existingItemIndex].quantity : 0;
      const totalQuantity = currentQuantity + quantity;

      // Check inventory availability
      const availability = await this.checkInventoryAvailability(variantId, totalQuantity);
      
      if (!availability) {
        toast.error('Unable to check stock availability');
        return { success: false, message: 'Unable to check stock availability' };
      }

      if (!availability.available) {
        toast.error(`Insufficient stock. Available: ${availability.availableStock}, Requested: ${totalQuantity}`);
        return { 
          success: false, 
          message: availability.reason,
          availableStock: availability.availableStock
        };
      }

      // Add or update item in cart
      if (existingItemIndex >= 0) {
        this.cart.items[existingItemIndex].quantity = totalQuantity;
        this.cart.items[existingItemIndex].updatedAt = new Date().toISOString();
      } else {
        this.cart.items.push({
          variantId,
          productName,
          variantName,
          price,
          quantity,
          imageUrl,
          addedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }

      this.updateCartTotal();
      this.saveCart();

      toast.success(`${productName} ${variantName ? `(${variantName})` : ''} added to cart`);
      
      return { 
        success: true, 
        message: 'Item added to cart successfully',
        cart: this.cart
      };

    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Error adding item to cart');
      return { success: false, message: 'Error adding item to cart' };
    }
  }

  // Update item quantity in cart with inventory validation
  async updateQuantity(variantId, newQuantity) {
    try {
      if (newQuantity <= 0) {
        return this.removeFromCart(variantId);
      }

      const itemIndex = this.cart.items.findIndex(item => item.variantId === variantId);
      if (itemIndex === -1) {
        return { success: false, message: 'Item not found in cart' };
      }

      // Check inventory availability
      const availability = await this.checkInventoryAvailability(variantId, newQuantity);
      
      if (!availability) {
        toast.error('Unable to check stock availability');
        return { success: false, message: 'Unable to check stock availability' };
      }

      if (!availability.available) {
        toast.error(`Insufficient stock. Available: ${availability.availableStock}, Requested: ${newQuantity}`);
        return { 
          success: false, 
          message: availability.reason,
          availableStock: availability.availableStock
        };
      }

      // Update quantity
      this.cart.items[itemIndex].quantity = newQuantity;
      this.cart.items[itemIndex].updatedAt = new Date().toISOString();

      this.updateCartTotal();
      this.saveCart();

      return { 
        success: true, 
        message: 'Quantity updated successfully',
        cart: this.cart
      };

    } catch (error) {
      console.error('Error updating quantity:', error);
      toast.error('Error updating item quantity');
      return { success: false, message: 'Error updating item quantity' };
    }
  }

  // Remove item from cart
  removeFromCart(variantId) {
    try {
      const itemIndex = this.cart.items.findIndex(item => item.variantId === variantId);
      if (itemIndex === -1) {
        return { success: false, message: 'Item not found in cart' };
      }

      const removedItem = this.cart.items[itemIndex];
      this.cart.items.splice(itemIndex, 1);

      this.updateCartTotal();
      this.saveCart();

      toast.info(`${removedItem.productName} removed from cart`);

      return { 
        success: true, 
        message: 'Item removed from cart',
        cart: this.cart
      };

    } catch (error) {
      console.error('Error removing from cart:', error);
      toast.error('Error removing item from cart');
      return { success: false, message: 'Error removing item from cart' };
    }
  }

  // Clear entire cart
  clearCart() {
    this.cart = { items: [], total: 0 };
    this.saveCart();
    toast.info('Cart cleared');
    return { success: true, message: 'Cart cleared', cart: this.cart };
  }

  // Update cart total
  updateCartTotal() {
    this.cart.total = this.cart.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  }

  // Validate entire cart against current inventory
  async validateCart() {
    try {
      if (this.cart.items.length === 0) {
        return { valid: true, issues: [] };
      }

      const items = this.cart.items.map(item => ({
        variantId: item.variantId,
        quantity: item.quantity
      }));

      const bulkCheck = await this.bulkCheckInventory(items);
      
      if (!bulkCheck) {
        return { valid: false, issues: ['Unable to validate cart inventory'] };
      }

      const issues = [];
      const unavailableItems = bulkCheck.items.filter(item => !item.available);

      unavailableItems.forEach(item => {
        const cartItem = this.cart.items.find(ci => ci.variantId === item.variantId);
        if (cartItem) {
          issues.push({
            variantId: item.variantId,
            productName: cartItem.productName,
            variantName: cartItem.variantName,
            requestedQuantity: item.requestedQuantity,
            availableStock: item.availableStock,
            message: `${cartItem.productName} ${cartItem.variantName ? `(${cartItem.variantName})` : ''}: Only ${item.availableStock} available, but ${item.requestedQuantity} requested`
          });
        }
      });

      return {
        valid: issues.length === 0,
        issues,
        allAvailable: bulkCheck.allAvailable,
        summary: bulkCheck.summary
      };

    } catch (error) {
      console.error('Error validating cart:', error);
      return { valid: false, issues: ['Error validating cart inventory'] };
    }
  }

  // Reserve inventory for checkout
  async reserveInventory() {
    try {
      if (this.cart.items.length === 0) {
        return { success: false, message: 'Cart is empty' };
      }

      const items = this.cart.items.map(item => ({
        variantId: item.variantId,
        quantity: item.quantity
      }));

      const response = await apiClient.post('/api/inventory/reserve', { items });
      
      if (response.success) {
        // Mark cart as having reserved inventory
        this.cart.reservationId = response.data.reservationId || Date.now().toString();
        this.cart.reservedAt = new Date().toISOString();
        this.saveCart();
        
        return { success: true, data: response.data };
      } else {
        return { success: false, message: response.message, failedItems: response.failedItems };
      }

    } catch (error) {
      console.error('Error reserving inventory:', error);
      return { success: false, message: 'Error reserving inventory' };
    }
  }

  // Release reserved inventory
  async releaseReservedInventory() {
    try {
      if (!this.cart.reservationId) {
        return { success: true, message: 'No reservation to release' };
      }

      const items = this.cart.items.map(item => ({
        variantId: item.variantId,
        quantity: item.quantity
      }));

      const response = await apiClient.post('/api/inventory/release', { items });
      
      // Clear reservation info regardless of API response
      delete this.cart.reservationId;
      delete this.cart.reservedAt;
      this.saveCart();
      
      return { success: true, data: response.data };

    } catch (error) {
      console.error('Error releasing reserved inventory:', error);
      // Still clear reservation info on error
      delete this.cart.reservationId;
      delete this.cart.reservedAt;
      this.saveCart();
      
      return { success: false, message: 'Error releasing reserved inventory' };
    }
  }

  // Get cart item count
  getItemCount() {
    return this.cart.items.reduce((count, item) => count + item.quantity, 0);
  }

  // Get cart total
  getTotal() {
    return this.cart.total;
  }
}

// Create and export singleton instance
const cartService = new CartService();
export default cartService;
