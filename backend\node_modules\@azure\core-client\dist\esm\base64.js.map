{"version": 3, "file": "base64.js", "sourceRoot": "", "sources": ["../../src/base64.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC/C,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,KAAiB;IAC/C,MAAM,WAAW,GAAG,KAAK,YAAY,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAqB,CAAC,CAAC;IAC/F,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACtC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,oBAAoB,CAAC,KAAa;IAChD,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;AACjD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Encodes a string in base64 format.\n * @param value - the string to encode\n * @internal\n */\nexport function encodeString(value: string): string {\n  return Buffer.from(value).toString(\"base64\");\n}\n\n/**\n * Encodes a byte array in base64 format.\n * @param value - the Uint8Aray to encode\n * @internal\n */\nexport function encodeByteArray(value: Uint8Array): string {\n  const bufferValue = value instanceof Buffer ? value : Buffer.from(value.buffer as ArrayBuffer);\n  return bufferValue.toString(\"base64\");\n}\n\n/**\n * Decodes a base64 string into a byte array.\n * @param value - the base64 string to decode\n * @internal\n */\nexport function decodeString(value: string): Uint8Array {\n  return Buffer.from(value, \"base64\");\n}\n\n/**\n * Decodes a base64 string into a string.\n * @param value - the base64 string to decode\n * @internal\n */\nexport function decodeStringToString(value: string): string {\n  return Buffer.from(value, \"base64\").toString();\n}\n"]}