import React, { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-toastify';
import { productsApi } from '../../services/api';
import websocketService from '../../services/websocketService';
import ProductCard from './components/ProductCard';
import ProductForm from './components/ProductForm';
import SearchFilters from './components/SearchFilters';
import './ProductManagementNew.css';

const ProductManagementNew = () => {
  // State management
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize] = useState(12); // Grid layout works better with 12 items

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState('ASC');

  // UI states
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'

  // Fetch products with filters and pagination
  const fetchProducts = useCallback(async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: pageSize,
        search: searchTerm || undefined,
        category: selectedCategory || undefined,
        status: selectedStatus || undefined,
        sortBy,
        sortDirection
      };

      const response = await productsApi.getProducts(params);

      if (response.success) {
        setProducts(response.data.products || []);
        setTotalCount(response.data.pagination?.totalItems || 0);
        setTotalPages(response.data.pagination?.totalPages || 0);
      } else {
        toast.error('Failed to fetch products');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Error loading products');
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, searchTerm, selectedCategory, selectedStatus, sortBy, sortDirection]);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const response = await productsApi.getCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // WebSocket event handlers
  useEffect(() => {
    const handleProductCreated = (data) => {
      toast.success(`New product created: ${data.name}`);
      fetchProducts();
    };

    const handleProductUpdated = (data) => {
      toast.info(`Product updated: ${data.name}`);
      fetchProducts();
    };

    const handleProductDeleted = (data) => {
      toast.info(`Product deleted: ${data.name}`);
      fetchProducts();
    };

    // Register event listeners
    websocketService.on('productCreated', handleProductCreated);
    websocketService.on('productUpdated', handleProductUpdated);
    websocketService.on('productDeleted', handleProductDeleted);

    // Cleanup function
    return () => {
      websocketService.off('productCreated', handleProductCreated);
      websocketService.off('productUpdated', handleProductUpdated);
      websocketService.off('productDeleted', handleProductDeleted);
    };
  }, [fetchProducts]);

  // Handle product actions
  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowAddForm(true);
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setShowAddForm(true);
  };

  const handleDeleteProduct = async (productId) => {
    if (!window.confirm('Are you sure you want to delete this product?')) {
      return;
    }

    try {
      const response = await productsApi.deleteProduct(productId);
      if (response.success) {
        toast.success('Product deleted successfully');
        fetchProducts();
      } else {
        toast.error('Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Error deleting product');
    }
  };

  const handleProductSaved = (productData) => {
    setShowAddForm(false);
    setEditingProduct(null);
    fetchProducts();

    if (editingProduct) {
      toast.success('Product updated successfully');
    } else {
      toast.success('Product created successfully');
    }
  };

  const handleFormCancel = () => {
    setShowAddForm(false);
    setEditingProduct(null);
  };

  // Handle search and filters
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
    setCurrentPage(1);
  };

  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
    setCurrentPage(1);
  };

  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      setSortBy(field);
      setSortDirection('ASC');
    }
    setCurrentPage(1);
  };

  // Pagination handlers
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div className="product-management-new">
      {/* Header */}
      <div className="pm-header">
        <div className="pm-header-content">
          <h1 className="pm-title">Product Management</h1>
          <div className="pm-header-stats">
            <span className="pm-stat">
              <span className="pm-stat-number">{totalCount}</span>
              <span className="pm-stat-label">Total Products</span>
            </span>
          </div>
        </div>
        <div className="pm-header-actions">
          <div className="pm-view-toggle">
            <button
              className={`pm-view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
              title="Grid View"
            >
              ⊞
            </button>
            <button
              className={`pm-view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
              title="List View"
            >
              ☰
            </button>
          </div>
          <button
            className="pm-btn pm-btn-primary"
            onClick={handleAddProduct}
          >
            <span className="pm-btn-icon">+</span>
            Add Product
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <SearchFilters
        searchTerm={searchTerm}
        selectedCategory={selectedCategory}
        selectedStatus={selectedStatus}
        categories={categories}
        onSearch={handleSearch}
        onCategoryFilter={handleCategoryFilter}
        onStatusFilter={handleStatusFilter}
        onSort={handleSort}
        sortBy={sortBy}
        sortDirection={sortDirection}
      />

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="pm-form-container">
          <ProductForm
            product={editingProduct}
            categories={categories}
            onSave={handleProductSaved}
            onCancel={handleFormCancel}
          />
        </div>
      )}

      {/* Products Grid/List */}
      <div className="pm-content">
        {loading ? (
          <div className="pm-loading">
            <div className="pm-loading-spinner"></div>
            <p>Loading products...</p>
          </div>
        ) : products.length === 0 ? (
          <div className="pm-empty">
            <div className="pm-empty-icon">📦</div>
            <h3>No Products Found</h3>
            <p>Start by adding your first product to the inventory.</p>
            <button
              className="pm-btn pm-btn-primary"
              onClick={handleAddProduct}
            >
              Add First Product
            </button>
          </div>
        ) : (
          <>
            <div className={`pm-products ${viewMode === 'grid' ? 'pm-grid' : 'pm-list'}`}>
              {products.map(product => (
                <ProductCard
                  key={product.id}
                  product={product}
                  viewMode={viewMode}
                  onEdit={handleEditProduct}
                  onDelete={handleDeleteProduct}
                />
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="pm-pagination">
                <div className="pm-pagination-info">
                  Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} products
                </div>
                <div className="pm-pagination-controls">
                  <button
                    className="pm-btn pm-btn-secondary"
                    onClick={handlePrevPage}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </button>
                  
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const page = i + Math.max(1, currentPage - 2);
                    if (page > totalPages) return null;
                    
                    return (
                      <button
                        key={page}
                        className={`pm-btn ${currentPage === page ? 'pm-btn-primary' : 'pm-btn-secondary'}`}
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </button>
                    );
                  })}
                  
                  <button
                    className="pm-btn pm-btn-secondary"
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ProductManagementNew;
