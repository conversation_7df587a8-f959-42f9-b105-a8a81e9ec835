/* Loading Spinner Styles */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #F0B21B;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-small .spinner {
    width: 1rem;
    height: 1rem;
    border-width: 2px;
}

.spinner-medium .spinner {
    width: 2rem;
    height: 2rem;
    border-width: 3px;
}

.spinner-large .spinner {
    width: 3rem;
    height: 3rem;
    border-width: 4px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
