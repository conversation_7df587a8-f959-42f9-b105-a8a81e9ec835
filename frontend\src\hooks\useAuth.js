import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '../services/auth';

const AuthContext = createContext();

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [token, setToken] = useState(null);

    useEffect(() => {
        const initializeAuth = async () => {
            // For proper guest state, we'll only restore authentication if explicitly enabled
            // This prevents auto-login behavior and ensures users start as guests
            const shouldRestoreSession = process.env.REACT_APP_RESTORE_SESSION !== 'false';

            if (shouldRestoreSession) {
                const savedToken = localStorage.getItem('token');
                const savedUser = localStorage.getItem('user');

                if (savedToken && savedUser) {
                    try {
                        const userData = JSON.parse(savedUser);
                        // Validate that the stored data is still valid
                        if (userData && userData.id && userData.email && userData.role) {
                            setUser(userData);
                            setToken(savedToken);
                        } else {
                            // Clear invalid stored data
                            localStorage.removeItem('token');
                            localStorage.removeItem('user');
                        }
                    } catch (error) {
                        // Clear invalid stored data
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                }
            } else {
                // Clear any existing session data to ensure guest state
                localStorage.removeItem('token');
                localStorage.removeItem('user');
            }

            setLoading(false);
        };

        initializeAuth();
    }, []);

    const login = async (email, password) => {
        try {
            const response = await authService.login(email, password);

            // The API client returns the response body directly (response.data is already extracted)
            const { token: newToken, user: userData } = response;

            // Normalize user data to match frontend expectations and DesignXcelDB schema
            const normalizedUser = {
                id: userData.id,
                username: userData.username,
                email: userData.email,
                fullName: userData.fullName,
                role: userData.role, // This is the key property for RBAC (Admin, InventoryManager, etc.)
                roleId: userData.roleId,
                isActive: userData.isActive
            };

            localStorage.setItem('token', newToken);
            localStorage.setItem('user', JSON.stringify(normalizedUser));
            setToken(newToken);
            setUser(normalizedUser);

            return { success: true, user: normalizedUser };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data?.message || error.message || 'Login failed'
            };
        }
    };

    const register = async (userData) => {
        try {
            const response = await authService.register(userData);
            const { token: newToken, user: newUser } = response;
            
            localStorage.setItem('token', newToken);
            setToken(newToken);
            setUser(newUser);
            
            return { success: true, user: newUser };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data?.message || 'Registration failed'
            };
        }
    };

    const logout = () => {
        // Clear all authentication data
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('refreshToken');

        // Clear any other auth-related data
        localStorage.removeItem('authExpiry');
        localStorage.removeItem('userPreferences');

        // Reset state
        setToken(null);
        setUser(null);

        // Optional: Clear any cached API data
        if (window.location.pathname.startsWith('/admin')) {
            window.location.href = '/';
        }
    };

    const clearAllAuthData = () => {
        // Force clear all authentication data - useful for development/testing
        localStorage.clear();
        sessionStorage.clear();
        setToken(null);
        setUser(null);
        window.location.reload();
    };

    const updateProfile = async (profileData) => {
        try {
            const response = await authService.updateProfile(profileData);
            setUser(response.user);
            return { success: true, user: response.user };
        } catch (error) {
            return {
                success: false,
                error: error.response?.data?.message || 'Profile update failed'
            };
        }
    };

    const value = {
        user,
        token,
        loading,
        login,
        register,
        logout,
        clearAllAuthData,
        updateProfile,
        isAuthenticated: !!user,
        isAdmin: user?.role === 'Admin',
        isInventoryManager: user?.role === 'InventoryManager',
        isTransactionManager: user?.role === 'TransactionManager',
        isUserManager: user?.role === 'UserManager',
        isOrderSupport: user?.role === 'OrderSupport',
        hasAdminAccess: user?.role === 'Admin',
        hasInventoryAccess: user?.role === 'Admin' || user?.role === 'InventoryManager',
        hasTransactionAccess: user?.role === 'Admin' || user?.role === 'TransactionManager',
        hasUserAccess: user?.role === 'Admin' || user?.role === 'UserManager',
        hasSupportAccess: user?.role === 'Admin' || user?.role === 'OrderSupport'
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};
