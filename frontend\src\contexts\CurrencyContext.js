import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Currency context
const CurrencyContext = createContext();

// Currency action types
const CURRENCY_ACTIONS = {
    SET_CURRENCY: 'SET_CURRENCY',
    SET_EXCHANGE_RATES: 'SET_EXCHANGE_RATES',
    LOAD_CURRENCY: 'LOAD_CURRENCY'
};

// Initial state
const initialState = {
    currentCurrency: 'USD',
    exchangeRates: {
        USD: 1,
        PHP: 56.50 // 1 USD = 56.50 PHP (approximate rate)
    },
    currencies: [
        { code: 'USD', name: 'US Dollar', symbol: '$' },
        { code: 'PHP', name: 'Philippine Peso', symbol: '₱' }
    ]
};

// Currency reducer
const currencyReducer = (state, action) => {
    switch (action.type) {
        case CURRENCY_ACTIONS.SET_CURRENCY:
            return {
                ...state,
                currentCurrency: action.payload
            };
        case CURRENCY_ACTIONS.SET_EXCHANGE_RATES:
            return {
                ...state,
                exchangeRates: action.payload
            };
        case CURRENCY_ACTIONS.LOAD_CURRENCY:
            return {
                ...state,
                currentCurrency: action.payload
            };
        default:
            return state;
    }
};

// Currency provider component
export const CurrencyProvider = ({ children }) => {
    const [state, dispatch] = useReducer(currencyReducer, initialState);

    // Load currency from localStorage on mount
    useEffect(() => {
        const savedCurrency = localStorage.getItem('selected-currency');
        if (savedCurrency) {
            dispatch({ type: CURRENCY_ACTIONS.LOAD_CURRENCY, payload: savedCurrency });
        }
    }, []);

    // Save currency to localStorage whenever it changes
    useEffect(() => {
        localStorage.setItem('selected-currency', state.currentCurrency);
    }, [state.currentCurrency]);

    // Set currency
    const setCurrency = (currencyCode) => {
        dispatch({ type: CURRENCY_ACTIONS.SET_CURRENCY, payload: currencyCode });
    };

    // Convert price from USD to selected currency
    const convertPrice = (priceInUSD) => {
        const rate = state.exchangeRates[state.currentCurrency] || 1;
        return priceInUSD * rate;
    };

    // Format price with currency symbol
    const formatPrice = (priceInUSD) => {
        const convertedPrice = convertPrice(priceInUSD);
        const currency = state.currencies.find(c => c.code === state.currentCurrency);
        const symbol = currency ? currency.symbol : '$';
        
        // Format based on currency
        if (state.currentCurrency === 'PHP') {
            return `${symbol}${convertedPrice.toLocaleString('en-PH', { 
                minimumFractionDigits: 2, 
                maximumFractionDigits: 2 
            })}`;
        } else {
            return `${symbol}${convertedPrice.toLocaleString('en-US', { 
                minimumFractionDigits: 2, 
                maximumFractionDigits: 2 
            })}`;
        }
    };

    // Get current currency info
    const getCurrentCurrency = () => {
        return state.currencies.find(c => c.code === state.currentCurrency) || state.currencies[0];
    };

    // Update exchange rates (could be called from an API)
    const updateExchangeRates = (rates) => {
        dispatch({ type: CURRENCY_ACTIONS.SET_EXCHANGE_RATES, payload: rates });
    };

    const value = {
        // State
        currentCurrency: state.currentCurrency,
        currencies: state.currencies,
        exchangeRates: state.exchangeRates,
        
        // Actions
        setCurrency,
        updateExchangeRates,
        
        // Utilities
        convertPrice,
        formatPrice,
        getCurrentCurrency
    };

    return (
        <CurrencyContext.Provider value={value}>
            {children}
        </CurrencyContext.Provider>
    );
};

// Custom hook to use currency context
export const useCurrency = () => {
    const context = useContext(CurrencyContext);
    if (!context) {
        throw new Error('useCurrency must be used within a CurrencyProvider');
    }
    return context;
};

export default CurrencyContext;
