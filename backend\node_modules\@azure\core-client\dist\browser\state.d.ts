import type { OperationRequestInfo } from "./interfaces.js";
/**
 * Browser-only implementation of the module's state. The browser esm variant will not load the commonjs state, so we do not need to share state between the two.
 */
export declare const state: {
    operationRequestMap: WeakMap<import("@azure/core-rest-pipeline").PipelineRequest, OperationRequestInfo>;
};
//# sourceMappingURL=state-browser.d.mts.map