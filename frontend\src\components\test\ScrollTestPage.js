import React from 'react';
import './ScrollTestPage.css';

const ScrollTestPage = () => {
    return (
        <div className="scroll-test-page">
            <div className="test-section hero-section">
                <h1>Header Scroll Test Page</h1>
                <p>Scroll down to test the header behavior</p>
                <div className="scroll-indicator">
                    <span>↓ Scroll Down ↓</span>
                </div>
            </div>

            <div className="test-section content-section">
                <h2>Section 1 - Initial Scroll</h2>
                <p>At 80px scroll, the header should start floating with shadow.</p>
                <div className="content-block">
                    <h3>Testing Header States:</h3>
                    <ul>
                        <li><strong>0-80px:</strong> Normal header with transparent background</li>
                        <li><strong>80px+:</strong> Floating header with shadow and backdrop blur</li>
                        <li><strong>150px+:</strong> Header hidden when scrolling down</li>
                        <li><strong>300px+:</strong> Fullscreen mode - banners hidden</li>
                    </ul>
                </div>
            </div>

            <div className="test-section content-section">
                <h2>Section 2 - Header Hidden Test</h2>
                <p>When scrolling down past 150px, the header should smoothly slide up and become hidden.</p>
                <div className="content-block">
                    <h3>Scroll Direction Detection:</h3>
                    <ul>
                        <li><strong>Scroll Down:</strong> Header slides up smoothly</li>
                        <li><strong>Scroll Up:</strong> Header slides down and becomes visible</li>
                        <li><strong>Smooth Transitions:</strong> No flickering or jumping</li>
                    </ul>
                </div>
            </div>

            <div className="test-section content-section">
                <h2>Section 3 - Fullscreen Mode</h2>
                <p>At 300px+ scroll, the special offer banner and top header should be completely hidden.</p>
                <div className="content-block">
                    <h3>Fullscreen Features:</h3>
                    <ul>
                        <li>Special offer banner hidden</li>
                        <li>Top header contact info hidden</li>
                        <li>Main navigation remains accessible</li>
                        <li>Immersive content viewing experience</li>
                    </ul>
                </div>
            </div>

            <div className="test-section content-section">
                <h2>Section 4 - Performance Test</h2>
                <p>Test rapid scrolling to ensure smooth performance without glitches.</p>
                <div className="content-block">
                    <h3>Performance Optimizations:</h3>
                    <ul>
                        <li>RequestAnimationFrame throttling</li>
                        <li>Debounced state updates</li>
                        <li>GPU acceleration with translate3d</li>
                        <li>Minimal DOM reflows</li>
                    </ul>
                </div>
            </div>

            <div className="test-section content-section">
                <h2>Section 5 - Mobile Responsiveness</h2>
                <p>Test on different screen sizes to ensure consistent behavior.</p>
                <div className="content-block">
                    <h3>Responsive Features:</h3>
                    <ul>
                        <li>Faster animations on mobile (0.3s vs 0.35s)</li>
                        <li>Optimized touch scrolling</li>
                        <li>Consistent behavior across devices</li>
                        <li>Accessibility support</li>
                    </ul>
                </div>
            </div>

            <div className="test-section content-section">
                <h2>Section 6 - Accessibility</h2>
                <p>Header behavior respects user preferences and accessibility needs.</p>
                <div className="content-block">
                    <h3>Accessibility Features:</h3>
                    <ul>
                        <li>Respects prefers-reduced-motion</li>
                        <li>Navigation always accessible</li>
                        <li>Keyboard navigation support</li>
                        <li>Screen reader friendly</li>
                    </ul>
                </div>
            </div>

            <div className="test-section final-section">
                <h2>Test Complete</h2>
                <p>Scroll back up to test the reverse behavior.</p>
                <div className="scroll-indicator">
                    <span>↑ Scroll Up ↑</span>
                </div>
            </div>
        </div>
    );
};

export default ScrollTestPage;
