/* Modern Payment Page Styles */
.modern-payment-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.payment-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Styles */
.payment-header {
    text-align: center;
    margin-bottom: 3rem;
}

.payment-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 2rem;
}

.payment-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-bottom: 1rem;
}

.step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: #e2e8f0;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: #F0B21B;
    color: white;
}

.step-label {
    font-weight: 500;
    color: #64748b;
    font-size: 0.875rem;
}

.step.active .step-label {
    color: #1a202c;
}

/* Error Message */
.error-message {
    background: #fee2e2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #dc2626;
    font-weight: 500;
}

/* Success Message */
.payment-success-message {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.success-icon {
    margin-bottom: 1.5rem;
}

.payment-success-message h2 {
    color: #10b981;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.payment-success-message p {
    color: #64748b;
    margin-bottom: 2rem;
}

/* Content Layout */
.payment-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 3rem;
    align-items: start;
}

/* Payment Forms */
.payment-forms {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.payment-forms h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.section-description {
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.6;
}

/* Payment Methods Grid */
.payment-methods-grid {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.payment-method-card {
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: white;
}

.payment-method-card:hover {
    border-color: #F0B21B;
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.15);
}

.payment-method-card.selected {
    border-color: #F0B21B;
    background: #fffbf0;
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.15);
}

.popular-badge {
    position: absolute;
    top: -0.5rem;
    right: 1rem;
    background: #F0B21B;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.method-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.method-icon {
    flex-shrink: 0;
}

.method-info {
    flex: 1;
}

.method-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 0.25rem;
}

.method-description {
    color: #64748b;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.fee-text {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
}

.fee-text.free {
    color: #10b981;
}

.method-selector {
    flex-shrink: 0;
}

.radio-button {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.radio-button.checked {
    border-color: #F0B21B;
    color: #F0B21B;
}

.method-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
    font-size: 0.875rem;
}

.feature-item svg {
    color: #10b981;
    flex-shrink: 0;
}

/* Payment Security Notice */
.payment-security-notice {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.security-icon {
    flex-shrink: 0;
}

.security-text h4 {
    color: #166534;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.security-text p {
    color: #15803d;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Supported Cards */
.supported-cards {
    margin-top: 2rem;
}

.supported-cards h4 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 1rem;
}

.card-brands {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.card-brand {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background: white;
}

/* Form Styles */
.payment-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-section {
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.5rem;
    background: #fafafa;
}

.form-section h4 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #F0B21B;
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.form-control::placeholder {
    color: #9ca3af;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Card Input Wrapper */
.card-input-wrapper {
    position: relative;
}

.card-brand-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

.card-number-input {
    padding-right: 3rem;
}

/* CVV Help */
.cvv-help {
    margin-left: 0.25rem;
    color: #9ca3af;
    cursor: help;
}

/* Security Notice */
.security-notice {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 0.5rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 1rem;
}

.security-notice p {
    color: #15803d;
    font-size: 0.875rem;
    margin: 0;
}

/* Payment Review */
.payment-review {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.review-section h4 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.review-section p {
    color: #64748b;
    margin: 0.25rem 0;
}

/* Navigation Buttons */
.payment-navigation {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.btn {
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 3rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #F0B21B;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #d69e16;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.btn-secondary {
    background: #f8fafc;
    color: #64748b;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
    background: #e2e8f0;
    color: #475569;
}

.btn-pay {
    font-size: 1.125rem;
    padding: 1rem 2rem;
}

/* Payment Sidebar */
.payment-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Order Summary Styles */
.order-summary {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.summary-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
}

.toggle-details {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.toggle-details:hover {
    background: #f1f5f9;
    color: #475569;
}

/* Order Items */
.order-items {
    margin-bottom: 1.5rem;
}

.order-items h4 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-item {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.item-image {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    overflow: hidden;
    flex-shrink: 0;
    background: #f1f5f9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-placeholder {
    color: #9ca3af;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 500;
    color: #1a202c;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.item-quantity,
.item-variant {
    color: #64748b;
    font-size: 0.75rem;
    margin: 0;
}

.item-price {
    font-weight: 600;
    color: #1a202c;
    font-size: 0.875rem;
}

/* Price Breakdown */
.price-breakdown {
    border-top: 1px solid #e2e8f0;
    padding-top: 1.5rem;
    margin-bottom: 1.5rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
}

.price-row:last-child {
    margin-bottom: 0;
}

.price-row span:first-child {
    color: #64748b;
}

.price-row span:last-child {
    color: #1a202c;
    font-weight: 500;
}

.fee-row {
    color: #64748b;
}

.fee-info {
    margin-left: 0.25rem;
    color: #9ca3af;
    cursor: help;
}

.total-row {
    border-top: 1px solid #e2e8f0;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
    font-size: 1rem;
}

.total-row span {
    font-weight: 600;
    color: #1a202c;
}

.total-amount {
    font-size: 1.125rem;
    color: #F0B21B;
}

/* Payment Method Display */
.payment-method-display {
    margin-bottom: 1.5rem;
}

.payment-method-display h4 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.selected-method {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
    font-size: 0.875rem;
}

/* Savings Notice */
.savings-notice {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 0.5rem;
    padding: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    color: #15803d;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Delivery Info */
.delivery-info {
    margin-bottom: 1.5rem;
}

.delivery-info h4 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.delivery-details {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.delivery-icon {
    color: #F0B21B;
    flex-shrink: 0;
}

.delivery-date {
    font-weight: 500;
    color: #1a202c;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.delivery-note {
    color: #64748b;
    font-size: 0.75rem;
    margin: 0;
}

/* Order ID */
.order-id {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.order-id-label {
    color: #64748b;
}

.order-id-value {
    color: #1a202c;
    font-weight: 500;
    font-family: monospace;
}

/* Security Badges Styles */
.security-badges {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.security-section,
.trust-section,
.guarantee-section,
.support-section {
    margin-bottom: 2rem;
}

.security-section:last-child,
.trust-section:last-child,
.guarantee-section:last-child,
.support-section:last-child {
    margin-bottom: 0;
}

.security-section h4,
.trust-section h4,
.support-section h4 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.security-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.security-feature {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.feature-icon {
    flex-shrink: 0;
}

.feature-info h5 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.feature-info p {
    color: #64748b;
    font-size: 0.75rem;
    margin: 0;
}

.trust-badges {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.trust-badge {
    text-align: center;
}

.badge-logo {
    margin-bottom: 0.5rem;
}

.badge-description {
    color: #64748b;
    font-size: 0.75rem;
    margin: 0;
}

.guarantee-badge {
    background: #fffbf0;
    border: 1px solid #fde68a;
    border-radius: 0.75rem;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: center;
}

.guarantee-icon {
    flex-shrink: 0;
}

.guarantee-text h5 {
    color: #92400e;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.guarantee-text p {
    color: #a16207;
    font-size: 0.75rem;
    margin: 0;
    line-height: 1.4;
}

.support-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.support-option {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.support-icon {
    flex-shrink: 0;
}

.support-info h6 {
    color: #1a202c;
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
}

.support-info p {
    color: #64748b;
    font-size: 0.75rem;
    margin: 0;
}

.policy-links {
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.policy-link {
    color: #64748b;
    text-decoration: none;
    font-size: 0.75rem;
    transition: color 0.3s ease;
}

.policy-link:hover {
    color: #F0B21B;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .payment-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .payment-sidebar {
        order: -1;
    }
}

@media (max-width: 768px) {
    .modern-payment-page {
        padding: 1rem 0;
    }

    .payment-container {
        padding: 0 0.5rem;
    }

    .payment-header h1 {
        font-size: 2rem;
    }

    .payment-steps {
        flex-direction: column;
        gap: 1rem;
    }

    .payment-forms {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .payment-navigation {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .order-summary,
    .security-badges {
        padding: 1.5rem;
    }

    .security-features,
    .trust-badges,
    .support-options {
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    .payment-header h1 {
        font-size: 1.75rem;
    }

    .payment-forms {
        padding: 1rem;
    }

    .form-section {
        padding: 1rem;
    }

    .payment-method-card {
        padding: 1rem;
    }

    .method-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .method-selector {
        align-self: flex-end;
    }

    .order-summary,
    .security-badges {
        padding: 1rem;
    }

    .guarantee-badge {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }

    .security-feature,
    .support-option {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}
