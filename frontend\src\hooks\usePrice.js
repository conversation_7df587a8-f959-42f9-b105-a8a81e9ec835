import { useCurrency } from '../contexts/CurrencyContext';

// Custom hook for price formatting and conversion
export const usePrice = () => {
    const { formatPrice, convertPrice, getCurrentCurrency } = useCurrency();

    // Format a single price
    const formatSinglePrice = (priceInUSD) => {
        if (!priceInUSD || isNaN(priceInUSD)) return formatPrice(0);
        return formatPrice(priceInUSD);
    };

    // Format price with discount
    const formatPriceWithDiscount = (originalPrice, discountPrice) => {
        const formatted = {
            original: formatSinglePrice(originalPrice),
            discount: discountPrice ? formatSinglePrice(discountPrice) : null,
            hasDiscount: !!discountPrice && discountPrice < originalPrice
        };

        return formatted;
    };

    // Calculate savings amount
    const calculateSavings = (originalPrice, discountPrice) => {
        if (!discountPrice || discountPrice >= originalPrice) return null;
        const savings = originalPrice - discountPrice;
        return formatSinglePrice(savings);
    };

    // Calculate savings percentage
    const calculateSavingsPercentage = (originalPrice, discountPrice) => {
        if (!discountPrice || discountPrice >= originalPrice) return null;
        const percentage = ((originalPrice - discountPrice) / originalPrice) * 100;
        return Math.round(percentage);
    };

    // Format cart total
    const formatCartTotal = (items) => {
        const total = items.reduce((sum, item) => {
            const price = item.discountPrice || item.price;
            return sum + (price * item.quantity);
        }, 0);
        return formatSinglePrice(total);
    };

    // Format cart subtotal (before tax/shipping)
    const formatCartSubtotal = (items) => {
        return formatCartTotal(items);
    };

    // Format tax amount
    const formatTax = (subtotal, taxRate = 0.08) => {
        const taxAmount = subtotal * taxRate;
        return formatSinglePrice(taxAmount);
    };

    // Format shipping cost
    const formatShipping = (shippingCost) => {
        return formatSinglePrice(shippingCost);
    };

    // Get current currency symbol
    const getCurrencySymbol = () => {
        return getCurrentCurrency().symbol;
    };

    // Get current currency code
    const getCurrencyCode = () => {
        return getCurrentCurrency().code;
    };

    return {
        formatSinglePrice,
        formatPriceWithDiscount,
        calculateSavings,
        calculateSavingsPercentage,
        formatCartTotal,
        formatCartSubtotal,
        formatTax,
        formatShipping,
        getCurrencySymbol,
        getCurrencyCode,
        convertPrice
    };
};

export default usePrice;
