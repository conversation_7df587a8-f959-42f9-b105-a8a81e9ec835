import React, { useState } from 'react';
import './ProductCard.css';

const ProductCard = ({ product, viewMode, onEdit, onDelete }) => {
  const [imageError, setImageError] = useState(false);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    const statusColors = {
      'active': '#10b981',
      'inactive': '#6b7280',
      'draft': '#f59e0b',
      'discontinued': '#ef4444'
    };
    return statusColors[status?.toLowerCase()] || '#6b7280';
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleEdit = (e) => {
    e.stopPropagation();
    onEdit(product);
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    onDelete(product.id);
  };

  if (viewMode === 'list') {
    return (
      <div className="product-card list-view">
        <div className="pc-list-content">
          <div className="pc-list-image">
            {!imageError ? (
              <img
                src={product.imageUrl || '/api/placeholder/60/60'}
                alt={product.name}
                onError={handleImageError}
              />
            ) : (
              <div className="pc-image-placeholder">
                📦
              </div>
            )}
          </div>
          
          <div className="pc-list-info">
            <div className="pc-list-main">
              <h3 className="pc-name">{product.name}</h3>
              <p className="pc-sku">SKU: {product.sku}</p>
              <p className="pc-description">{product.description}</p>
            </div>
            
            <div className="pc-list-meta">
              <span className="pc-category">{product.category}</span>
              <span className="pc-price">{formatCurrency(product.basePrice)}</span>
              <span 
                className="pc-status"
                style={{ backgroundColor: getStatusColor(product.isActive ? 'active' : 'inactive') }}
              >
                {product.isActive ? 'Active' : 'Inactive'}
              </span>
              <span className="pc-date">Updated: {formatDate(product.updatedAt)}</span>
            </div>
          </div>
          
          <div className="pc-list-actions">
            <button
              className="pc-action-btn pc-edit-btn"
              onClick={handleEdit}
              title="Edit Product"
            >
              ✏️
            </button>
            <button
              className="pc-action-btn pc-delete-btn"
              onClick={handleDelete}
              title="Delete Product"
            >
              🗑️
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div className="product-card grid-view">
      <div className="pc-image-container">
        {!imageError ? (
          <img
            src={product.imageUrl || '/api/placeholder/300/200'}
            alt={product.name}
            className="pc-image"
            onError={handleImageError}
          />
        ) : (
          <div className="pc-image-placeholder large">
            <span className="pc-placeholder-icon">📦</span>
            <span className="pc-placeholder-text">No Image</span>
          </div>
        )}
        
        <div className="pc-image-overlay">
          <div className="pc-overlay-actions">
            <button
              className="pc-overlay-btn"
              onClick={handleEdit}
              title="Edit Product"
            >
              ✏️
            </button>
            <button
              className="pc-overlay-btn pc-delete"
              onClick={handleDelete}
              title="Delete Product"
            >
              🗑️
            </button>
          </div>
        </div>
        
        <div className="pc-status-badge">
          <span 
            className="pc-status-indicator"
            style={{ backgroundColor: getStatusColor(product.isActive ? 'active' : 'inactive') }}
          >
            {product.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>
      
      <div className="pc-content">
        <div className="pc-header">
          <h3 className="pc-name" title={product.name}>{product.name}</h3>
          <p className="pc-sku">SKU: {product.sku}</p>
        </div>
        
        <div className="pc-details">
          <p className="pc-description" title={product.description}>
            {product.description}
          </p>
          
          <div className="pc-meta">
            <span className="pc-category">{product.category}</span>
            <span className="pc-price">{formatCurrency(product.basePrice)}</span>
          </div>
          
          <div className="pc-stats">
            <div className="pc-stat">
              <span className="pc-stat-icon">📷</span>
              <span className="pc-stat-value">{product.imageCount || 0}</span>
            </div>
            <div className="pc-stat">
              <span className="pc-stat-icon">🎯</span>
              <span className="pc-stat-value">{product.modelCount || 0}</span>
            </div>
            <div className="pc-stat">
              <span className="pc-stat-icon">📅</span>
              <span className="pc-stat-value">{formatDate(product.updatedAt)}</span>
            </div>
          </div>
        </div>
        
        <div className="pc-actions">
          <button
            className="pc-action-btn pc-edit-btn"
            onClick={handleEdit}
          >
            <span className="pc-btn-icon">✏️</span>
            Edit
          </button>
          <button
            className="pc-action-btn pc-delete-btn"
            onClick={handleDelete}
          >
            <span className="pc-btn-icon">🗑️</span>
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
