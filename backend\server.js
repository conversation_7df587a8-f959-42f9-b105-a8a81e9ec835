require('dotenv').config();
const express = require('express');
const session = require('express-session');
const flash = require('connect-flash');
const sql = require('mssql');
const path = require('path');
const bcrypt = require('bcryptjs');
const cors = require('cors');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const bodyParser = require('body-parser');

const app = express();

// --- Stripe webhook route: must come BEFORE express.json() and express.urlencoded() ---
app.post('/api/stripe/webhook', bodyParser.raw({ type: 'application/json' }), async (req, res) => {
    const sig = req.headers['stripe-signature'];
    let event;
    try {
        event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
    } catch (err) {
        console.error('[STRIPE WEBHOOK] Signature verification failed:', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }
    
    console.log('[STRIPE WEBHOOK] Received event type:', event.type);
    
    if (event.type === 'checkout.session.completed') {
        const session = event.data.object;
        const email = session.customer_email;
        let cart = [];
        // Parse cart from metadata
        try {
            if (session.metadata && session.metadata.cart) {
                cart = JSON.parse(session.metadata.cart);
                console.log('[STRIPE WEBHOOK] Successfully parsed cart from metadata:', cart);
            } else {
                console.error('[STRIPE WEBHOOK] No cart metadata found in session:', session.metadata);
            }
        } catch (e) {
            console.error('[STRIPE WEBHOOK] Failed to parse cart metadata:', e);
        }
        // Log received data
        console.log('[STRIPE WEBHOOK] Received checkout.session.completed:', {
            email,
            cart,
            sessionId: session.id,
            amount_total: session.amount_total,
            currency: session.currency
        });
        // Save order to database
        try {
            await pool.connect();
            console.log('[STRIPE WEBHOOK] Database connected successfully');
            // Find customer by email
            if (!email) {
                console.error('[STRIPE WEBHOOK] No customer email provided. Order not saved.');
                return res.status(200).send('No customer email, order not saved');
            }
            console.log('[STRIPE WEBHOOK] Looking up customer with email:', email);
            const customerResult = await pool.request()
                .input('email', sql.NVarChar, email)
                .query('SELECT CustomerID, FullName FROM Customers WHERE Email = @email');
            console.log('[STRIPE WEBHOOK] Customer lookup result:', customerResult.recordset);
            const customer = customerResult.recordset[0];
            if (!customer) {
                console.error('[STRIPE WEBHOOK] Customer not found for email:', email);
                return res.status(200).send('Customer not found, order not saved');
            }
            console.log('[STRIPE WEBHOOK] Found customer:', customer);
            if (!Array.isArray(cart) || cart.length === 0) {
                console.error('[STRIPE WEBHOOK] Cart is empty or malformed. Order not saved.');
                return res.status(200).send('Cart is empty or malformed, order not saved');
            }
            // Get customer's default shipping address
            console.log('[STRIPE WEBHOOK] Looking up customer default address for CustomerID:', customer.CustomerID);
            const addressResult = await pool.request()
                .input('customerId', sql.Int, customer.CustomerID)
                .query('SELECT AddressID FROM CustomerAddresses WHERE CustomerID = @customerId AND IsDefault = 1');
            
            let shippingAddressId = null;
            if (addressResult.recordset.length > 0) {
                shippingAddressId = addressResult.recordset[0].AddressID;
                console.log('[STRIPE WEBHOOK] Found default shipping address ID:', shippingAddressId);
            } else {
                console.log('[STRIPE WEBHOOK] No default shipping address found for customer');
            }
            
            // Insert order
            const totalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            console.log('[STRIPE WEBHOOK] Calculating total amount:', totalAmount, 'for cart items:', cart);
            const orderResult = await pool.request()
                .input('customerId', sql.Int, customer.CustomerID)
                .input('status', sql.NVarChar, 'Pending')
                .input('totalAmount', sql.Decimal(10,2), totalAmount)
                .input('paymentMethod', sql.NVarChar, 'E-Wallet')
                .input('currency', sql.NVarChar, 'PHP')
                .input('paymentDate', sql.DateTime, new Date())
                .input('shippingAddressId', sql.Int, shippingAddressId)
                .query(`INSERT INTO Orders (CustomerID, Status, TotalAmount, PaymentMethod, Currency, PaymentDate, ShippingAddressID)
                        OUTPUT INSERTED.OrderID VALUES (@customerId, @status, @totalAmount, @paymentMethod, @currency, @paymentDate, @shippingAddressId)`);
            const orderId = orderResult.recordset[0].OrderID;
            console.log('[STRIPE WEBHOOK] Order inserted successfully with OrderID:', orderId);
            // Insert order items
            for (const item of cart) {
                console.log('[STRIPE WEBHOOK] Processing order item:', item);
                // Find product by name (or use item.id if available)
                let productResult;
                if (item.id) {
                    console.log('[STRIPE WEBHOOK] Looking up product by ID:', item.id);
                    productResult = await pool.request()
                        .input('id', sql.Int, item.id)
                        .query('SELECT ProductID, Name FROM Products WHERE ProductID = @id');
                } else {
                    console.log('[STRIPE WEBHOOK] Looking up product by name:', item.name);
                    productResult = await pool.request()
                        .input('name', sql.NVarChar, item.name)
                        .query('SELECT ProductID, Name FROM Products WHERE Name = @name');
                }
                console.log('[STRIPE WEBHOOK] Product lookup result:', productResult.recordset);
                const product = productResult.recordset[0];
                if (!product) {
                    console.error('[STRIPE WEBHOOK] Product not found for:', item);
                    continue;
                }
                console.log('[STRIPE WEBHOOK] Inserting order item for product:', product.Name);
                await pool.request()
                    .input('orderId', sql.Int, orderId)
                    .input('productId', sql.Int, product.ProductID)
                    .input('quantity', sql.Int, item.quantity)
                    .input('priceAtPurchase', sql.Decimal(10,2), item.price)
                    .query(`INSERT INTO OrderItems (OrderID, ProductID, Quantity, PriceAtPurchase)
                            VALUES (@orderId, @productId, @quantity, @priceAtPurchase)`);
                console.log('[STRIPE WEBHOOK] Order item inserted successfully');
            }
            console.log('[STRIPE WEBHOOK] Order saved successfully for customer:', email, 'OrderID:', orderId);
        } catch (err) {
            console.error('[STRIPE WEBHOOK] Error saving order:', err);
            console.error('[STRIPE WEBHOOK] Error stack:', err.stack);
        }
    }
    res.status(200).send('Webhook received');
});

// --- All other middleware/routes ---
app.use(cors({
    origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://localhost:5000'],
    credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Session configuration
app.use(session({
    secret: process.env.SESSION_SECRET || 'your_session_secret_key_here',
    resave: false,
    saveUninitialized: false
}));

app.use(flash());

// Force UTF-8 encoding for all HTML responses
app.use((req, res, next) => {
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    next();
});

// Database configuration
const dbConfig = {
    server: process.env.DB_SERVER,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
    options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true
    }
};

// Only add port if it's explicitly set (for default instances)
if (process.env.DB_PORT && !process.env.DB_PORT.startsWith('#')) {
    dbConfig.port = parseInt(process.env.DB_PORT);
}

// Database connection pool
const pool = new sql.ConnectionPool(dbConfig);
const poolConnect = pool.connect()
    .then(() => {
        console.log('Connected to MSSQL database successfully');
    })
    .catch(err => {
        console.error('Database Connection Failed! Bad Config: ', err);
    });

// Middleware to make user available to all views
app.use((req, res, next) => {
    res.locals.user = req.session.user;
    res.locals.error = req.flash('error');
    res.locals.success = req.flash('success');
    next();
});

// Routes
app.get('/', (req, res) => {
    res.redirect('/login');
});

app.get('/login', (req, res) => {
    res.render('EmpLogin/EmpLogin');
});

app.post('/auth/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        
        await poolConnect;
        
        // Query to get user with role information
        const result = await pool.request()
            .input('email', sql.NVarChar, email)
            .query(`
                SELECT u.*, r.RoleName 
                FROM Users u 
                JOIN Roles r ON u.RoleID = r.RoleID 
                WHERE u.Email = @email AND u.IsActive = 1
            `);

        const user = result.recordset[0];

        if (!user) {
            req.flash('error', 'Invalid email or password');
            return res.redirect('/login');
        }

        // In a real application, you should use bcrypt to compare hashed passwords
        // For now, we'll do a direct comparison since the passwords are stored as plain text
        if (password !== user.PasswordHash) {
            req.flash('error', 'Invalid email or password');
            return res.redirect('/login');
        }

        // Store user in session
        req.session.user = {
            id: user.UserID,
            username: user.Username,
            fullName: user.FullName,
            email: user.Email,
            role: user.RoleName
        };

        // Redirect based on role
        switch (user.RoleName) {
            case 'Admin':
                res.redirect('/Employee/AdminIndex');
                break;
            case 'TransactionManager':
                res.redirect('/Employee/TransactionManager');
                break;
            case 'InventoryManager':
                res.redirect('/Employee/InventoryManager');
                break;
            case 'UserManager':
                res.redirect('/Employee/UserManager');
                break;
            case 'OrderSupport':
                res.redirect('/Employee/OrderSupport');
                break;
            default:
                res.redirect('/login');
        }
    } catch (err) {
        console.error('Login error:', err);
        req.flash('error', 'An error occurred during login');
        res.redirect('/login');
    }
});

// Customer login endpoint for frontend
app.post('/api/auth/customer/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        
        await poolConnect;
        
        // Query to get customer
        const result = await pool.request()
            .input('email', sql.NVarChar, email)
            .query(`
                SELECT * FROM Customers 
                WHERE Email = @email AND IsActive = 1
            `);

        const customer = result.recordset[0];

        if (!customer) {
            return res.status(401).json({ 
                success: false, 
                message: 'Invalid email or password' 
            });
        }

        // Compare password (in real app, use bcrypt)
        const passwordMatch = await bcrypt.compare(password, customer.PasswordHash);
        if (!passwordMatch) {
            return res.status(401).json({ 
                success: false, 
                message: 'Invalid email or password' 
            });
        }

        // Create customer session data
        const customerData = {
            id: customer.CustomerID,
            fullName: customer.FullName,
            email: customer.Email,
            phoneNumber: customer.PhoneNumber,
            role: 'Customer',
            type: 'customer'
        };

        // Store in session
        req.session.user = customerData;

        // Return success response for frontend
        res.json({
            success: true,
            message: 'Login successful',
            user: customerData,
            token: 'customer-token-' + customer.CustomerID // Simple token for demo
        });

    } catch (err) {
        console.error('Customer login error:', err);
        res.status(500).json({ 
            success: false, 
            message: 'An error occurred during login' 
        });
    }
});

// Customer registration endpoint
app.post('/api/auth/customer/register', async (req, res) => {
    try {
        const { fullName, email, phoneNumber, password } = req.body;
        if (!fullName || !email || !password) {
            return res.status(400).json({ success: false, message: 'Full name, email, and password are required.' });
        }
        await poolConnect;
        // Check for duplicate email
        const existing = await pool.request()
            .input('email', sql.NVarChar, email)
            .query('SELECT CustomerID FROM Customers WHERE Email = @email');
        if (existing.recordset.length > 0) {
            return res.status(409).json({ success: false, message: 'Email already registered.' });
        }
        // Hash password
        const hash = await bcrypt.hash(password, 10);
        // Insert new customer
        await pool.request()
            .input('fullName', sql.NVarChar, fullName)
            .input('email', sql.NVarChar, email)
            .input('phoneNumber', sql.NVarChar, phoneNumber || null)
            .input('passwordHash', sql.NVarChar, hash)
            .query('INSERT INTO Customers (FullName, Email, PhoneNumber, PasswordHash) VALUES (@fullName, @email, @phoneNumber, @passwordHash)');
        res.json({ success: true, message: 'Registration successful.' });
    } catch (err) {
        console.error('Customer registration error:', err);
        res.status(500).json({ success: false, message: 'An error occurred during registration.' });
    }
});

// --- OTP EMAIL ENDPOINTS ---
const nodemailer = require('nodemailer');
const otpStore = {};

// Helper to generate 6-digit OTP
function generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

// Send OTP endpoint
app.post('/api/auth/send-otp', async (req, res) => {
    const { email } = req.body;
    if (!email || !email.includes('@')) {
        return res.status(400).json({ success: false, message: 'Invalid email.' });
    }
    const otp = generateOTP();
    otpStore[email] = { otp, expires: Date.now() + 10 * 60 * 1000 }; // 10 min expiry
    try {
        const transporter = nodemailer.createTransport({
            service: 'gmail',
            auth: {
                user: process.env.OTP_EMAIL_USER,
                pass: process.env.OTP_EMAIL_PASS
            }
        });
        await transporter.sendMail({
            from: process.env.OTP_EMAIL_USER,
            to: email,
            subject: 'Your DesignXcel OTP Code',
            text: `Your OTP code is: ${otp}. It is valid for 10 minutes.`
        });
        res.json({ success: true, message: 'OTP sent to email.' });
    } catch (err) {
        console.error('Error sending OTP email:', err);
        res.status(500).json({ success: false, message: 'Failed to send OTP.' });
    }
});

// Verify OTP endpoint
app.post('/api/auth/verify-otp', (req, res) => {
    const { email, otp } = req.body;
    if (!email || !otp) {
        return res.status(400).json({ success: false, message: 'Email and OTP required.' });
    }
    const record = otpStore[email];
    if (!record || record.otp !== otp) {
        return res.status(400).json({ success: false, message: 'Invalid OTP.' });
    }
    if (Date.now() > record.expires) {
        delete otpStore[email];
        return res.status(400).json({ success: false, message: 'OTP expired.' });
    }
    delete otpStore[email];
    res.json({ success: true, message: 'OTP verified.' });
});

// Customer profile API endpoints
app.get('/api/customer/profile', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role !== 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        await poolConnect;
        const customerId = req.session.user.id;
        const result = await pool.request()
            .input('customerId', sql.Int, customerId)
            .query('SELECT CustomerID, FullName, Email, PhoneNumber FROM Customers WHERE CustomerID = @customerId');
        const customer = result.recordset[0];
        res.json({ success: true, customer });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to fetch profile', error: err.message });
    }
});

// Get all orders for the currently logged-in customer
app.get('/api/customer/orders', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role !== 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        await poolConnect;
        const customerId = req.session.user.id;
        const result = await pool.request()
            .input('customerId', sql.Int, customerId)
            .query(`SELECT OrderID, Status, TotalAmount, OrderDate, PaymentMethod FROM Orders WHERE CustomerID = @customerId ORDER BY OrderDate DESC`);
        res.json({ success: true, orders: result.recordset });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to fetch orders', error: err.message });
    }
});

// Cancel an order for the currently logged-in customer
app.put('/api/customer/orders/:orderId/cancel', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role !== 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        await poolConnect;
        const customerId = req.session.user.id;
        const orderId = parseInt(req.params.orderId);
        // Only allow cancelling if the order belongs to the customer and is not already cancelled
        const orderResult = await pool.request()
            .input('orderId', sql.Int, orderId)
            .input('customerId', sql.Int, customerId)
            .query('SELECT Status FROM Orders WHERE OrderID = @orderId AND CustomerID = @customerId');
        if (!orderResult.recordset.length) {
            return res.status(404).json({ success: false, message: 'Order not found.' });
        }
        const order = orderResult.recordset[0];
        if (order.Status === 'Cancelled') {
            return res.json({ success: true, message: 'Order already cancelled.' });
        }
        await pool.request()
            .input('orderId', sql.Int, orderId)
            .query(`UPDATE Orders SET Status = 'Cancelled' WHERE OrderID = @orderId`);
        res.json({ success: true, message: 'Order cancelled successfully.' });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to cancel order', error: err.message });
    }
});

// Get all orders with items for the currently logged-in customer
app.get('/api/customer/orders-with-items', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role !== 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        await poolConnect;
        const customerId = req.session.user.id;
        // Get user info
        const customerResult = await pool.request()
            .input('customerId', sql.Int, customerId)
            .query('SELECT CustomerID, FullName, Email, PhoneNumber FROM Customers WHERE CustomerID = @customerId');
        const customer = customerResult.recordset[0];
        // Get default address
        const addressResult = await pool.request()
            .input('customerId', sql.Int, customerId)
            .query('SELECT TOP 1 * FROM CustomerAddresses WHERE CustomerID = @customerId AND IsDefault = 1');
        const address = addressResult.recordset[0] || null;
        // Get all orders
        const ordersResult = await pool.request()
            .input('customerId', sql.Int, customerId)
            .query(`SELECT OrderID, Status, TotalAmount, OrderDate, PaymentMethod FROM Orders WHERE CustomerID = @customerId ORDER BY OrderDate DESC`);
        const orders = ordersResult.recordset;
        if (!orders.length) return res.json({ success: true, orders: [] });
        // Get all order items for these orders (with product image)
        const orderIds = orders.map(o => o.OrderID);
        if (!orderIds.length) return res.json({ success: true, orders: [] });
        const orderItemsResult = await pool.request()
            .query(`SELECT oi.OrderID, oi.ProductID, oi.Quantity, oi.PriceAtPurchase, p.Name, p.ImageURL FROM OrderItems oi JOIN Products p ON oi.ProductID = p.ProductID WHERE oi.OrderID IN (${orderIds.join(',')})`);
        const itemsByOrder = {};
        for (const item of orderItemsResult.recordset) {
            if (!itemsByOrder[item.OrderID]) itemsByOrder[item.OrderID] = [];
            itemsByOrder[item.OrderID].push({
                name: item.Name,
                quantity: item.Quantity,
                price: item.PriceAtPurchase,
                image: item.ImageURL || null
            });
        }
        // Attach items, user, and address to orders
        const ordersWithItems = orders.map(order => ({
            ...order,
            items: itemsByOrder[order.OrderID] || [],
            user: {
                fullName: customer.FullName,
                email: customer.Email,
                phoneNumber: customer.PhoneNumber
            },
            address: address
        }));
        res.json({ success: true, orders: ordersWithItems });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to fetch orders with items', error: err.message });
    }
});

app.put('/api/customer/profile', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role !== 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        const { fullName, email, phoneNumber } = req.body;
        const customerId = req.session.user.id;
        await poolConnect;
        await pool.request()
            .input('customerId', sql.Int, customerId)
            .input('fullName', sql.NVarChar, fullName)
            .input('email', sql.NVarChar, email)
            .input('phoneNumber', sql.NVarChar, phoneNumber)
            .query('UPDATE Customers SET FullName = @fullName, Email = @email, PhoneNumber = @phoneNumber WHERE CustomerID = @customerId');
        res.json({ success: true });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to update profile', error: err.message });
    }
});

// Customer addresses API endpoints
app.get('/api/customer/addresses', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role !== 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        await poolConnect;
        const customerId = req.session.user.id;
        const result = await pool.request()
            .input('customerId', sql.Int, customerId)
            .query('SELECT * FROM CustomerAddresses WHERE CustomerID = @customerId');
        res.json({ success: true, addresses: result.recordset });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to fetch addresses', error: err.message });
    }
});

app.post('/api/customer/addresses', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role !== 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        const customerId = req.session.user.id;
        const { label, houseNumber, street, barangay, city, province, region, postalCode, country, isDefault } = req.body;
        await poolConnect;
        await pool.request()
            .input('customerId', sql.Int, customerId)
            .input('label', sql.NVarChar, label)
            .input('houseNumber', sql.NVarChar, houseNumber)
            .input('street', sql.NVarChar, street)
            .input('barangay', sql.NVarChar, barangay)
            .input('city', sql.NVarChar, city)
            .input('province', sql.NVarChar, province)
            .input('region', sql.NVarChar, region)
            .input('postalCode', sql.NVarChar, postalCode)
            .input('country', sql.NVarChar, country || 'Philippines')
            .input('isDefault', sql.Bit, isDefault ? 1 : 0)
            .query(`INSERT INTO CustomerAddresses (CustomerID, Label, HouseNumber, Street, Barangay, City, Province, Region, PostalCode, Country, IsDefault)
                    VALUES (@customerId, @label, @houseNumber, @street, @barangay, @city, @province, @region, @postalCode, @country, @isDefault)`);
        res.json({ success: true });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to add address', error: err.message });
    }
});

app.put('/api/customer/addresses/:addressId', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role !== 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        const customerId = req.session.user.id;
        const addressId = req.params.addressId;
        const { label, houseNumber, street, barangay, city, province, region, postalCode, country, isDefault } = req.body;
        await poolConnect;
        await pool.request()
            .input('addressId', sql.Int, addressId)
            .input('customerId', sql.Int, customerId)
            .input('label', sql.NVarChar, label)
            .input('houseNumber', sql.NVarChar, houseNumber)
            .input('street', sql.NVarChar, street)
            .input('barangay', sql.NVarChar, barangay)
            .input('city', sql.NVarChar, city)
            .input('province', sql.NVarChar, province)
            .input('region', sql.NVarChar, region)
            .input('postalCode', sql.NVarChar, postalCode)
            .input('country', sql.NVarChar, country || 'Philippines')
            .input('isDefault', sql.Bit, isDefault ? 1 : 0)
            .query(`UPDATE CustomerAddresses SET Label=@label, HouseNumber=@houseNumber, Street=@street, Barangay=@barangay, City=@city, Province=@province, Region=@region, PostalCode=@postalCode, Country=@country, IsDefault=@isDefault WHERE AddressID=@addressId AND CustomerID=@customerId`);
        res.json({ success: true });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to update address', error: err.message });
    }
});

// Employee/Admin profile API endpoints
app.get('/api/user/profile', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role === 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        // For demo, just return session user info
        const { id, username, fullName, email, role } = req.session.user;
        res.json({ success: true, user: { id, username, fullName, email, role } });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to fetch profile', error: err.message });
    }
});

app.put('/api/user/profile', async (req, res) => {
    try {
        if (!req.session.user || req.session.user.role === 'Customer') {
            return res.status(401).json({ success: false, message: 'Unauthorized' });
        }
        const { fullName, email } = req.body;
        const userId = req.session.user.id;
        await poolConnect;
        await pool.request()
            .input('userId', sql.Int, userId)
            .input('fullName', sql.NVarChar, fullName)
            .input('email', sql.NVarChar, email)
            .query('UPDATE Users SET FullName = @fullName, Email = @email WHERE UserID = @userId');
        // Update session
        req.session.user.fullName = fullName;
        req.session.user.email = email;
        res.json({ success: true });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to update profile', error: err.message });
    }
});

// --- Testimonials API ---
const multer = require('multer');
const fs = require('fs');

// Set up multer for image uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadPath = path.join(__dirname, 'public', 'uploads');
        if (!fs.existsSync(uploadPath)) fs.mkdirSync(uploadPath, { recursive: true });
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + '-' + file.originalname);
    }
});
const upload = multer({ storage: storage });

// GET all testimonials (add rating)
app.get('/api/admin/testimonials', async (req, res) => {
    try {
        await poolConnect;
        const result = await pool.request().query('SELECT * FROM Testimonials ORDER BY createdAt DESC');
        res.json(result.recordset.map(t => ({
            id: t.ID,
            name: t.Name,
            title: t.Title,
            text: t.Text,
            imageUrl: t.ImageUrl,
            rating: t.Rating,
            createdAt: t.CreatedAt
        })));
    } catch (err) {
        console.error('Testimonials GET error:', err);
        res.status(500).json({ error: 'Failed to fetch testimonials.', details: err.message });
    }
});

// POST new testimonial (accept rating)
app.post('/api/admin/testimonials', upload.single('image'), async (req, res) => {
    try {
        const { name, title, text, rating } = req.body;
        let imageUrl = '';
        if (req.file) {
            imageUrl = '/uploads/' + req.file.filename;
        }
        await poolConnect;
        const result = await pool.request()
            .input('Name', sql.NVarChar, name)
            .input('Title', sql.NVarChar, title)
            .input('Text', sql.NVarChar, text)
            .input('ImageUrl', sql.NVarChar, imageUrl)
            .input('Rating', sql.Int, rating)
            .query('INSERT INTO Testimonials (Name, Title, Text, ImageUrl, Rating, CreatedAt) OUTPUT INSERTED.ID VALUES (@Name, @Title, @Text, @ImageUrl, @Rating, GETDATE())');
        res.json({ success: true, id: result.recordset[0].ID });
    } catch (err) {
        console.error('Testimonials POST error:', err);
        res.status(500).json({ error: 'Failed to add testimonial.', details: err.message });
    }
});

// DELETE testimonial
app.delete('/api/admin/testimonials/:id', async (req, res) => {
    try {
        const id = req.params.id;
        await poolConnect;
        // Get image path to delete file
        const imgResult = await pool.request().input('ID', sql.Int, id).query('SELECT ImageUrl FROM Testimonials WHERE ID = @ID');
        if (imgResult.recordset.length && imgResult.recordset[0].ImageUrl) {
            const imgPath = path.join(__dirname, 'public', imgResult.recordset[0].ImageUrl);
            if (fs.existsSync(imgPath)) fs.unlinkSync(imgPath);
        }
        await pool.request().input('ID', sql.Int, id).query('DELETE FROM Testimonials WHERE ID = @ID');
        res.json({ success: true });
    } catch (err) {
        res.status(500).json({ error: 'Failed to delete testimonial.' });
    }
});

// --- Products API for AdminCMS ---
// Get all products
app.get('/api/admin/products', async (req, res) => {
    try {
        await poolConnect;
        const result = await pool.request().query(`
            SELECT 
                ProductID,
                Name,
                Description,
                Price,
                StockQuantity,
                Category,
                ImageURL,
                DateAdded,
                IsActive,
                Dimensions,
                IsFeatured
            FROM Products
        `);
        res.json(result.recordset);
    } catch (err) {
        res.status(500).json({ error: 'Failed to fetch products', details: err.message });
    }
});
// Set/unset featured
app.post('/api/admin/products/:id/feature', async (req, res) => {
    try {
        const { isFeatured } = req.body;
        const productId = req.params.id;
        await poolConnect;
        await pool.request()
            .input('isFeatured', sql.Bit, isFeatured ? 1 : 0)
            .input('productId', sql.Int, productId)
            .query('UPDATE Products SET IsFeatured = @isFeatured WHERE ProductID = @productId');
        res.json({ success: true });
    } catch (err) {
        res.status(500).json({ error: 'Failed to update featured status', details: err.message });
    }
});

// Mount the routes from routes.js
const employeeRoutes = require('./routes')(sql, pool);
app.use('/', employeeRoutes);

// Test endpoint to simulate webhook locally (for development only)
app.post('/api/test-webhook', async (req, res) => {
    console.log('[TEST WEBHOOK] Simulating webhook call');
    
    // Simulate the webhook event data
    const mockEvent = {
        type: 'checkout.session.completed',
        data: {
            object: {
                id: 'cs_test_' + Date.now(),
                customer_email: req.body.email || '<EMAIL>',
                metadata: {
                    cart: JSON.stringify(req.body.items || [])
                },
                amount_total: req.body.total || 2000,
                currency: 'php'
            }
        }
    };
    
    console.log('[TEST WEBHOOK] Mock event data:', mockEvent);
    
    // Process the mock event
    if (mockEvent.type === 'checkout.session.completed') {
        const session = mockEvent.data.object;
        const email = session.customer_email;
        let cart = [];
        
        // Parse cart from metadata
        try {
            if (session.metadata && session.metadata.cart) {
                cart = JSON.parse(session.metadata.cart);
                console.log('[TEST WEBHOOK] Successfully parsed cart from metadata:', cart);
            } else {
                console.error('[TEST WEBHOOK] No cart metadata found in session:', session.metadata);
            }
        } catch (e) {
            console.error('[TEST WEBHOOK] Failed to parse cart metadata:', e);
        }
        
        // Log received data
        console.log('[TEST WEBHOOK] Received checkout.session.completed:', {
            email,
            cart,
            sessionId: session.id,
            amount_total: session.amount_total,
            currency: session.currency
        });
        
        // Save order to database
        try {
            await poolConnect;
            console.log('[TEST WEBHOOK] Database connected successfully');
            
            // Find customer by email
            if (!email) {
                console.error('[TEST WEBHOOK] No customer email provided. Order not saved.');
                return res.status(400).json({ error: 'No customer email provided' });
            }
            
            console.log('[TEST WEBHOOK] Looking up customer with email:', email);
            const customerResult = await pool.request()
                .input('email', sql.NVarChar, email)
                .query('SELECT CustomerID, FullName FROM Customers WHERE Email = @email');
            
            console.log('[TEST WEBHOOK] Customer lookup result:', customerResult.recordset);
            
            const customer = customerResult.recordset[0];
            if (!customer) {
                console.error('[TEST WEBHOOK] Customer not found for email:', email);
                return res.status(400).json({ error: 'Customer not found' });
            }
            
            console.log('[TEST WEBHOOK] Found customer:', customer);
            
            if (!Array.isArray(cart) || cart.length === 0) {
                console.error('[TEST WEBHOOK] Cart is empty or malformed. Order not saved.');
                return res.status(400).json({ error: 'Cart is empty or malformed' });
            }
            
            // Get customer's default shipping address
            console.log('[TEST WEBHOOK] Looking up customer default address for CustomerID:', customer.CustomerID);
            const addressResult = await pool.request()
                .input('customerId', sql.Int, customer.CustomerID)
                .query('SELECT AddressID FROM CustomerAddresses WHERE CustomerID = @customerId AND IsDefault = 1');
            
            let shippingAddressId = null;
            if (addressResult.recordset.length > 0) {
                shippingAddressId = addressResult.recordset[0].AddressID;
                console.log('[TEST WEBHOOK] Found default shipping address ID:', shippingAddressId);
            } else {
                console.log('[TEST WEBHOOK] No default shipping address found for customer');
            }
            
            // Insert order
            const totalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            console.log('[TEST WEBHOOK] Calculating total amount:', totalAmount, 'for cart items:', cart);
            
            const orderResult = await pool.request()
                .input('customerId', sql.Int, customer.CustomerID)
                .input('status', sql.NVarChar, 'Pending')
                .input('totalAmount', sql.Decimal(10,2), totalAmount)
                .input('paymentMethod', sql.NVarChar, 'E-Wallet')
                .input('currency', sql.NVarChar, 'PHP')
                .input('paymentDate', sql.DateTime, new Date())
                .input('shippingAddressId', sql.Int, shippingAddressId)
                .query(`INSERT INTO Orders (CustomerID, Status, TotalAmount, PaymentMethod, Currency, PaymentDate, ShippingAddressID)
                        OUTPUT INSERTED.OrderID VALUES (@customerId, @status, @totalAmount, @paymentMethod, @currency, @paymentDate, @shippingAddressId)`);
            
            const orderId = orderResult.recordset[0].OrderID;
            console.log('[TEST WEBHOOK] Order inserted successfully with OrderID:', orderId);
            
            // Insert order items
            for (const item of cart) {
                console.log('[TEST WEBHOOK] Processing order item:', item);
                
                // Find product by name (or use item.id if available)
                let productResult;
                if (item.id) {
                    console.log('[TEST WEBHOOK] Looking up product by ID:', item.id);
                    productResult = await pool.request()
                        .input('id', sql.Int, item.id)
                        .query('SELECT ProductID, Name FROM Products WHERE ProductID = @id');
                } else {
                    console.log('[TEST WEBHOOK] Looking up product by name:', item.name);
                    productResult = await pool.request()
                        .input('name', sql.NVarChar, item.name)
                        .query('SELECT ProductID, Name FROM Products WHERE Name = @name');
                }
                
                console.log('[TEST WEBHOOK] Product lookup result:', productResult.recordset);
                
                const product = productResult.recordset[0];
                if (!product) {
                    console.error('[TEST WEBHOOK] Product not found for:', item);
                    continue;
                }
                
                console.log('[TEST WEBHOOK] Inserting order item for product:', product.Name);
                await pool.request()
                    .input('orderId', sql.Int, orderId)
                    .input('productId', sql.Int, product.ProductID)
                    .input('quantity', sql.Int, item.quantity)
                    .input('priceAtPurchase', sql.Decimal(10,2), item.price)
                    .query(`INSERT INTO OrderItems (OrderID, ProductID, Quantity, PriceAtPurchase)
                            VALUES (@orderId, @productId, @quantity, @priceAtPurchase)`);
                
                console.log('[TEST WEBHOOK] Order item inserted successfully');
            }
            
            console.log('[TEST WEBHOOK] Order saved successfully for customer:', email, 'OrderID:', orderId);
            res.json({ success: true, orderId, message: 'Order saved successfully' });
            
        } catch (err) {
            console.error('[TEST WEBHOOK] Error saving order:', err);
            console.error('[TEST WEBHOOK] Error stack:', err.stack);
            res.status(500).json({ error: 'Failed to save order', details: err.message });
        }
    }
});

// Stripe Payment Routes
app.post('/api/create-checkout-session', async (req, res) => {
    try {
        console.log('Received checkout session request:', {
            body: req.body,
            items: req.body.items,
            email: req.body.email
        });
        
        const { items, email } = req.body; // items: [{ name, quantity, price, id }], email: string
        if (!items || !Array.isArray(items) || items.length === 0) {
            console.error('No items provided in request');
            return res.status(400).json({ error: 'No items provided' });
        }
        
        const line_items = items.map(item => ({
            price_data: {
                currency: 'php',
                product_data: {
                    name: item.name,
                },
                unit_amount: Math.round(item.price * 100),
            },
            quantity: item.quantity,
        }));
        
        console.log('Created line items for Stripe:', line_items);
        
        // Build session params
        const sessionParams = {
            payment_method_types: ['card'],
            line_items,
            mode: 'payment',
            success_url: `${req.headers.origin || 'http://localhost:3000'}/order-success`,
            cancel_url: `${req.headers.origin || 'http://localhost:3000'}/payment`,
            metadata: {
                cart: JSON.stringify(items),
            },
        };
        
        if (email && typeof email === 'string' && email.includes('@')) {
            sessionParams.customer_email = email;
            console.log('Adding customer email to session:', email);
        } else {
            console.log('No valid email provided, session will not have customer_email');
        }
        
        console.log('Creating Stripe session with params:', sessionParams);
        
        const session = await stripe.checkout.sessions.create(sessionParams);
        console.log('Stripe session created successfully:', session.id);
        
        res.json({ sessionId: session.id });
    } catch (error) {
        console.error('Error creating checkout session:', error);
        res.status(500).json({ error: 'Failed to create checkout session', message: error.message });
    }
});

app.post('/api/confirm-payment', async (req, res) => {
    try {
        const { paymentIntentId } = req.body;
        
        // Retrieve the payment intent to confirm it was successful
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
        
        if (paymentIntent.status === 'succeeded') {
            // Here you would typically save the order to your database
            res.json({ 
                success: true, 
                message: 'Payment successful',
                paymentIntent: paymentIntent
            });
        } else {
            res.status(400).json({ 
                success: false, 
                message: 'Payment failed',
                status: paymentIntent.status
            });
        }
    } catch (error) {
        console.error('Error confirming payment:', error);
        res.status(500).json({ 
            error: 'Failed to confirm payment',
            message: error.message 
        });
    }
});

// --- Theme Settings API ---
// GET current active theme
app.get('/api/theme/active', async (req, res) => {
    try {
        await poolConnect;
        // Ensure table exists
        await pool.request().query(`IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ThemeSettings' and xtype='U')
            CREATE TABLE ThemeSettings (
                ID INT PRIMARY KEY IDENTITY,
                ActiveTheme NVARCHAR(50) NOT NULL DEFAULT 'default'
            );`);
        // Ensure at least one row exists
        const result = await pool.request().query('SELECT TOP 1 * FROM ThemeSettings');
        let activeTheme = 'default';
        if (result.recordset.length === 0) {
            await pool.request().query(`INSERT INTO ThemeSettings (ActiveTheme) VALUES ('default')`);
        } else {
            activeTheme = result.recordset[0].ActiveTheme;
        }
        res.json({ activeTheme });
    } catch (err) {
        console.error('Theme GET error:', err);
        res.status(500).json({ error: 'Failed to fetch theme.', details: err.message });
    }
});

// POST set active theme
app.post('/api/theme/active', async (req, res) => {
    try {
        const { activeTheme } = req.body;
        if (!activeTheme) return res.status(400).json({ error: 'activeTheme is required.' });
        await poolConnect;
        // Ensure table exists
        await pool.request().query(`IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ThemeSettings' and xtype='U')
            CREATE TABLE ThemeSettings (
                ID INT PRIMARY KEY IDENTITY,
                ActiveTheme NVARCHAR(50) NOT NULL DEFAULT 'default'
            );`);
        // Ensure at least one row exists
        const result = await pool.request().query('SELECT TOP 1 * FROM ThemeSettings');
        if (result.recordset.length === 0) {
            await pool.request()
                .input('activeTheme', sql.NVarChar, activeTheme)
                .query(`INSERT INTO ThemeSettings (ActiveTheme) VALUES (@activeTheme)`);
        } else {
            await pool.request()
                .input('activeTheme', sql.NVarChar, activeTheme)
                .input('id', result.recordset[0].ID)
                .query(`UPDATE ThemeSettings SET ActiveTheme = @activeTheme WHERE ID = @id`);
        }
        res.json({ success: true, activeTheme });
    } catch (err) {
        console.error('Theme POST error:', err);
        res.status(500).json({ error: 'Failed to set theme.', details: err.message });
    }
});

// --- Customer-to-Support Chat API ---
app.get('/api/chat/messages', async (req, res) => {
    if (!req.session.user || req.session.user.role !== 'Customer') {
        return res.status(401).json({ success: false, message: 'Login required' });
    }
    await poolConnect;
    const customerId = req.session.user.id;
    try {
        const result = await pool.request()
            .input('customerId', sql.Int, customerId)
            .query(`
                SELECT * FROM ChatMessages
                WHERE CustomerID = @customerId
                ORDER BY SentAt ASC
            `);
        res.json({ success: true, messages: result.recordset });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to fetch messages', error: err.message });
    }
});

app.post('/api/chat/messages', async (req, res) => {
    if (!req.session.user || req.session.user.role !== 'Customer') {
        return res.status(401).json({ success: false, message: 'Login required' });
    }
    const { message } = req.body;
    if (!message || !message.trim()) {
        return res.status(400).json({ success: false, message: 'Message required' });
    }
    await poolConnect;
    const customerId = req.session.user.id;
    try {
        await pool.request()
            .input('customerId', sql.Int, customerId)
            .input('SenderType', sql.NVarChar, 'customer')
            .input('MessageText', sql.NVarChar, message)
            .query(`
                INSERT INTO ChatMessages (CustomerID, SenderType, MessageText)
                VALUES (@customerId, @SenderType, @MessageText)
            `);
        res.json({ success: true });
    } catch (err) {
        res.status(500).json({ success: false, message: 'Failed to send message', error: err.message });
    }
});

// --- Support Chat API for Admin/Employee ---
function isSupportUser(req) {
  return req.session.user && [
    'Admin', 'OrderSupport', 'InventoryManager', 'TransactionManager', 'UserManager'
  ].includes(req.session.user.role);
}

// Get all chat threads (one per customer, most recent at top)
app.get('/api/support/chat/threads', async (req, res) => {
  if (!isSupportUser(req)) return res.status(401).json({ success: false, message: 'Unauthorized' });
  await poolConnect;
  try {
    const result = await pool.request().query(`
      SELECT c.CustomerID, c.FullName, c.Email,
        MAX(m.SentAt) AS LastMessageAt,
        (SELECT TOP 1 MessageText FROM ChatMessages WHERE CustomerID = c.CustomerID ORDER BY SentAt DESC) AS LastMessageText,
        SUM(CASE WHEN m.SenderType = 'customer' AND m.IsRead = 0 THEN 1 ELSE 0 END) AS UnreadCount
      FROM Customers c
      LEFT JOIN ChatMessages m ON c.CustomerID = m.CustomerID
      WHERE EXISTS (SELECT 1 FROM ChatMessages WHERE CustomerID = c.CustomerID)
      GROUP BY c.CustomerID, c.FullName, c.Email
      ORDER BY LastMessageAt DESC
    `);
    res.json({ success: true, threads: result.recordset });
  } catch (err) {
    res.status(500).json({ success: false, message: 'Failed to fetch threads', error: err.message });
  }
});

// Get all messages for a customer
app.get('/api/support/chat/messages/:customerId', async (req, res) => {
  if (!isSupportUser(req)) return res.status(401).json({ success: false, message: 'Unauthorized' });
  await poolConnect;
  const customerId = parseInt(req.params.customerId);
  try {
    const result = await pool.request()
      .input('customerId', sql.Int, customerId)
      .query('SELECT * FROM ChatMessages WHERE CustomerID = @customerId ORDER BY SentAt ASC');
    res.json({ success: true, messages: result.recordset });
  } catch (err) {
    res.status(500).json({ success: false, message: 'Failed to fetch messages', error: err.message });
  }
});

// Send a reply as support
app.post('/api/support/chat/messages/:customerId', async (req, res) => {
  if (!isSupportUser(req)) return res.status(401).json({ success: false, message: 'Unauthorized' });
  const { message } = req.body;
  if (!message || !message.trim()) return res.status(400).json({ success: false, message: 'Message required' });
  await poolConnect;
  const customerId = parseInt(req.params.customerId);
  const supportUserId = req.session.user.id;
  try {
    await pool.request()
      .input('customerId', sql.Int, customerId)
      .input('supportUserId', sql.Int, supportUserId)
      .input('SenderType', sql.NVarChar, 'support')
      .input('MessageText', sql.NVarChar, message)
      .query('INSERT INTO ChatMessages (CustomerID, SupportUserID, SenderType, MessageText, IsRead) VALUES (@customerId, @supportUserId, @SenderType, @MessageText, 1)');
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ success: false, message: 'Failed to send message', error: err.message });
  }
});

// Mark all customer messages as read
app.post('/api/support/chat/mark-read/:customerId', async (req, res) => {
  if (!isSupportUser(req)) return res.status(401).json({ success: false, message: 'Unauthorized' });
  await poolConnect;
  const customerId = parseInt(req.params.customerId);
  try {
    await pool.request()
      .input('customerId', sql.Int, customerId)
      .query("UPDATE ChatMessages SET IsRead = 1 WHERE CustomerID = @customerId AND SenderType = 'customer' AND IsRead = 0");
    res.json({ success: true });
  } catch (err) {
    res.status(500).json({ success: false, message: 'Failed to mark as read', error: err.message });
  }
});

// Logout routes
app.get('/logout', (req, res) => {
    req.session.destroy();
    res.redirect('/login');
});

app.post('/logout', (req, res) => {
    req.session.destroy();
    res.json({ success: true });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});
