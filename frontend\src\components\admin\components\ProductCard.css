/* Product Card Component */
.product-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid #e1e8ed;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #F0B21B;
}

/* Grid View */
.product-card.grid-view {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.pc-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: #f8f9fa;
}

.pc-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .pc-image {
  transform: scale(1.05);
}

.pc-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f3f4;
  color: #666;
  font-size: 24px;
}

.pc-image-placeholder.large {
  flex-direction: column;
  gap: 8px;
}

.pc-placeholder-icon {
  font-size: 32px;
  opacity: 0.5;
}

.pc-placeholder-text {
  font-size: 14px;
  font-weight: 500;
}

.pc-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .pc-image-overlay {
  opacity: 1;
}

.pc-overlay-actions {
  display: flex;
  gap: 12px;
}

.pc-overlay-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: white;
  color: #333;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.pc-overlay-btn:hover {
  background: #F0B21B;
  color: white;
  transform: scale(1.1);
}

.pc-overlay-btn.pc-delete:hover {
  background: #ef4444;
}

.pc-status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
}

.pc-status-indicator {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pc-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pc-header {
  margin-bottom: 12px;
}

.pc-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pc-sku {
  margin: 0;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.pc-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pc-description {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pc-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.pc-category {
  padding: 4px 8px;
  background: #F0B21B20;
  color: #F0B21B;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #F0B21B40;
}

.pc-price {
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
}

.pc-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-top: 1px solid #e1e8ed;
  margin-top: auto;
}

.pc-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.pc-stat-icon {
  font-size: 14px;
}

.pc-stat-value {
  font-weight: 500;
}

.pc-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.pc-action-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  min-height: 36px;
}

.pc-edit-btn {
  background: #F0B21B;
  color: white;
}

.pc-edit-btn:hover {
  background: #d49e17;
  transform: translateY(-1px);
}

.pc-delete-btn {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e1e8ed;
}

.pc-delete-btn:hover {
  background: #fff5f5;
  color: #ef4444;
  border-color: #ef4444;
}

.pc-btn-icon {
  font-size: 14px;
}

/* List View */
.product-card.list-view {
  margin-bottom: 0;
}

.pc-list-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
}

.pc-list-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  flex-shrink: 0;
}

.pc-list-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pc-list-image .pc-image-placeholder {
  width: 100%;
  height: 100%;
  font-size: 16px;
}

.pc-list-info {
  flex: 1;
  display: flex;
  gap: 16px;
  align-items: center;
  min-width: 0;
}

.pc-list-main {
  flex: 1;
  min-width: 0;
}

.pc-list-main .pc-name {
  font-size: 16px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pc-list-main .pc-sku {
  margin-bottom: 4px;
}

.pc-list-main .pc-description {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
}

.pc-list-meta {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-shrink: 0;
}

.pc-list-meta .pc-category,
.pc-list-meta .pc-price,
.pc-list-meta .pc-status,
.pc-list-meta .pc-date {
  font-size: 13px;
  white-space: nowrap;
}

.pc-list-meta .pc-status {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-weight: 500;
}

.pc-list-meta .pc-date {
  color: #666;
  min-width: 100px;
}

.pc-list-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.pc-list-actions .pc-action-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 6px;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1199px) {
  .pc-list-meta {
    gap: 12px;
  }

  .pc-list-meta .pc-date {
    display: none;
  }
}

@media (max-width: 767px) {
  .pc-list-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .pc-list-image {
    align-self: center;
  }

  .pc-list-info {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .pc-list-meta {
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
  }

  .pc-list-actions {
    justify-content: center;
  }

  .pc-list-actions .pc-action-btn {
    width: 44px;
    height: 44px;
  }
}
