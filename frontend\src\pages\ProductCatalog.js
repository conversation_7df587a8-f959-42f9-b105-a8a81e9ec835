import React, { useState, useEffect } from 'react';
import { useLocation, Link } from 'react-router-dom';
import ProductCard from '../components/product/ProductCard';
import ProductFilter from '../components/product/ProductFilter';
import { getAllProducts, getCategories } from '../services/products';
import '../styles/pages.css';

const ProductCatalog = () => {
    const location = useLocation();
    const [products, setProducts] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState({
        category: '',
        priceRange: '',
        search: '',
        sortBy: 'name',
        featured: false,
        inStock: false,
        customizable: false
    });
    const [filteredProducts, setFilteredProducts] = useState([]);
    const [selectedCategoryName, setSelectedCategoryName] = useState('');

    // Parse URL parameters
    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const categoryParam = searchParams.get('category');
        const searchParam = searchParams.get('search');

        setFilters(prev => ({
            ...prev,
            category: categoryParam || '',
            search: searchParam || ''
        }));

        if (categoryParam) {
            setSelectedCategoryName(categoryParam);
        }
    }, [location.search]);

    useEffect(() => {
        loadData();
    }, []);

    useEffect(() => {
        applyFilters();
    }, [products, filters]);

    const loadData = async () => {
        try {
            const [productsResponse, categoriesResponse] = await Promise.all([
                getAllProducts(),
                getCategories()
            ]);
            const productsData = productsResponse.products || [];
            const categoriesData = categoriesResponse.categories || [];
            setProducts(productsData);
            setCategories([
                { id: '', name: 'All Products', count: productsData.length },
                ...categoriesData
            ]);
        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            setLoading(false);
        }
    };

    const applyFilters = () => {
        let filtered = [...products];

        // Category filter
        if (filters.category) {
            filtered = filtered.filter(product =>
                product.categoryId?.toString() === filters.category ||
                product.categoryName === filters.category
            );
        }

        // Search filter
        if (filters.search) {
            filtered = filtered.filter(product =>
                product.name.toLowerCase().includes(filters.search.toLowerCase()) ||
                product.description.toLowerCase().includes(filters.search.toLowerCase())
            );
        }

        // Price range filter
        if (filters.priceRange) {
            const [min, max] = filters.priceRange.split('-').map(Number);
            filtered = filtered.filter(product => {
                const price = product.discountPrice || product.price;
                if (max === 999999) {
                    // For "Over $2000" option
                    return price >= min;
                }
                return price >= min && price <= max;
            });
        }

        // Quick filters
        if (filters.featured) {
            filtered = filtered.filter(product => product.featured);
        }

        if (filters.inStock) {
            filtered = filtered.filter(product => product.stock > 0);
        }

        if (filters.customizable) {
            filtered = filtered.filter(product => product.customizable);
        }

        // Sort
        filtered.sort((a, b) => {
            const priceA = a.discountPrice || a.price;
            const priceB = b.discountPrice || b.price;

            switch (filters.sortBy) {
                case 'price-low':
                    return priceA - priceB;
                case 'price-high':
                    return priceB - priceA;
                case 'name':
                default:
                    return a.name.localeCompare(b.name);
            }
        });

        setFilteredProducts(filtered);
    };

    const handleFilterChange = (newFilters) => {
        setFilters({ ...filters, ...newFilters });
    };

    const clearAllFilters = () => {
        setFilters({
            category: '',
            priceRange: '',
            search: '',
            sortBy: 'name',
            featured: false,
            inStock: false,
            customizable: false
        });
    };

    if (loading) {
        return (
            <div className="catalog-page">
                <div className="container">
                    <div className="loading">
                        <div className="spinner"></div>
                        Loading products...
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="catalog-page">
            <div className="container">
                <div className="catalog-header">
                    <div className="breadcrumb">
                        <Link to="/">Home</Link> › <Link to="/products">Products</Link>
                        {selectedCategoryName && (
                            <> › <span>{selectedCategoryName}</span></>
                        )}
                    </div>
                    <h1>
                        {selectedCategoryName ? `${selectedCategoryName} Collection` : 'Product Catalog'}
                    </h1>
                    <p>
                        {selectedCategoryName
                            ? `Explore our premium ${selectedCategoryName.toLowerCase()} collection`
                            : 'Discover our complete collection of premium office furniture'
                        }
                    </p>
                    {selectedCategoryName && (
                        <div className="category-actions">
                            <Link to="/products" className="clear-filter-btn">
                                View All Products
                            </Link>
                        </div>
                    )}
                </div>

                <div className="catalog-content">
                    <aside className="catalog-sidebar">
                        <ProductFilter
                            categories={categories}
                            filters={filters}
                            onFilterChange={handleFilterChange}
                            onClearFilters={clearAllFilters}
                        />

                        {/* Quick Filters */}
                        <div className="quick-filters">
                            <h3>Quick Filters</h3>
                            <div className="filter-group">
                                <label>
                                    <input
                                        type="checkbox"
                                        checked={filters.featured}
                                        onChange={(e) => handleFilterChange({ featured: e.target.checked })}
                                    /> Featured Products
                                </label>
                                <label>
                                    <input
                                        type="checkbox"
                                        checked={filters.inStock}
                                        onChange={(e) => handleFilterChange({ inStock: e.target.checked })}
                                    /> In Stock Only
                                </label>
                                <label>
                                    <input
                                        type="checkbox"
                                        checked={filters.customizable}
                                        onChange={(e) => handleFilterChange({ customizable: e.target.checked })}
                                    /> 3D Customizable
                                </label>
                            </div>
                        </div>

                        {/* Price Range */}
                        <div className="price-range-section">
                            <h3>Price Range</h3>
                            <div className="price-range-options">
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value=""
                                        checked={filters.priceRange === ''}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    /> All Prices
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value="0-500"
                                        checked={filters.priceRange === '0-500'}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    /> Under $500
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value="500-1000"
                                        checked={filters.priceRange === '500-1000'}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    /> $500 - $1000
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value="1000-2000"
                                        checked={filters.priceRange === '1000-2000'}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    /> $1000 - $2000
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value="2000-999999"
                                        checked={filters.priceRange === '2000-999999'}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    /> Over $2000
                                </label>
                            </div>
                        </div>

                        {/* Clear All Filters */}
                        <div className="filter-actions-section">
                            <button
                                className="btn btn-secondary clear-all-btn"
                                onClick={clearAllFilters}
                            >
                                Clear All Filters
                            </button>
                        </div>

                        {/* Price Range */}
                        <div className="price-range-filter">
                            <h3>Price Range</h3>
                            <div className="price-options">
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value=""
                                        checked={filters.priceRange === ''}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    />
                                    All Prices
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value="0-500"
                                        checked={filters.priceRange === '0-500'}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    />
                                    Under $500
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value="500-1000"
                                        checked={filters.priceRange === '500-1000'}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    />
                                    $500 - $1000
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value="1000-2000"
                                        checked={filters.priceRange === '1000-2000'}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    />
                                    $1000 - $2000
                                </label>
                                <label>
                                    <input
                                        type="radio"
                                        name="priceRange"
                                        value="2000-"
                                        checked={filters.priceRange === '2000-'}
                                        onChange={(e) => handleFilterChange({ priceRange: e.target.value })}
                                    />
                                    Over $2000
                                </label>
                            </div>
                        </div>

                        {/* 3D Configurator Promo */}
                        <div className="configurator-promo">
                            <div className="promo-content">
                                <h3>3D Configurator</h3>
                                <p>Customize furniture with our innovative 3D tool and see your design come to life</p>
                                <button className="promo-btn">Learn More</button>
                            </div>
                        </div>
                    </aside>

                    <main className="catalog-main">
                        <div className="catalog-controls">
                            <div className="results-info">
                                <span>{filteredProducts.length} products found</span>
                            </div>

                            <div className="catalog-actions">
                                <div className="search-box">
                                    <input
                                        type="text"
                                        placeholder="Search products..."
                                        value={filters.search}
                                        onChange={(e) => handleFilterChange({ search: e.target.value })}
                                    />
                                </div>

                                <select
                                    value={filters.sortBy}
                                    onChange={(e) => handleFilterChange({ sortBy: e.target.value })}
                                    className="sort-select"
                                >
                                    <option value="name">Sort by Name</option>
                                    <option value="price-low">Price: Low to High</option>
                                    <option value="price-high">Price: High to Low</option>
                                </select>

                                <div className="view-toggle">
                                    <button className="view-btn active">⊞</button>
                                    <button className="view-btn">☰</button>
                                </div>
                            </div>
                        </div>

                        <div className="products-grid">
                            {filteredProducts.map(product => (
                                <ProductCard key={product.id} product={product} />
                            ))}
                        </div>

                        {filteredProducts.length === 0 && (
                            <div className="no-products">
                                <h3>No products found</h3>
                                <p>Try adjusting your filters or search terms</p>
                            </div>
                        )}
                    </main>
                </div>
            </div>
        </div>
    );
};

export default ProductCatalog;