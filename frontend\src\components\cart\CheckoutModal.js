import React from 'react';
import { Link } from 'react-router-dom';

const CheckoutModal = ({ isOpen, onClose, product, quantity = 1 }) => {
    if (!isOpen || !product) return null;

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(price);
    };

    const displayPrice = product.discountPrice || product.price;
    const totalPrice = displayPrice * quantity;

    return (
        <div className="checkout-modal-overlay" onClick={onClose}>
            <div className="checkout-modal-simple" onClick={(e) => e.stopPropagation()}>
                {/* Close Button */}
                <button className="checkout-modal-close" onClick={onClose} aria-label="Close">
                    ×
                </button>

                {/* Success Icon */}
                <div className="checkout-success-icon">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#F0B21B"/>
                        <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </div>

                {/* Title */}
                <h2 className="checkout-modal-title">Item Added to Cart!</h2>

                {/* Product Info */}
                <div className="checkout-product-card">
                    <img
                        src={product.images && product.images[0] ? product.images[0] : 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop'}
                        alt={product.name}
                        className="checkout-product-image"
                        onError={(e) => {
                            e.target.src = 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop';
                        }}
                    />
                    <div className="checkout-product-details">
                        <h3 className="checkout-product-name">{product.name}</h3>
                        <div className="checkout-product-pricing">
                            <span className="checkout-product-price">{formatPrice(displayPrice)}</span>
                            {quantity > 1 && (
                                <span className="checkout-product-quantity">Qty: {quantity}</span>
                            )}
                        </div>
                        <div className="checkout-product-total">Total: {formatPrice(totalPrice)}</div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="checkout-modal-buttons">
                    <button
                        className="checkout-btn checkout-btn-secondary"
                        onClick={onClose}
                    >
                        Continue Shopping
                    </button>
                    <Link
                        to="/cart"
                        className="checkout-btn checkout-btn-primary"
                        onClick={onClose}
                    >
                        View Cart
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default CheckoutModal;
