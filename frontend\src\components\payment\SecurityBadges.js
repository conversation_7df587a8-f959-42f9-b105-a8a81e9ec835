import React from 'react';

const SecurityBadges = () => {
    const securityFeatures = [
        {
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#10B981"/>
                    <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            title: 'SSL Secured',
            description: '256-bit encryption'
        },
        {
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="11" width="18" height="10" rx="2" ry="2" fill="#F0B21B"/>
                    <circle cx="12" cy="16" r="1" fill="white"/>
                    <path d="M7 11V7C7 4.79086 8.79086 3 11 3H13C15.2091 3 17 4.79086 17 7V11" stroke="#F0B21B" strokeWidth="2"/>
                </svg>
            ),
            title: 'PCI Compliant',
            description: 'Industry standard'
        },
        {
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" fill="#2c3e50"/>
                    <path d="M12 6V12L16 14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            title: '24/7 Monitoring',
            description: 'Fraud protection'
        }
    ];

    const trustBadges = [
        {
            name: 'PayMongo',
            logo: (
                <svg width="80" height="32" viewBox="0 0 80 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="80" height="32" rx="4" fill="#F0B21B"/>
                    <text x="40" y="20" textAnchor="middle" fill="white" fontSize="12" fontWeight="bold">PayMongo</text>
                </svg>
            ),
            description: 'Secure Payment Gateway'
        },
        {
            name: 'Verified',
            logo: (
                <svg width="80" height="32" viewBox="0 0 80 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="80" height="32" rx="4" fill="#10B981"/>
                    <path d="M25 16L30 21L45 11" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <text x="55" y="20" textAnchor="middle" fill="white" fontSize="10" fontWeight="bold">Verified</text>
                </svg>
            ),
            description: 'Trusted Merchant'
        }
    ];

    return (
        <div className="security-badges">
            <div className="security-section">
                <h4>Security & Trust</h4>
                <div className="security-features">
                    {securityFeatures.map((feature, index) => (
                        <div key={index} className="security-feature">
                            <div className="feature-icon">
                                {feature.icon}
                            </div>
                            <div className="feature-info">
                                <h5>{feature.title}</h5>
                                <p>{feature.description}</p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            <div className="trust-section">
                <h4>Trusted By</h4>
                <div className="trust-badges">
                    {trustBadges.map((badge, index) => (
                        <div key={index} className="trust-badge">
                            <div className="badge-logo">
                                {badge.logo}
                            </div>
                            <p className="badge-description">{badge.description}</p>
                        </div>
                    ))}
                </div>
            </div>

            <div className="guarantee-section">
                <div className="guarantee-badge">
                    <div className="guarantee-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#F0B21B"/>
                            <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    </div>
                    <div className="guarantee-text">
                        <h5>100% Secure</h5>
                        <p>Your payment information is encrypted and never stored</p>
                    </div>
                </div>
            </div>

            <div className="support-section">
                <h4>Need Help?</h4>
                <div className="support-options">
                    <div className="support-option">
                        <div className="support-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 16.92V19C22 20.1 21.1 21 20 21C10.61 21 3 13.39 3 4C3 2.9 3.9 2 5 2H7.08C7.6 2 8.05 2.37 8.14 2.88L9.07 8.35C9.15 8.8 8.95 9.26 8.55 9.52L6.84 10.83C8.1 13.1 10.9 15.9 13.17 17.16L14.48 15.45C14.74 15.05 15.2 14.85 15.65 14.93L21.12 15.86C21.63 15.95 22 16.4 22 16.92Z" fill="#F0B21B"/>
                            </svg>
                        </div>
                        <div className="support-info">
                            <h6>Call Support</h6>
                            <p>+63 2 8123 4567</p>
                        </div>
                    </div>
                    
                    <div className="support-option">
                        <div className="support-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="#F0B21B" strokeWidth="2" fill="none"/>
                                <path d="M22 6L12 13L2 6" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </div>
                        <div className="support-info">
                            <h6>Email Support</h6>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    
                    <div className="support-option">
                        <div className="support-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z" fill="#F0B21B"/>
                                <circle cx="9" cy="9" r="1" fill="white"/>
                                <circle cx="12" cy="9" r="1" fill="white"/>
                                <circle cx="15" cy="9" r="1" fill="white"/>
                            </svg>
                        </div>
                        <div className="support-info">
                            <h6>Live Chat</h6>
                            <p>Available 24/7</p>
                        </div>
                    </div>
                </div>
            </div>

            <div className="policy-links">
                <a href="/privacy-policy" className="policy-link">Privacy Policy</a>
                <a href="/terms-of-service" className="policy-link">Terms of Service</a>
                <a href="/refund-policy" className="policy-link">Refund Policy</a>
            </div>
        </div>
    );
};

export default SecurityBadges;
