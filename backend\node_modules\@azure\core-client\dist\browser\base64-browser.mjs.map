{"version": 3, "file": "base64-browser.mjs", "sourceRoot": "", "sources": ["../../src/base64-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,KAAiB;IAC/C,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,oBAAoB,CAAC,KAAa;IAChD,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;AACrB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\ndeclare global {\n  // stub these out for the browser\n  function btoa(input: string): string;\n  function atob(input: string): string;\n}\n\n/**\n * Encodes a string in base64 format.\n * @param value - the string to encode\n * @internal\n */\nexport function encodeString(value: string): string {\n  return btoa(value);\n}\n\n/**\n * Encodes a byte array in base64 format.\n * @param value - the Uint8Aray to encode\n * @internal\n */\nexport function encodeByteArray(value: Uint8Array): string {\n  let str = \"\";\n  for (let i = 0; i < value.length; i++) {\n    str += String.fromCharCode(value[i]);\n  }\n  return btoa(str);\n}\n\n/**\n * Decodes a base64 string into a byte array.\n * @param value - the base64 string to decode\n * @internal\n */\nexport function decodeString(value: string): Uint8Array {\n  const byteString = atob(value);\n  const arr = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    arr[i] = byteString.charCodeAt(i);\n  }\n  return arr;\n}\n\n/**\n * Decodes a base64 string into a string.\n * @param value - the base64 string to decode\n * @internal\n */\nexport function decodeStringToString(value: string): string {\n  return atob(value);\n}\n"]}