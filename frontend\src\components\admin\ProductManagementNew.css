/* Modern Product Management Styles */
.product-management-new {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.pm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pm-header-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.pm-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
}

.pm-header-stats {
  display: flex;
  gap: 16px;
}

.pm-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  background: #F0B21B20;
  border-radius: 8px;
  border: 1px solid #F0B21B40;
}

.pm-stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #F0B21B;
}

.pm-stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pm-header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pm-view-toggle {
  display: flex;
  background: #f1f3f4;
  border-radius: 8px;
  padding: 4px;
}

.pm-view-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  color: #666;
}

.pm-view-btn:hover {
  background: #e8eaed;
}

.pm-view-btn.active {
  background: #F0B21B;
  color: white;
  box-shadow: 0 2px 4px rgba(240, 178, 27, 0.3);
}

/* Buttons */
.pm-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  min-height: 44px;
  box-sizing: border-box;
}

.pm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pm-btn-primary {
  background: #F0B21B;
  color: white;
  box-shadow: 0 2px 8px rgba(240, 178, 27, 0.3);
}

.pm-btn-primary:hover:not(:disabled) {
  background: #d49e17;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.4);
}

.pm-btn-secondary {
  background: white;
  color: #666;
  border: 1px solid #e1e8ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pm-btn-secondary:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #F0B21B;
  color: #F0B21B;
}

.pm-btn-icon {
  font-size: 16px;
  font-weight: bold;
}

/* Form Container */
.pm-form-container {
  margin-bottom: 32px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 2px solid #F0B21B;
}

/* Content Area */
.pm-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Loading State */
.pm-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #666;
}

.pm-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f1f3f4;
  border-top: 4px solid #F0B21B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty State */
.pm-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.pm-empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.pm-empty h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 20px;
}

.pm-empty p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 16px;
}

/* Products Grid */
.pm-products {
  padding: 24px;
}

.pm-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.pm-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Pagination */
.pm-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-top: 1px solid #e1e8ed;
  background: #f8f9fa;
}

.pm-pagination-info {
  color: #666;
  font-size: 14px;
}

.pm-pagination-controls {
  display: flex;
  gap: 8px;
}

.pm-pagination-controls .pm-btn {
  padding: 8px 16px;
  min-height: 36px;
  font-size: 13px;
}

/* Responsive Design */
@media (max-width: 1199px) {
  .pm-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .pm-header-content {
    justify-content: center;
  }
  
  .pm-header-actions {
    justify-content: center;
  }
  
  .pm-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 767px) {
  .product-management-new {
    padding: 16px;
  }
  
  .pm-header {
    padding: 16px;
  }
  
  .pm-title {
    font-size: 24px;
  }
  
  .pm-header-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .pm-stat {
    flex-direction: row;
    gap: 8px;
    padding: 8px 12px;
  }
  
  .pm-stat-number {
    font-size: 16px;
  }
  
  .pm-stat-label {
    font-size: 11px;
  }
  
  .pm-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .pm-products {
    padding: 16px;
  }
  
  .pm-pagination {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .pm-pagination-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .pm-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* Touch targets for mobile */
@media (max-width: 767px) {
  .pm-btn {
    min-height: 44px;
  }
  
  .pm-view-btn {
    min-height: 44px;
    min-width: 44px;
  }
}
