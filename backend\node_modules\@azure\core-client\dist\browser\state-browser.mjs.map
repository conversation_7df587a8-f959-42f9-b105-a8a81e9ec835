{"version": 3, "file": "state-browser.mjs", "sourceRoot": "", "sources": ["../../src/state-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC;;GAEG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG;IACnB,mBAAmB,EAAE,IAAI,OAAO,EAA0C;CAC3E,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationRequest, OperationRequestInfo } from \"./interfaces.js\";\n\n/**\n * Browser-only implementation of the module's state. The browser esm variant will not load the commonjs state, so we do not need to share state between the two.\n */\nexport const state = {\n  operationRequestMap: new WeakMap<OperationRequest, OperationRequestInfo>(),\n};\n"]}