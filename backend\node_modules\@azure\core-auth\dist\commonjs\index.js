"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isTokenCredential = exports.isSASCredential = exports.AzureSASCredential = exports.isNamedKeyCredential = exports.AzureNamedKeyCredential = exports.isKeyCredential = exports.AzureKeyCredential = void 0;
var azureKeyCredential_js_1 = require("./azureKeyCredential.js");
Object.defineProperty(exports, "AzureKeyCredential", { enumerable: true, get: function () { return azureKeyCredential_js_1.AzureKeyCredential; } });
var keyCredential_js_1 = require("./keyCredential.js");
Object.defineProperty(exports, "isKeyCredential", { enumerable: true, get: function () { return keyCredential_js_1.isKeyCredential; } });
var azureNamedKeyCredential_js_1 = require("./azureNamedKeyCredential.js");
Object.defineProperty(exports, "AzureNamedKeyCredential", { enumerable: true, get: function () { return azureNamedKeyCredential_js_1.AzureNamedKeyCredential; } });
Object.defineProperty(exports, "isNamedKeyCredential", { enumerable: true, get: function () { return azureNamedKeyCredential_js_1.isNamedKeyCredential; } });
var azureSASCredential_js_1 = require("./azureSASCredential.js");
Object.defineProperty(exports, "AzureSASCredential", { enumerable: true, get: function () { return azureSASCredential_js_1.AzureSASCredential; } });
Object.defineProperty(exports, "isSASCredential", { enumerable: true, get: function () { return azureSASCredential_js_1.isSASCredential; } });
var tokenCredential_js_1 = require("./tokenCredential.js");
Object.defineProperty(exports, "isTokenCredential", { enumerable: true, get: function () { return tokenCredential_js_1.isTokenCredential; } });
//# sourceMappingURL=index.js.map