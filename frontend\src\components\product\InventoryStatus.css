.inventory-status {
  padding: 12px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  margin: 8px 0;
}

.inventory-status.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.inventory-status.error {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  background: #fef2f2;
  border-color: #fecaca;
}

.inventory-status.unavailable {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  background: #f9fafb;
  border-color: #d1d5db;
}

.inventory-status.variant {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stock-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
}

.stock-icon {
  font-size: 14px;
}

.stock-text {
  font-size: 14px;
}

.stock-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 13px;
  color: #64748b;
}

.stock-quantity {
  font-weight: 500;
}

.fulfillment-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.fulfillment-status.can-fulfill {
  background: #dcfce7;
  color: #166534;
}

.fulfillment-status.cannot-fulfill {
  background: #fef2f2;
  color: #dc2626;
}

.inventory-status.product {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-stock-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.summary-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.summary-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.low-stock-warning {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #fef3c7;
  border: 1px solid #fbbf24;
  border-radius: 6px;
  color: #92400e;
  font-size: 13px;
  font-weight: 500;
}

.warning-icon {
  font-size: 14px;
}

.variants-stock h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.variants-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.variant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.variant-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.variant-name {
  font-size: 13px;
  font-weight: 500;
  color: #1e293b;
}

.variant-sku {
  font-size: 11px;
  color: #64748b;
}

.variant-stock {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 600;
  font-size: 13px;
}

/* Responsive design */
@media (max-width: 768px) {
  .inventory-status {
    padding: 10px;
  }
  
  .product-stock-summary {
    gap: 12px;
  }
  
  .variant-item {
    padding: 6px 10px;
  }
  
  .variants-list {
    gap: 6px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .inventory-status {
    background: #1e293b;
    border-color: #334155;
    color: #e2e8f0;
  }
  
  .summary-value {
    color: #f1f5f9;
  }
  
  .variant-item {
    background: #334155;
    border-color: #475569;
  }
  
  .variant-name {
    color: #f1f5f9;
  }
}
