.payment-checkout {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    background: #fafafa;
    min-height: 100vh;
}

.checkout-header {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.checkout-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 1rem 0;
    text-align: center;
}

.order-summary-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.order-number {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.order-total {
    font-size: 1.25rem;
    font-weight: 700;
    color: #F0B21B;
}

.error-alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.error-icon {
    font-size: 1.25rem;
    color: #ef4444;
}

.error-text {
    color: #dc2626;
    font-weight: 500;
    font-size: 0.9rem;
}

.checkout-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ewallet-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 1.5rem;
}

.ewallet-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.ewallet-details h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 0.5rem 0;
}

.ewallet-details p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    line-height: 1.4;
}

.payment-actions {
    margin: 2rem 0 1.5rem 0;
}

.payment-button {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-button.enabled {
    background: #F0B21B;
    color: white;
    box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.payment-button.enabled:hover {
    background: #d4a017;
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(240, 178, 27, 0.4);
}

.payment-button.enabled:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(240, 178, 27, 0.3);
}

.payment-button.disabled {
    background: #e5e7eb;
    color: #9ca3af;
    cursor: not-allowed;
}

.processing-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.security-notice {
    text-align: center;
    margin-top: 1.5rem;
}

.security-badges {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
}

.security-badge {
    font-size: 0.8rem;
    color: #059669;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.security-text {
    font-size: 0.8rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .payment-checkout {
        padding: 1rem;
    }
    
    .checkout-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .checkout-title {
        font-size: 1.25rem;
    }
    
    .order-summary-compact {
        padding: 0.75rem;
    }
    
    .order-total {
        font-size: 1.1rem;
    }
    
    .checkout-content {
        padding: 1.5rem;
    }
    
    .ewallet-info {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .ewallet-icon {
        font-size: 2rem;
    }
    
    .ewallet-details h3 {
        font-size: 1rem;
    }
    
    .ewallet-details p {
        font-size: 0.85rem;
    }
    
    .payment-button {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
        min-height: 50px;
    }
    
    .security-badges {
        gap: 0.5rem;
    }
    
    .security-badge {
        font-size: 0.75rem;
    }
    
    .security-text {
        font-size: 0.75rem;
    }
}

/* Animation for successful payment */
.payment-success {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        background: #F0B21B;
    }
    50% {
        transform: scale(1.05);
        background: #059669;
    }
    100% {
        transform: scale(1);
        background: #059669;
    }
}

/* Loading state for the entire component */
.payment-checkout.loading {
    pointer-events: none;
    opacity: 0.8;
}

.payment-checkout.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Focus states for accessibility */
.payment-button:focus-visible {
    outline: 2px solid #F0B21B;
    outline-offset: 2px;
}
