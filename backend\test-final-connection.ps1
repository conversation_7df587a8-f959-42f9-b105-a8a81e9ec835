# Test final database connection

Write-Host "=== Final Database Connection Test ===" -ForegroundColor Green

# Test SQL Server authentication directly
try {
    Write-Host "Testing SQL Server authentication..." -ForegroundColor Cyan
    
    $connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;User Id=DesignXcel;Password=****************;TrustServerCertificate=True;Connection Timeout=10;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    $command = $connection.CreateCommand()
    $command.CommandText = @"
SELECT 
    DB_NAME() as DatabaseName, 
    USER_NAME() as UserName, 
    SYSTEM_USER as SystemUser,
    IS_MEMBER('db_owner') as IsDbOwner,
    @@VERSION as SqlVersion
"@
    $reader = $command.ExecuteReader()
    
    if ($reader.Read()) {
        Write-Host "✅ SQL Server authentication successful!" -ForegroundColor Green
        Write-Host "  Database: $($reader['DatabaseName'])" -ForegroundColor Green
        Write-Host "  User: $($reader['UserName'])" -ForegroundColor Green
        Write-Host "  System User: $($reader['SystemUser'])" -ForegroundColor Green
        Write-Host "  Is DB Owner: $($reader['IsDbOwner'])" -ForegroundColor Green
        
        $success = $true
    }
    
    $reader.Close()
    $connection.Close()
    
    if ($success) {
        Write-Host ""
        Write-Host "🎉 Database connection is working perfectly!" -ForegroundColor Green
        Write-Host "Your DesignXcel backend should now be able to connect to the database." -ForegroundColor Green
        
        # Test a simple table operation
        Write-Host ""
        Write-Host "Testing table operations..." -ForegroundColor Cyan
        
        $connection.Open()
        $command = $connection.CreateCommand()
        
        # Test creating a simple table
        $command.CommandText = @"
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ConnectionTest' and xtype='U')
CREATE TABLE ConnectionTest (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    TestMessage NVARCHAR(100),
    CreatedAt DATETIME DEFAULT GETDATE()
)
"@
        $command.ExecuteNonQuery() | Out-Null
        
        # Insert test data
        $command.CommandText = "INSERT INTO ConnectionTest (TestMessage) VALUES ('Database connection successful')"
        $command.ExecuteNonQuery() | Out-Null
        
        # Read test data
        $command.CommandText = "SELECT TOP 1 * FROM ConnectionTest ORDER BY ID DESC"
        $reader = $command.ExecuteReader()
        
        if ($reader.Read()) {
            Write-Host "✅ Table operations successful!" -ForegroundColor Green
            Write-Host "  Test ID: $($reader['ID'])" -ForegroundColor Green
            Write-Host "  Message: $($reader['TestMessage'])" -ForegroundColor Green
            Write-Host "  Created: $($reader['CreatedAt'])" -ForegroundColor Green
        }
        
        $reader.Close()
        
        # Clean up test table
        $command.CommandText = "DROP TABLE ConnectionTest"
        $command.ExecuteNonQuery() | Out-Null
        
        $connection.Close()
        
        Write-Host "✅ Database operations test completed successfully!" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ Connection failed: $($_.Exception.Message)" -ForegroundColor Red
    
    # Try to provide specific troubleshooting
    if ($_.Exception.Message -like "*Login failed*") {
        Write-Host ""
        Write-Host "💡 Login failed - trying to fix user permissions..." -ForegroundColor Yellow
        
        try {
            # Connect with Windows Auth to fix permissions
            $adminConnectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;Integrated Security=True;TrustServerCertificate=True;"
            $adminConnection = New-Object System.Data.SqlClient.SqlConnection($adminConnectionString)
            $adminConnection.Open()
            
            $adminCommand = $adminConnection.CreateCommand()
            $adminCommand.CommandText = @"
IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'DesignXcel')
BEGIN
    CREATE USER [DesignXcel] FOR LOGIN [DesignXcel];
END
ALTER ROLE [db_owner] ADD MEMBER [DesignXcel];
"@
            $adminCommand.ExecuteNonQuery() | Out-Null
            $adminConnection.Close()
            
            Write-Host "✅ User permissions fixed. Please run the test again." -ForegroundColor Green
            
        } catch {
            Write-Host "❌ Could not fix permissions: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
