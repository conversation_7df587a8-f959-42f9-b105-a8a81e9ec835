import type { HttpClient } from "@azure/core-rest-pipeline";
import type { RequestPolicy } from "./policies/requestPolicyFactoryPolicy.js";
/**
 * Converts a RequestPolicy based HttpClient to a PipelineRequest based HttpClient.
 * @param requestPolicyClient - A HttpClient compatible with core-http
 * @returns A HttpClient compatible with core-rest-pipeline
 */
export declare function convertHttpClient(requestPolicyClient: RequestPolicy): HttpClient;
//# sourceMappingURL=httpClientAdapter.d.ts.map