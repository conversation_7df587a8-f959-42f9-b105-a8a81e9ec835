# Frontend-Only Production Environment Configuration
# This file contains environment variables for frontend-only production deployment

# Frontend-Only Mode - No Backend Required
# REACT_APP_API_URL=# Not needed - frontend-only mode
# REACT_APP_API_VERSION=# Not needed - frontend-only mode
# REACT_APP_WEBSOCKET_URL=# Not needed - frontend-only mode

# Application Configuration
REACT_APP_NAME=Office Furniture E-commerce
REACT_APP_VERSION=1.0.0-frontend
REACT_APP_ENVIRONMENT=production

# Feature Flags (Frontend-Only Production Settings)
REACT_APP_ENABLE_3D_CONFIGURATOR=true
REACT_APP_ENABLE_REAL_TIME_UPDATES=false
REACT_APP_ENABLE_ADMIN_DASHBOARD=false
REACT_APP_ENABLE_PAYMENT_PROCESSING=true

# PayMongo Configuration (Production Public Keys) - Required for payment processing
REACT_APP_PAYMONGO_PUBLIC_KEY=pk_live_your_production_public_key_here

# Currency and Localization
REACT_APP_DEFAULT_CURRENCY=PHP
REACT_APP_DEFAULT_LANGUAGE=en
REACT_APP_SUPPORTED_CURRENCIES=PHP,USD
REACT_APP_SUPPORTED_LANGUAGES=en,fil

# Build Configuration
GENERATE_SOURCEMAP=false

# Production Configuration
REACT_APP_DEBUG_MODE=false
REACT_APP_LOG_LEVEL=error

# Static Asset Configuration
REACT_APP_ASSETS_URL=/
REACT_APP_MODELS_PATH=/models
REACT_APP_IMAGES_PATH=/images
REACT_APP_3D_MODELS_PATH=/models
