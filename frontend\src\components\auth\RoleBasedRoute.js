import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import UnauthorizedAccess from './UnauthorizedAccess';

/**
 * RoleBasedRoute Component
 * Protects routes based on user roles
 */
const RoleBasedRoute = ({ 
    children, 
    allowedRoles = [], 
    redirectTo = '/login',
    showUnauthorized = true 
}) => {
    const { user, isAuthenticated, loading } = useAuth();
    const location = useLocation();

    if (loading) {
        return (
            <div className="auth-loading">
                <div className="loading-spinner"></div>
                <p>Checking permissions...</p>
            </div>
        );
    }

    // Not authenticated - redirect to login
    if (!isAuthenticated) {
        return <Navigate to={redirectTo} state={{ from: location }} replace />;
    }

    // Check if user role is allowed
    const hasPermission = allowedRoles.length === 0 || allowedRoles.includes(user.role);

    if (!hasPermission) {
        if (showUnauthorized) {
            return <UnauthorizedAccess userRole={user.role} requiredRoles={allowedRoles} />;
        } else {
            return <Navigate to="/" replace />;
        }
    }

    return children;
};

export default RoleBasedRoute;
