{"version": 3, "file": "authorizeRequestOnTenantChallenge.js", "sourceRoot": "", "sources": ["../../src/authorizeRequestOnTenantChallenge.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAUlC;;GAEG;AACH,MAAM,SAAS,GAAG;IAChB,YAAY,EAAE,WAAW;IACzB;;OAEG;IACH,eAAe,EAAE;QACf;;WAEG;QACH,aAAa,EAAE,eAAe;KAC/B;CACF,CAAC;AAEF,SAAS,MAAM,CAAC,IAAY;IAC1B,OAAO,uFAAuF,CAAC,IAAI,CACjG,IAAI,CACL,CAAC;AACJ,CAAC;AAED;;;;IAII;AACJ,MAAM,CAAC,MAAM,iCAAiC,GAEtB,KAAK,EAAE,gBAAgB,EAAE,EAAE;;IACjD,MAAM,cAAc,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG,YAAY,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC1D,IAAI,SAAS,EAAE,CAAC;QACd,MAAM,aAAa,GAAc,cAAc,CAAC,SAAS,CAAC,CAAC;QAC3D,MAAM,eAAe,GAAG,WAAW,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QACrE,MAAM,QAAQ,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,KAAK,CAAC;QACf,CAAC;QACD,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,eAAe,kCACpE,cAAc,KACjB,QAAQ,IACR,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAClC,SAAS,CAAC,eAAe,CAAC,aAAa,EACvC,GAAG,MAAA,WAAW,CAAC,SAAS,mCAAI,QAAQ,IAAI,WAAW,CAAC,KAAK,EAAE,CAC5D,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF;;;;GAIG;AACH,SAAS,eAAe,CAAC,aAAwB;IAC/C,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;IAC/D,MAAM,YAAY,GAAG,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACvD,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IACjC,IAAI,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;QACjC,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;GAIG;AACH,SAAS,WAAW,CAClB,gBAAoD,EACpD,aAAwB;IAExB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;QAC/B,OAAO,gBAAgB,CAAC,MAAM,CAAC;IACjC,CAAC;IAED,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IAC3D,eAAe,CAAC,QAAQ,GAAG,SAAS,CAAC,YAAY,CAAC;IAClD,IAAI,KAAK,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC;IACvC,IAAI,KAAK,KAAK,iCAAiC,EAAE,CAAC;QAChD,6CAA6C;QAC7C,KAAK,GAAG,kCAAkC,CAAC;IAC7C,CAAC;IACD,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,CAAC;AAED;;;GAGG;AACH,SAAS,YAAY,CAAC,QAA0B;IAC9C,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC3D,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,SAAS,EAAE,CAAC;QACzC,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO;AACT,CAAC;AAUD;;;;;GAKG;AACH,SAAS,cAAc,CAAC,SAAiB;IACvC,MAAM,eAAe,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC1D,MAAM,cAAc,GAAG,GAAG,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAChF,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CACpD,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CACnE,CAAC;IACF,mCAAmC;IACnC,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,iCAAM,CAAC,GAAK,CAAC,EAAG,EAAE,EAAe,CAAC,CAAC;AAC3E,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,OAAwB;IAChD,OAAO;QACL,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,cAAc,EAAE;YACd,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB;QACD,cAAc,EAAE,OAAO,CAAC,cAAc;KACvC,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  AuthorizeRequestOnChallengeOptions,\n  PipelineRequest,\n  PipelineResponse,\n} from \"@azure/core-rest-pipeline\";\n\nimport type { GetTokenOptions } from \"@azure/core-auth\";\n\n/**\n * A set of constants used internally when processing requests.\n */\nconst Constants = {\n  DefaultScope: \"/.default\",\n  /**\n   * Defines constants for use with HTTP headers.\n   */\n  HeaderConstants: {\n    /**\n     * The Authorization header.\n     */\n    AUTHORIZATION: \"authorization\",\n  },\n};\n\nfunction isUuid(text: string): boolean {\n  return /^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/.test(\n    text,\n  );\n}\n\n/**\n * Defines a callback to handle auth challenge for Storage APIs.\n * This implements the bearer challenge process described here: https://learn.microsoft.com/rest/api/storageservices/authorize-with-azure-active-directory#bearer-challenge\n * Handling has specific features for storage that departs to the general AAD challenge docs.\n **/\nexport const authorizeRequestOnTenantChallenge: (\n  challengeOptions: AuthorizeRequestOnChallengeOptions,\n) => Promise<boolean> = async (challengeOptions) => {\n  const requestOptions = requestToOptions(challengeOptions.request);\n  const challenge = getChallenge(challengeOptions.response);\n  if (challenge) {\n    const challengeInfo: Challenge = parseChallenge(challenge);\n    const challengeScopes = buildScopes(challengeOptions, challengeInfo);\n    const tenantId = extractTenantId(challengeInfo);\n    if (!tenantId) {\n      return false;\n    }\n    const accessToken = await challengeOptions.getAccessToken(challengeScopes, {\n      ...requestOptions,\n      tenantId,\n    });\n\n    if (!accessToken) {\n      return false;\n    }\n\n    challengeOptions.request.headers.set(\n      Constants.HeaderConstants.AUTHORIZATION,\n      `${accessToken.tokenType ?? \"Bearer\"} ${accessToken.token}`,\n    );\n    return true;\n  }\n  return false;\n};\n\n/**\n * Extracts the tenant id from the challenge information\n * The tenant id is contained in the authorization_uri as the first\n * path part.\n */\nfunction extractTenantId(challengeInfo: Challenge): string | undefined {\n  const parsedAuthUri = new URL(challengeInfo.authorization_uri);\n  const pathSegments = parsedAuthUri.pathname.split(\"/\");\n  const tenantId = pathSegments[1];\n  if (tenantId && isUuid(tenantId)) {\n    return tenantId;\n  }\n  return undefined;\n}\n\n/**\n * Builds the authentication scopes based on the information that comes in the\n * challenge information. Scopes url is present in the resource_id, if it is empty\n * we keep using the original scopes.\n */\nfunction buildScopes(\n  challengeOptions: AuthorizeRequestOnChallengeOptions,\n  challengeInfo: Challenge,\n): string[] {\n  if (!challengeInfo.resource_id) {\n    return challengeOptions.scopes;\n  }\n\n  const challengeScopes = new URL(challengeInfo.resource_id);\n  challengeScopes.pathname = Constants.DefaultScope;\n  let scope = challengeScopes.toString();\n  if (scope === \"https://disk.azure.com/.default\") {\n    // the extra slash is required by the service\n    scope = \"https://disk.azure.com//.default\";\n  }\n  return [scope];\n}\n\n/**\n * We will retrieve the challenge only if the response status code was 401,\n * and if the response contained the header \"WWW-Authenticate\" with a non-empty value.\n */\nfunction getChallenge(response: PipelineResponse): string | undefined {\n  const challenge = response.headers.get(\"WWW-Authenticate\");\n  if (response.status === 401 && challenge) {\n    return challenge;\n  }\n  return;\n}\n\n/**\n * Challenge structure\n */\ninterface Challenge {\n  authorization_uri: string;\n  resource_id?: string;\n}\n\n/**\n * Converts: `Bearer a=\"b\" c=\"d\"`.\n * Into: `[ { a: 'b', c: 'd' }]`.\n *\n * @internal\n */\nfunction parseChallenge(challenge: string): Challenge {\n  const bearerChallenge = challenge.slice(\"Bearer \".length);\n  const challengeParts = `${bearerChallenge.trim()} `.split(\" \").filter((x) => x);\n  const keyValuePairs = challengeParts.map((keyValue) =>\n    (([key, value]) => ({ [key]: value }))(keyValue.trim().split(\"=\")),\n  );\n  // Key-value pairs to plain object:\n  return keyValuePairs.reduce((a, b) => ({ ...a, ...b }), {} as Challenge);\n}\n\n/**\n * Extracts the options form a Pipeline Request for later re-use\n */\nfunction requestToOptions(request: PipelineRequest): GetTokenOptions {\n  return {\n    abortSignal: request.abortSignal,\n    requestOptions: {\n      timeout: request.timeout,\n    },\n    tracingOptions: request.tracingOptions,\n  };\n}\n"]}