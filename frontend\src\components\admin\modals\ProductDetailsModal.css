/* Product Details Modal Styles */
.product-details-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

/* Modal Header */
.product-details-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e1e8ed;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-content h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Details Grid */
.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.detail-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e1e8ed;
}

.detail-section.full-width {
  grid-column: 1 / -1;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #F0B21B;
  padding-bottom: 8px;
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e1e8ed;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  font-weight: 500;
  color: #6c757d;
  font-size: 14px;
}

.detail-item span {
  color: #2c3e50;
  font-weight: 600;
  text-align: right;
}

/* Boolean Badge */
.boolean-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.boolean-badge.true {
  background: #d4edda;
  color: #155724;
}

.boolean-badge.false {
  background: #f8d7da;
  color: #721c24;
}

/* Description Content */
.description-content {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e1e8ed;
  line-height: 1.6;
  color: #2c3e50;
  min-height: 80px;
}

/* Files Section */
.files-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.file-category h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #F0B21B;
  padding-bottom: 8px;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.file-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  transition: all 0.2s ease;
}

.file-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 6px;
  border: 1px solid #e1e8ed;
}

.file-info {
  flex: 1;
}

.file-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 4px;
}

.file-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #6c757d;
}

.file-actions {
  display: flex;
  gap: 6px;
}

.btn-icon {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.btn-icon:hover {
  background: #F0B21B;
  transform: scale(1.1);
}

/* Images Grid */
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.image-card {
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
  overflow: hidden;
  transition: all 0.2s ease;
}

.image-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-preview {
  width: 100%;
  height: 150px;
  overflow: hidden;
  background: #e9ecef;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.image-card:hover .image-preview img {
  transform: scale(1.05);
}

.image-info {
  padding: 12px;
}

.image-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 13px;
  margin-bottom: 4px;
  word-break: break-word;
}

.image-details {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #6c757d;
}

/* No Files State */
.no-files,
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
  font-style: italic;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #e1e8ed;
}

/* Preview Container */
.preview-container {
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e1e8ed;
}

/* Components Table */
.components-table {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.components-table table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.components-table th,
.components-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e1e8ed;
}

.components-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

/* Colors Grid */
.colors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}

.color-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

.color-swatch {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid #e1e8ed;
  flex-shrink: 0;
}

.color-info {
  flex: 1;
}

.color-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 13px;
}

.color-code {
  font-size: 11px;
  color: #6c757d;
  font-family: monospace;
}

.price-modifier {
  font-size: 11px;
  color: #F0B21B;
  font-weight: 500;
}

/* Audit Timeline */
.audit-timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.audit-entry {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #F0B21B;
}

.audit-timestamp {
  font-size: 12px;
  color: #6c757d;
  min-width: 150px;
  font-weight: 500;
}

.audit-content {
  flex: 1;
}

.audit-action {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.audit-user {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.audit-details {
  font-size: 13px;
  color: #2c3e50;
  background: white;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e1e8ed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-details-modal {
    max-height: 95vh;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .files-grid {
    grid-template-columns: 1fr;
  }

  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .colors-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .audit-entry {
    flex-direction: column;
    gap: 8px;
  }

  .audit-timestamp {
    min-width: auto;
  }
}
