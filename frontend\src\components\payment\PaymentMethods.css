/* Payment Methods Component Styles */
.payment-methods {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.payment-methods-header {
    text-align: center;
    margin-bottom: 2rem;
}

.payment-methods-header h3 {
    color: #2c3e50;
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.payment-methods-header p {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* Payment Methods Grid */
.payment-methods-grid {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.payment-method-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.payment-method-card:hover {
    border-color: #F0B21B;
    box-shadow: 0 4px 20px rgba(240, 178, 27, 0.15);
    transform: translateY(-2px);
}

.payment-method-card.selected {
    border-color: #F0B21B;
    background: linear-gradient(135deg, rgba(240, 178, 27, 0.05) 0%, rgba(240, 178, 27, 0.02) 100%);
    box-shadow: 0 6px 25px rgba(240, 178, 27, 0.2);
}

/* Popular Badge */
.popular-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #F0B21B 0%, #d4a017 100%);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Recommended Badge */
.recommended-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

/* Method Header */
.method-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.method-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    background: rgba(240, 178, 27, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #F0B21B;
}

.method-info {
    flex: 1;
    min-width: 0;
}

.method-name {
    color: #2c3e50;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.method-description {
    color: #666;
    font-size: 0.875rem;
    margin: 0 0 0.75rem 0;
    line-height: 1.4;
}

.processing-time {
    color: #666;
    font-size: 0.75rem;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.fee-text {
    color: #28a745;
    font-weight: 600;
    font-size: 0.875rem;
}

.fee-text.free {
    color: #28a745;
}

.fee-text.bank-fee {
    color: #F0B21B;
}

.method-selector {
    flex-shrink: 0;
}

.radio-button {
    width: 24px;
    height: 24px;
    border: 2px solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #F0B21B;
}

.payment-method-card.selected .radio-button {
    border-color: #F0B21B;
    background: #F0B21B;
    color: white;
}

/* Method Features */
.method-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #555;
    font-size: 0.875rem;
}

.feature-item svg {
    flex-shrink: 0;
    color: #28a745;
}

/* Supported Banks */
.supported-banks {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.banks-title {
    color: #2c3e50;
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
}

.banks-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.bank-tag {
    background: rgba(240, 178, 27, 0.1);
    color: #B8860B;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.bank-tag.more {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Payment Security Notice */
.payment-security-notice {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.security-icon {
    flex-shrink: 0;
}

.security-text {
    flex: 1;
}

.security-text h4 {
    color: #065f46;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.security-text p {
    color: #047857;
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.4;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .payment-methods {
        padding: 0 1rem;
    }
    
    .payment-method-card {
        padding: 1rem;
    }
    
    .method-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .method-info {
        width: 100%;
    }
    
    .method-selector {
        align-self: flex-end;
    }
    
    .method-features {
        grid-template-columns: 1fr;
        gap: 0.375rem;
    }
    
    .popular-badge {
        position: static;
        align-self: flex-start;
        margin-bottom: 0.5rem;
    }
    
    .payment-security-notice {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
}
