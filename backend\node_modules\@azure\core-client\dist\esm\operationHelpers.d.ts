import type { OperationArguments, OperationParameter, OperationRequest, OperationRequestInfo } from "./interfaces.js";
/**
 * @internal
 * Retrieves the value to use for a given operation argument
 * @param operationArguments - The arguments passed from the generated client
 * @param parameter - The parameter description
 * @param fallbackObject - If something isn't found in the arguments bag, look here.
 *  Generally used to look at the service client properties.
 */
export declare function getOperationArgumentValueFromParameter(operationArguments: OperationArguments, parameter: OperationParameter, fallbackObject?: {
    [parameterName: string]: any;
}): any;
export declare function getOperationRequestInfo(request: OperationRequest): OperationRequestInfo;
//# sourceMappingURL=operationHelpers.d.ts.map