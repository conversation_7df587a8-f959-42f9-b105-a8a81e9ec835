const sql = require('mssql');
require('dotenv').config();

async function testDefaultInstance() {
    console.log('Testing connection to default SQL Server instance...\n');
    
    // Test connecting to the default instance (MSSQLSERVER) which we saw running
    const configs = [
        // Test 1: Default instance with Windows Auth
        {
            server: 'DESKTOP-F4OI6BT', // No instance name = default instance
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                trustedConnection: true
            }
        },
        
        // Test 2: Default instance with port
        {
            server: 'DESKTOP-F4OI6BT',
            port: 1433,
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                trustedConnection: true
            }
        },
        
        // Test 3: Localhost default instance
        {
            server: 'localhost',
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                trustedConnection: true
            }
        },
        
        // Test 4: 127.0.0.1 default instance
        {
            server: '127.0.0.1',
            port: 1433,
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                trustedConnection: true
            }
        }
    ];
    
    const descriptions = [
        'Default instance (DESKTOP-F4OI6BT) with Windows Auth',
        'Default instance with port 1433',
        'Localhost default instance',
        '127.0.0.1 with port 1433'
    ];
    
    let workingConfig = null;
    
    for (let i = 0; i < configs.length; i++) {
        console.log(`\n=== Testing ${descriptions[i]} ===`);
        
        let pool;
        try {
            pool = await sql.connect(configs[i]);
            console.log('✅ Connection successful!');
            
            const result = await pool.request().query(`
                SELECT 
                    @@VERSION as Version,
                    @@SERVERNAME as ServerName,
                    SERVERPROPERTY('InstanceName') as InstanceName,
                    SERVERPROPERTY('ServerName') as ServerNameProperty,
                    SERVERPROPERTY('IsIntegratedSecurityOnly') as WindowsAuthOnly,
                    DB_NAME() as DatabaseName
            `);
            
            if (result.recordset && result.recordset.length > 0) {
                const info = result.recordset[0];
                console.log(`Server Name: ${info.ServerName}`);
                console.log(`Instance: ${info.InstanceName || 'Default'}`);
                console.log(`Database: ${info.DatabaseName}`);
                console.log(`Windows Auth Only: ${info.WindowsAuthOnly ? 'Yes' : 'No'}`);
                
                workingConfig = { config: configs[i], description: descriptions[i], info };
            }
            
            break; // Stop on first successful connection
            
        } catch (err) {
            console.log('❌ Connection failed:', err.message);
        } finally {
            if (pool) {
                try {
                    await pool.close();
                } catch (closeErr) {
                    // Ignore close errors
                }
            }
        }
    }
    
    if (workingConfig) {
        console.log('\n🎉 Found working SQL Server connection!');
        console.log(`Working config: ${workingConfig.description}`);
        
        // Check authentication mode
        if (workingConfig.info.WindowsAuthOnly) {
            console.log('\n⚠️  SQL Server is configured for Windows Authentication only.');
            console.log('To use SQL Server authentication, you need to:');
            console.log('1. Open SQL Server Management Studio (SSMS)');
            console.log('2. Connect to the server');
            console.log('3. Right-click the server → Properties → Security');
            console.log('4. Change to "SQL Server and Windows Authentication mode"');
            console.log('5. Restart SQL Server service');
            console.log('\nAlternatively, we can modify your app to use Windows Authentication.');
        } else {
            console.log('\n✅ SQL Server supports mixed authentication mode.');
            await setupSqlUser(workingConfig.config);
        }
        
        // Update the .env file with working server configuration
        console.log('\n📝 Recommended .env configuration:');
        if (workingConfig.config.server === 'DESKTOP-F4OI6BT') {
            console.log('DB_SERVER=DESKTOP-F4OI6BT');
        } else {
            console.log(`DB_SERVER=${workingConfig.config.server}`);
        }
        if (workingConfig.config.port) {
            console.log(`DB_PORT=${workingConfig.config.port}`);
        }
        
    } else {
        console.log('\n❌ Could not connect to any SQL Server instance.');
        console.log('\n💡 Please check:');
        console.log('1. SQL Server services are running');
        console.log('2. TCP/IP protocol is enabled in SQL Server Configuration Manager');
        console.log('3. Windows Firewall allows SQL Server connections');
        console.log('4. Your Windows user has permission to connect to SQL Server');
    }
}

async function setupSqlUser(masterConfig) {
    console.log('\nAttempting to create SQL Server login and database...');
    
    let pool;
    try {
        pool = await sql.connect(masterConfig);
        
        // Check if database exists
        console.log('Checking if DesignXcelDB exists...');
        const dbCheck = await pool.request().query(`
            SELECT name FROM sys.databases WHERE name = 'DesignXcelDB'
        `);
        
        if (dbCheck.recordset.length === 0) {
            console.log('Creating DesignXcelDB database...');
            await pool.request().query(`CREATE DATABASE [DesignXcelDB]`);
            console.log('✅ Database created successfully.');
        } else {
            console.log('✅ Database already exists.');
        }
        
        // Check if login exists
        console.log('Checking if DesignXcel login exists...');
        const loginCheck = await pool.request().query(`
            SELECT name FROM sys.server_principals WHERE name = 'DesignXcel'
        `);
        
        if (loginCheck.recordset.length === 0) {
            console.log('Creating DesignXcel login...');
            await pool.request().query(`
                CREATE LOGIN [DesignXcel] 
                WITH PASSWORD = 'Azwrathfrozen22@', 
                DEFAULT_DATABASE = [DesignXcelDB], 
                CHECK_EXPIRATION = OFF, 
                CHECK_POLICY = OFF
            `);
            console.log('✅ Login created successfully.');
        } else {
            console.log('✅ Login already exists.');
        }
        
        // Create database user
        console.log('Setting up database user...');
        await pool.request().query(`
            USE [DesignXcelDB];
            IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'DesignXcel')
            BEGIN
                CREATE USER [DesignXcel] FOR LOGIN [DesignXcel];
                ALTER ROLE [db_owner] ADD MEMBER [DesignXcel];
            END
        `);
        console.log('✅ Database user configured successfully.');
        
        // Test the new login
        console.log('\nTesting new SQL Server login...');
        const testConfig = {
            ...masterConfig,
            user: 'DesignXcel',
            password: 'Azwrathfrozen22@',
            database: 'DesignXcelDB',
            options: {
                ...masterConfig.options,
                trustedConnection: false
            }
        };
        delete testConfig.options.trustedConnection;
        
        const testPool = await sql.connect(testConfig);
        const testResult = await testPool.request().query('SELECT DB_NAME() as DatabaseName, USER_NAME() as UserName');
        console.log('✅ SQL Server authentication test successful!');
        console.log(`Connected as: ${testResult.recordset[0].UserName}`);
        console.log(`Database: ${testResult.recordset[0].DatabaseName}`);
        await testPool.close();
        
    } catch (err) {
        console.error('❌ Error setting up SQL user:', err.message);
    } finally {
        if (pool) {
            await pool.close();
        }
    }
}

testDefaultInstance();
