import React, { useState, useRef, useEffect } from 'react';
import { useCurrency } from '../../contexts/CurrencyContext';
import { useLanguage } from '../../contexts/LanguageContext';

const CurrencyLanguageSelector = () => {
    const { currentCurrency, currencies, setCurrency } = useCurrency();
    const { currentLanguage, languages, setLanguage } = useLanguage();
    const [isCurrencyOpen, setIsCurrencyOpen] = useState(false);
    const [isLanguageOpen, setIsLanguageOpen] = useState(false);

    const currencyRef = useRef(null);
    const languageRef = useRef(null);

    // Get current currency and language info
    const getCurrentCurrency = () => {
        return currencies.find(c => c.code === currentCurrency) || currencies[0];
    };

    const getCurrentLanguage = () => {
        return languages.find(l => l.code === currentLanguage) || languages[0];
    };

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (currencyRef.current && !currencyRef.current.contains(event.target)) {
                setIsCurrencyOpen(false);
            }
            if (languageRef.current && !languageRef.current.contains(event.target)) {
                setIsLanguageOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleCurrencySelect = (currency) => {
        setCurrency(currency.code);
        setIsCurrencyOpen(false);
        console.log('Currency changed to:', currency.code);
    };

    const handleLanguageSelect = (language) => {
        setLanguage(language.code);
        setIsLanguageOpen(false);
        console.log('Language changed to:', language.code);
    };

    return (
        <div className="currency-language-selector">
            {/* Currency Selector */}
            <div className="selector-dropdown" ref={currencyRef}>
                <button 
                    className="selector-button"
                    onClick={() => {
                        setIsCurrencyOpen(!isCurrencyOpen);
                        setIsLanguageOpen(false);
                    }}
                >
                    <span>{getCurrentCurrency().symbol} {currentCurrency}</span>
                    <svg
                        width="12" 
                        height="12" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                        className={`dropdown-arrow ${isCurrencyOpen ? 'open' : ''}`}
                    >
                        <path 
                            d="M6 9L12 15L18 9" 
                            stroke="currentColor" 
                            strokeWidth="2" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                        />
                    </svg>
                </button>
                
                {isCurrencyOpen && (
                    <div className="selector-dropdown-menu">
                        {currencies.map((currency) => (
                            <button
                                key={currency.code}
                                className={`dropdown-item ${currentCurrency === currency.code ? 'selected' : ''}`}
                                onClick={() => handleCurrencySelect(currency)}
                            >
                                <span className="currency-code">{currency.code}</span>
                                <span className="currency-name">- {currency.name}</span>
                            </button>
                        ))}
                    </div>
                )}
            </div>

            {/* Language Selector */}
            <div className="selector-dropdown" ref={languageRef}>
                <button 
                    className="selector-button"
                    onClick={() => {
                        setIsLanguageOpen(!isLanguageOpen);
                        setIsCurrencyOpen(false);
                    }}
                >
                    <span>{getCurrentLanguage().name}</span>
                    <svg
                        width="12" 
                        height="12" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                        className={`dropdown-arrow ${isLanguageOpen ? 'open' : ''}`}
                    >
                        <path 
                            d="M6 9L12 15L18 9" 
                            stroke="currentColor" 
                            strokeWidth="2" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                        />
                    </svg>
                </button>
                
                {isLanguageOpen && (
                    <div className="selector-dropdown-menu">
                        {languages.map((language) => (
                            <button
                                key={language.code}
                                className={`dropdown-item ${currentLanguage === language.code ? 'selected' : ''}`}
                                onClick={() => handleLanguageSelect(language)}
                            >
                                {language.name}
                            </button>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default CurrencyLanguageSelector;
