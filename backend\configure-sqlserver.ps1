# PowerShell script to configure SQL Server for DesignXcel application

Write-Host "=== SQL Server Configuration for DesignXcel ===" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "⚠️  Warning: Not running as administrator. Some operations may fail." -ForegroundColor Yellow
    Write-Host "For best results, run PowerShell as Administrator." -ForegroundColor Yellow
    Write-Host ""
}

# Function to test SQL connection
function Test-SqlConnection {
    param(
        [string]$ServerInstance,
        [string]$Database = "master",
        [bool]$UseWindowsAuth = $true,
        [string]$Username = "",
        [string]$Password = ""
    )
    
    try {
        $connectionString = if ($UseWindowsAuth) {
            "Server=$ServerInstance;Database=$Database;Integrated Security=True;TrustServerCertificate=True;"
        } else {
            "Server=$ServerInstance;Database=$Database;User Id=$Username;Password=$Password;TrustServerCertificate=True;"
        }
        
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT @@SERVERNAME as ServerName, @@VERSION as Version, SERVERPROPERTY('IsIntegratedSecurityOnly') as WindowsAuthOnly"
        $reader = $command.ExecuteReader()
        
        if ($reader.Read()) {
            $result = @{
                Success = $true
                ServerName = $reader["ServerName"]
                WindowsAuthOnly = $reader["WindowsAuthOnly"]
                Version = $reader["Version"]
            }
        }
        $reader.Close()
        $connection.Close()
        return $result
    }
    catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
        }
    }
}

# Test different SQL Server instances
$instances = @(
    "DESKTOP-F4OI6BT\SQLEXPRESS",
    "DESKTOP-F4OI6BT",
    "localhost\SQLEXPRESS",
    "localhost",
    ".\SQLEXPRESS",
    "."
)

Write-Host "Testing SQL Server connections..." -ForegroundColor Cyan
$workingInstance = $null

foreach ($instance in $instances) {
    Write-Host "Testing: $instance" -NoNewline
    $result = Test-SqlConnection -ServerInstance $instance
    
    if ($result.Success) {
        Write-Host " ✅ SUCCESS" -ForegroundColor Green
        Write-Host "  Server: $($result.ServerName)"
        Write-Host "  Windows Auth Only: $($result.WindowsAuthOnly)"
        $workingInstance = @{
            Instance = $instance
            ServerName = $result.ServerName
            WindowsAuthOnly = $result.WindowsAuthOnly
        }
        break
    } else {
        Write-Host " ❌ Failed: $($result.Error)" -ForegroundColor Red
    }
}

if ($workingInstance) {
    Write-Host ""
    Write-Host "🎉 Found working SQL Server instance: $($workingInstance.Instance)" -ForegroundColor Green
    
    # Check authentication mode
    if ($workingInstance.WindowsAuthOnly) {
        Write-Host ""
        Write-Host "⚠️  SQL Server is configured for Windows Authentication only." -ForegroundColor Yellow
        Write-Host "To enable SQL Server authentication:" -ForegroundColor Yellow
        Write-Host "1. Open SQL Server Management Studio (SSMS)"
        Write-Host "2. Connect to: $($workingInstance.Instance)"
        Write-Host "3. Right-click server → Properties → Security"
        Write-Host "4. Select 'SQL Server and Windows Authentication mode'"
        Write-Host "5. Restart SQL Server service"
        Write-Host ""
        
        # Try to enable mixed mode authentication via registry (requires admin)
        if ($isAdmin) {
            Write-Host "Attempting to enable mixed mode authentication..." -ForegroundColor Cyan
            try {
                # Find the instance registry path
                $instanceName = if ($workingInstance.Instance -like "*\*") {
                    $workingInstance.Instance.Split('\')[1]
                } else {
                    "MSSQLSERVER"
                }
                
                $regPath = "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL16.$instanceName\MSSQLServer"
                Set-ItemProperty -Path $regPath -Name "LoginMode" -Value 2 -ErrorAction Stop
                Write-Host "✅ Mixed mode authentication enabled in registry." -ForegroundColor Green
                Write-Host "⚠️  SQL Server service restart required for changes to take effect." -ForegroundColor Yellow
                
                # Restart SQL Server service
                $serviceName = if ($instanceName -eq "MSSQLSERVER") { "MSSQLSERVER" } else { "MSSQL`$$instanceName" }
                Write-Host "Restarting SQL Server service: $serviceName" -ForegroundColor Cyan
                Restart-Service -Name $serviceName -Force -ErrorAction Stop
                Write-Host "✅ SQL Server service restarted." -ForegroundColor Green
                
                # Wait a moment for service to start
                Start-Sleep -Seconds 5
                
            } catch {
                Write-Host "❌ Failed to enable mixed mode: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "Please enable mixed mode authentication manually." -ForegroundColor Yellow
            }
        }
    }
    
    # Try to create database and user
    Write-Host ""
    Write-Host "Setting up DesignXcel database and user..." -ForegroundColor Cyan
    
    try {
        $connectionString = "Server=$($workingInstance.Instance);Database=master;Integrated Security=True;TrustServerCertificate=True;"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        # Create database if it doesn't exist
        $command = $connection.CreateCommand()
        $command.CommandText = "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'DesignXcelDB') CREATE DATABASE [DesignXcelDB]"
        $command.ExecuteNonQuery() | Out-Null
        Write-Host "✅ DesignXcelDB database ready." -ForegroundColor Green
        
        # Create login if it doesn't exist (only if mixed mode is enabled)
        if (-not $workingInstance.WindowsAuthOnly) {
            $command.CommandText = @"
IF NOT EXISTS (SELECT name FROM sys.server_principals WHERE name = 'DesignXcel')
BEGIN
    CREATE LOGIN [DesignXcel] WITH PASSWORD = 'Azwrathfrozen22@', 
    DEFAULT_DATABASE = [DesignXcelDB], 
    CHECK_EXPIRATION = OFF, 
    CHECK_POLICY = OFF
END
"@
            $command.ExecuteNonQuery() | Out-Null
            Write-Host "✅ DesignXcel login ready." -ForegroundColor Green
            
            # Create database user
            $command.CommandText = @"
USE [DesignXcelDB];
IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'DesignXcel')
BEGIN
    CREATE USER [DesignXcel] FOR LOGIN [DesignXcel];
    ALTER ROLE [db_owner] ADD MEMBER [DesignXcel];
END
"@
            $command.ExecuteNonQuery() | Out-Null
            Write-Host "✅ DesignXcel database user ready with db_owner permissions." -ForegroundColor Green
        }
        
        $connection.Close()
        
    } catch {
        Write-Host "❌ Error setting up database: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Update .env file
    Write-Host ""
    Write-Host "📝 Updating .env file..." -ForegroundColor Cyan
    
    $envPath = ".\.env"
    if (Test-Path $envPath) {
        $envContent = Get-Content $envPath
        $newEnvContent = @()
        
        foreach ($line in $envContent) {
            if ($line -like "DB_SERVER=*") {
                $newEnvContent += "DB_SERVER=$($workingInstance.Instance)"
            } elseif ($line -like "DB_PORT=*") {
                # Remove or comment out port for named instances
                if ($workingInstance.Instance -like "*\*") {
                    $newEnvContent += "# DB_PORT=1433  # Not needed for named instances"
                } else {
                    $newEnvContent += $line
                }
            } else {
                $newEnvContent += $line
            }
        }
        
        $newEnvContent | Set-Content $envPath
        Write-Host "✅ .env file updated with working server configuration." -ForegroundColor Green
    }
    
    # Test the final configuration
    Write-Host ""
    Write-Host "🧪 Testing final configuration..." -ForegroundColor Cyan
    
    if (-not $workingInstance.WindowsAuthOnly) {
        $testResult = Test-SqlConnection -ServerInstance $workingInstance.Instance -Database "DesignXcelDB" -UseWindowsAuth $false -Username "DesignXcel" -Password "Azwrathfrozen22@"
        
        if ($testResult.Success) {
            Write-Host "✅ SQL Server authentication test successful!" -ForegroundColor Green
            Write-Host ""
            Write-Host "🎉 Database connection is ready!" -ForegroundColor Green
            Write-Host "You can now start your DesignXcel backend application." -ForegroundColor Green
        } else {
            Write-Host "❌ SQL Server authentication test failed: $($testResult.Error)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  Windows Authentication only. Consider modifying your app to use Windows Auth." -ForegroundColor Yellow
    }
    
} else {
    Write-Host ""
    Write-Host "❌ Could not connect to any SQL Server instance." -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Ensure SQL Server Express is installed"
    Write-Host "2. Start SQL Server services:"
    Write-Host "   - SQL Server (SQLEXPRESS)"
    Write-Host "   - SQL Server Browser"
    Write-Host "3. Enable TCP/IP in SQL Server Configuration Manager"
    Write-Host "4. Check Windows Firewall settings"
}

Write-Host ""
Write-Host "=== Configuration Complete ===" -ForegroundColor Green
