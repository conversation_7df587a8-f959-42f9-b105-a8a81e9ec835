import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';

const AccountPreferences = () => {
    const { user } = useAuth();
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');
    const [preferences, setPreferences] = useState({
        // Communication Preferences
        emailNotifications: true,
        smsNotifications: false,
        marketingEmails: true,
        orderUpdates: true,
        promotionalOffers: false,
        
        // Display Preferences
        currency: 'PHP',
        language: 'en',
        timezone: 'Asia/Manila',
        theme: 'light',
        
        // Privacy Preferences
        profileVisibility: 'private',
        dataSharing: false,
        analyticsTracking: true,
        
        // Shopping Preferences
        savePaymentMethods: true,
        autoSaveAddresses: true,
        wishlistPublic: false,
        recommendationsEnabled: true
    });

    useEffect(() => {
        fetchPreferences();
    }, []);

    const fetchPreferences = async () => {
        setLoading(true);
        try {
            // Mock preferences data - replace with actual API call
            // In a real app, this would fetch user preferences from the backend
            setLoading(false);
        } catch (error) {
            console.error('Failed to fetch preferences:', error);
            setLoading(false);
        }
    };

    const handleChange = (category, key, value) => {
        setPreferences(prev => ({
            ...prev,
            [key]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setMessage('');

        try {
            // Mock API call - replace with actual API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            setMessage('Preferences updated successfully!');
        } catch (error) {
            setMessage('Failed to update preferences. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const PreferenceSection = ({ title, description, children }) => (
        <div className="preference-section">
            <div className="preference-header">
                <h3 className="preference-title">{title}</h3>
                <p className="preference-description">{description}</p>
            </div>
            <div className="preference-content">
                {children}
            </div>
        </div>
    );

    const ToggleSwitch = ({ label, description, checked, onChange }) => (
        <div className="preference-item">
            <div className="preference-info">
                <label className="preference-label">{label}</label>
                {description && <p className="preference-desc">{description}</p>}
            </div>
            <label className="toggle-switch">
                <input
                    type="checkbox"
                    checked={checked}
                    onChange={(e) => onChange(e.target.checked)}
                />
                <span className="toggle-slider"></span>
            </label>
        </div>
    );

    const SelectOption = ({ label, description, value, options, onChange }) => (
        <div className="preference-item">
            <div className="preference-info">
                <label className="preference-label">{label}</label>
                {description && <p className="preference-desc">{description}</p>}
            </div>
            <select
                className="preference-select"
                value={value}
                onChange={(e) => onChange(e.target.value)}
            >
                {options.map(option => (
                    <option key={option.value} value={option.value}>
                        {option.label}
                    </option>
                ))}
            </select>
        </div>
    );

    return (
        <div className="account-preferences">
            <div className="section-header">
                <div>
                    <h2 className="section-title">Account Preferences</h2>
                    <p className="section-subtitle">
                        Customize your account settings and preferences
                    </p>
                </div>
            </div>

            {message && (
                <div className={`message ${message.includes('success') ? 'success' : 'error'}`}>
                    {message}
                </div>
            )}

            <form onSubmit={handleSubmit} className="preferences-form">
                <PreferenceSection
                    title="Communication Preferences"
                    description="Choose how you want to receive updates and notifications"
                >
                    <ToggleSwitch
                        label="Email Notifications"
                        description="Receive important account updates via email"
                        checked={preferences.emailNotifications}
                        onChange={(value) => handleChange('communication', 'emailNotifications', value)}
                    />
                    <ToggleSwitch
                        label="SMS Notifications"
                        description="Receive order updates and alerts via SMS"
                        checked={preferences.smsNotifications}
                        onChange={(value) => handleChange('communication', 'smsNotifications', value)}
                    />
                    <ToggleSwitch
                        label="Marketing Emails"
                        description="Receive newsletters and product updates"
                        checked={preferences.marketingEmails}
                        onChange={(value) => handleChange('communication', 'marketingEmails', value)}
                    />
                    <ToggleSwitch
                        label="Order Updates"
                        description="Get notified about order status changes"
                        checked={preferences.orderUpdates}
                        onChange={(value) => handleChange('communication', 'orderUpdates', value)}
                    />
                    <ToggleSwitch
                        label="Promotional Offers"
                        description="Receive special deals and discount notifications"
                        checked={preferences.promotionalOffers}
                        onChange={(value) => handleChange('communication', 'promotionalOffers', value)}
                    />
                </PreferenceSection>

                <PreferenceSection
                    title="Display Preferences"
                    description="Customize how information is displayed"
                >
                    <SelectOption
                        label="Currency"
                        description="Choose your preferred currency for pricing"
                        value={preferences.currency}
                        options={[
                            { value: 'PHP', label: 'Philippine Peso (₱)' },
                            { value: 'USD', label: 'US Dollar ($)' },
                            { value: 'EUR', label: 'Euro (€)' },
                            { value: 'GBP', label: 'British Pound (£)' }
                        ]}
                        onChange={(value) => handleChange('display', 'currency', value)}
                    />
                    <SelectOption
                        label="Language"
                        description="Choose your preferred language"
                        value={preferences.language}
                        options={[
                            { value: 'en', label: 'English' },
                            { value: 'fil', label: 'Filipino' },
                            { value: 'es', label: 'Spanish' },
                            { value: 'zh', label: 'Chinese' }
                        ]}
                        onChange={(value) => handleChange('display', 'language', value)}
                    />
                    <SelectOption
                        label="Timezone"
                        description="Set your local timezone"
                        value={preferences.timezone}
                        options={[
                            { value: 'Asia/Manila', label: 'Manila (GMT+8)' },
                            { value: 'America/New_York', label: 'New York (GMT-5)' },
                            { value: 'Europe/London', label: 'London (GMT+0)' },
                            { value: 'Asia/Tokyo', label: 'Tokyo (GMT+9)' }
                        ]}
                        onChange={(value) => handleChange('display', 'timezone', value)}
                    />
                    <SelectOption
                        label="Theme"
                        description="Choose your preferred color theme"
                        value={preferences.theme}
                        options={[
                            { value: 'light', label: 'Light Theme' },
                            { value: 'dark', label: 'Dark Theme' },
                            { value: 'auto', label: 'Auto (System)' }
                        ]}
                        onChange={(value) => handleChange('display', 'theme', value)}
                    />
                </PreferenceSection>

                <PreferenceSection
                    title="Privacy Preferences"
                    description="Control your privacy and data sharing settings"
                >
                    <SelectOption
                        label="Profile Visibility"
                        description="Control who can see your profile information"
                        value={preferences.profileVisibility}
                        options={[
                            { value: 'private', label: 'Private' },
                            { value: 'friends', label: 'Friends Only' },
                            { value: 'public', label: 'Public' }
                        ]}
                        onChange={(value) => handleChange('privacy', 'profileVisibility', value)}
                    />
                    <ToggleSwitch
                        label="Data Sharing"
                        description="Allow sharing of anonymized data for research"
                        checked={preferences.dataSharing}
                        onChange={(value) => handleChange('privacy', 'dataSharing', value)}
                    />
                    <ToggleSwitch
                        label="Analytics Tracking"
                        description="Help improve our service with usage analytics"
                        checked={preferences.analyticsTracking}
                        onChange={(value) => handleChange('privacy', 'analyticsTracking', value)}
                    />
                </PreferenceSection>

                <PreferenceSection
                    title="Shopping Preferences"
                    description="Customize your shopping experience"
                >
                    <ToggleSwitch
                        label="Save Payment Methods"
                        description="Securely save payment methods for faster checkout"
                        checked={preferences.savePaymentMethods}
                        onChange={(value) => handleChange('shopping', 'savePaymentMethods', value)}
                    />
                    <ToggleSwitch
                        label="Auto-Save Addresses"
                        description="Automatically save new addresses during checkout"
                        checked={preferences.autoSaveAddresses}
                        onChange={(value) => handleChange('shopping', 'autoSaveAddresses', value)}
                    />
                    <ToggleSwitch
                        label="Public Wishlist"
                        description="Allow others to see your wishlist"
                        checked={preferences.wishlistPublic}
                        onChange={(value) => handleChange('shopping', 'wishlistPublic', value)}
                    />
                    <ToggleSwitch
                        label="Product Recommendations"
                        description="Show personalized product recommendations"
                        checked={preferences.recommendationsEnabled}
                        onChange={(value) => handleChange('shopping', 'recommendationsEnabled', value)}
                    />
                </PreferenceSection>

                <div className="form-actions">
                    <button
                        type="submit"
                        className="btn-primary"
                        disabled={loading}
                    >
                        {loading ? (
                            <>
                                <svg className="spinner" width="16" height="16" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" strokeDasharray="32" strokeDashoffset="32">
                                        <animate attributeName="stroke-dashoffset" dur="1s" values="32;0;32" repeatCount="indefinite"/>
                                    </circle>
                                </svg>
                                Saving Preferences...
                            </>
                        ) : (
                            <>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <polyline points="20,6 9,17 4,12"/>
                                </svg>
                                Save Preferences
                            </>
                        )}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default AccountPreferences;
