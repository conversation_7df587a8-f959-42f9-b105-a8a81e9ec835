USE DesignXcelDB;
GO

CREATE TABLE Roles (
    RoleID INT PRIMARY KEY IDENTITY,
    RoleName NVARCHAR(100) UNIQUE CHECK (RoleName IN (
        'InventoryManager', 'TransactionManager', 'UserManager', 'OrderSupport', 'Admin'
    ))
);

CREATE TABLE Users (
    UserID INT PRIMARY KEY IDENTITY,
    Username NVARCHAR(100) UNIQUE,
    PasswordHash NVARCHAR(255),
    FullName NVARCHAR(100),
    Email NVARCHAR(100),
    RoleID INT FOREIGN KEY REFERENCES Roles(RoleID) UNIQUE, -- Only one user per role
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME DEFAULT GETDATE()
);

INSERT INTO Roles (RoleName) VALUES 
('InventoryManager'),
('TransactionManager'),
('UserManager'),
('OrderSupport'),
('Admin');

-- Inventory Manager
INSERT INTO Users (Userna<PERSON>, PasswordHash, <PERSON>Name, Email, RoleID)
VALUES ('Jeff', 'inventory123', '<PERSON> Antonio', '<EMAIL>',
    (SELECT RoleID FROM Roles WHERE RoleName = 'InventoryManager'));

-- Transaction Manager
INSERT INTO Users (Username, PasswordHash, FullName, Email, RoleID)
VALUES ('Baste', 'transac123', 'Baste Solano', '<EMAIL>',
    (SELECT RoleID FROM Roles WHERE RoleName = 'TransactionManager'));

-- User Manager
INSERT INTO Users (Username, PasswordHash, FullName, Email, RoleID)
VALUES ('Eugene', 'userman123', 'Eugene Pantua', '<EMAIL>',
    (SELECT RoleID FROM Roles WHERE RoleName = 'UserManager'));

-- Order Support
INSERT INTO Users (Username, PasswordHash, FullName, Email, RoleID)
VALUES ('Gacs', 'ordersupp123', 'David Gacutan', '<EMAIL>',
    (SELECT RoleID FROM Roles WHERE RoleName = 'OrderSupport'));

-- Admin
INSERT INTO Users (Username, PasswordHash, FullName, Email, RoleID)
VALUES ('Drei', 'admin123', 'Andrei Jumawan', '<EMAIL>',
    (SELECT RoleID FROM Roles WHERE RoleName = 'Admin'));

CREATE TABLE Products (
    ProductID INT PRIMARY KEY IDENTITY,
    Name NVARCHAR(100),
    Description NVARCHAR(MAX),
    Price DECIMAL(10,2),
    StockQuantity INT,
    Category NVARCHAR(100),
    ImageURL NVARCHAR(255),
    DateAdded DATETIME DEFAULT GETDATE(),
    IsActive BIT DEFAULT 1
);

CREATE TABLE RawMaterials (
    MaterialID INT PRIMARY KEY IDENTITY,
    Name NVARCHAR(100),
    QuantityAvailable INT,
    Unit NVARCHAR(50),
    LastUpdated DATETIME DEFAULT GETDATE(),
    IsActive BIT DEFAULT 1
);

CREATE TABLE ProductMaterials (
    ProductMaterialID INT PRIMARY KEY IDENTITY,
    ProductID INT FOREIGN KEY REFERENCES Products(ProductID),
    MaterialID INT FOREIGN KEY REFERENCES RawMaterials(MaterialID),
    QuantityRequired INT
);

ALTER TABLE Products
ADD CONSTRAINT CHK_ProductCategory 
CHECK (Category IN ('Chairs', 'Tables', 'Storage', 'Partitions', 'Others'));

ALTER TABLE Products
ADD Dimensions NVARCHAR(50);

CREATE TABLE Customers (
    CustomerID INT PRIMARY KEY IDENTITY,
    FullName NVARCHAR(100),
    Email NVARCHAR(100) UNIQUE,
    PhoneNumber NVARCHAR(20),
    PasswordHash NVARCHAR(255),
    CreatedAt DATETIME DEFAULT GETDATE(),
    IsActive BIT DEFAULT 1
);

CREATE TABLE CustomerAddresses (
    AddressID INT PRIMARY KEY IDENTITY,
    CustomerID INT FOREIGN KEY REFERENCES Customers(CustomerID),
    Label NVARCHAR(50),
    HouseNumber NVARCHAR(50),
    Street NVARCHAR(100),
    Barangay NVARCHAR(100),
    City NVARCHAR(100),
    Province NVARCHAR(100),
    Region NVARCHAR(100),
    PostalCode NVARCHAR(20),
    Country NVARCHAR(100) DEFAULT 'Philippines',
    IsDefault BIT DEFAULT 0,
    CreatedAt DATETIME DEFAULT GETDATE()
);

CREATE TABLE ActivityLogs (
    LogID INT PRIMARY KEY IDENTITY,
    UserID INT FOREIGN KEY REFERENCES Users(UserID),
    Action NVARCHAR(50), -- e.g., 'INSERT', 'UPDATE', 'DELETE'
    TableAffected NVARCHAR(100),
    RecordID INT, -- ID of the affected record (optional but useful)
    Description NVARCHAR(MAX), -- Details of what changed
    Timestamp DATETIME DEFAULT GETDATE()
);

CREATE TABLE ProductStockAdjustments ( --For Automation of quantities
    AdjustmentID INT PRIMARY KEY IDENTITY,
    ProductID INT FOREIGN KEY REFERENCES Products(ProductID),
    QuantityAdded INT,
    Reason NVARCHAR(255), -- e.g., 'Production', 'Return', 'Manual Adjustment'
    PerformedBy INT FOREIGN KEY REFERENCES Users(UserID),
    Timestamp DATETIME DEFAULT GETDATE()
);

CREATE PROCEDURE AddProductStock
    @ProductID INT,
    @QuantityToAdd INT,
    @PerformedBy INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Validate inputs
    IF @QuantityToAdd <= 0
    BEGIN
        RAISERROR('QuantityToAdd must be greater than 0', 16, 1);
        RETURN;
    END;

    -- Start a transaction
    BEGIN TRANSACTION;

    -- Check material availability
    IF EXISTS (
        SELECT 1
        FROM ProductMaterials pm
        JOIN RawMaterials rm ON pm.MaterialID = rm.MaterialID
        WHERE pm.ProductID = @ProductID
        AND (pm.QuantityRequired * @QuantityToAdd) > rm.QuantityAvailable
    )
    BEGIN
        ROLLBACK;
        RAISERROR('Not enough raw materials available for production.', 16, 1);
        RETURN;
    END;

    -- Deduct raw materials
    UPDATE rm
    SET rm.QuantityAvailable = rm.QuantityAvailable - (pm.QuantityRequired * @QuantityToAdd),
        rm.LastUpdated = GETDATE()
    FROM RawMaterials rm
    JOIN ProductMaterials pm ON rm.MaterialID = pm.MaterialID
    WHERE pm.ProductID = @ProductID;

    -- Increase product stock
    UPDATE Products
    SET StockQuantity = StockQuantity + @QuantityToAdd
    WHERE ProductID = @ProductID;

    -- Log adjustment (optional)
    INSERT INTO ProductStockAdjustments (ProductID, QuantityAdded, Reason, PerformedBy)
    VALUES (@ProductID, @QuantityToAdd, 'Production', @PerformedBy);

    COMMIT;
END;

ALTER TABLE Products DROP CONSTRAINT CHK_ProductCategory;

-- FOR ROLE-BASED ACCESS
CREATE TABLE UserPermissions (
    PermissionID INT PRIMARY KEY IDENTITY,
    UserID INT FOREIGN KEY REFERENCES Users(UserID),
    Section NVARCHAR(50), -- e.g., 'inventory', 'transactions', 'users', 'chat', 'content', 'logs'
    CanAccess BIT DEFAULT 0
);
-- Add a unique constraint so each user can only have one row per section
ALTER TABLE UserPermissions ADD CONSTRAINT UQ_UserSection UNIQUE (UserID, Section);

INSERT INTO Customers (FullName, Email, PhoneNumber, PasswordHash)
VALUES (
    'Drei Jumawan',
    '<EMAIL>',
    '09090466955',
    '123'
);

-- First get the ID of the newly inserted customer
DECLARE @CustomerID INT = SCOPE_IDENTITY();

INSERT INTO CustomerAddresses (
    CustomerID, Label, HouseNumber, Street, Barangay, City, Province, Region, PostalCode, IsDefault
)
VALUES (
    @CustomerID,
    'Home',
    '238',
    'P. Dela cruz st.',
    'Barangay San Bartolome',
    'Quezon City',
    'Metro Manila',
    'NCR',
    '1116',
    1
);

CREATE TABLE Orders (
    OrderID INT PRIMARY KEY IDENTITY,
    CustomerID INT FOREIGN KEY REFERENCES Customers(CustomerID),
    OrderDate DATETIME DEFAULT GETDATE(),
    Status NVARCHAR(50), -- e.g., 'Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'
    TotalAmount DECIMAL(10,2),
    ShippingAddressID INT FOREIGN KEY REFERENCES CustomerAddresses(AddressID)
);

CREATE TABLE OrderItems (
    OrderItemID INT PRIMARY KEY IDENTITY,
    OrderID INT FOREIGN KEY REFERENCES Orders(OrderID),
    ProductID INT FOREIGN KEY REFERENCES Products(ProductID),
    Quantity INT,
    PriceAtPurchase DECIMAL(10,2) -- price of the product at the time of ordering
);	

ALTER TABLE Orders 
ADD PaymentMethod NVARCHAR(50),
    Currency NVARCHAR(10) DEFAULT 'PHP',
    PaymentDate DATETIME NULL;

ALTER TABLE Orders
ADD CONSTRAINT CHK_PaymentMethod
CHECK (PaymentMethod IN ('Cash on Delivery', 'E-Wallet', 'Bank Transfer'));



-- BEFORE RUNNING:
stripe listen --forward-to localhost:5000/api/stripe/webhook

-- CMS
CREATE TABLE CMSContent (
    ContentID INT PRIMARY KEY IDENTITY,
    Page NVARCHAR(100),         -- e.g., 'home'
    Section NVARCHAR(100),      -- e.g., 'banner'
    Content NVARCHAR(MAX),      -- JSON or plain text
    LastUpdated DATETIME DEFAULT GETDATE()
);

CREATE TABLE Testimonials (
    ID INT PRIMARY KEY IDENTITY,
    Name NVARCHAR(100) NOT NULL,
    Title NVARCHAR(100),
    Text NVARCHAR(MAX) NOT NULL,
    ImageUrl NVARCHAR(255),
    CreatedAt DATETIME DEFAULT GETDATE(),
	Rating INT NOT NULL DEFAULT 5
);

CREATE TABLE ChatMessages (
    MessageID INT PRIMARY KEY IDENTITY,
    CustomerID INT FOREIGN KEY REFERENCES Customers(CustomerID),
    SupportUserID INT NULL, -- For replies from support (Users table), can be NULL for customer messages
    SenderType NVARCHAR(20) NOT NULL CHECK (SenderType IN ('customer', 'support')),
    MessageText NVARCHAR(MAX) NOT NULL,
    SentAt DATETIME DEFAULT GETDATE(),
    IsRead BIT DEFAULT 0
);

CREATE TABLE HeaderOfferBar (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    OfferText NVARCHAR(500) NOT NULL,
    ButtonText NVARCHAR(100) NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'active', -- 'active', 'inactive', 'scheduled'
    BackgroundColor NVARCHAR(7) NOT NULL DEFAULT '#ffc107', -- Hex color code
    TextColor NVARCHAR(7) NOT NULL DEFAULT '#ffffff', -- Hex color code
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE()
);

-- Insert default header offer bar settings
INSERT INTO HeaderOfferBar (OfferText, ButtonText, StartDate, EndDate, Status, BackgroundColor, TextColor)
VALUES (
    'SPECIAL OFFER Get 25% off premium office furniture collections - Limited time offer ending soon!',
    'Shop Now',
    GETDATE(),
    DATEADD(month, 1, GETDATE()), -- End date 1 month from now
    'active',
    '#ffc107',
    '#ffffff'
);

-- Create index for better performance
CREATE INDEX IX_HeaderOfferBar_Status ON HeaderOfferBar(Status);
CREATE INDEX IX_HeaderOfferBar_Dates ON HeaderOfferBar(StartDate, EndDate);