{"version": 3, "file": "operationHelpers.js", "sourceRoot": "", "sources": ["../../src/operationHelpers.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAYlC,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AAEnC;;;;;;;GAOG;AACH,MAAM,UAAU,sCAAsC,CACpD,kBAAsC,EACtC,SAA6B,EAC7B,cAAiD;IAEjD,IAAI,aAAa,GAAG,SAAS,CAAC,aAAa,CAAC;IAC5C,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC;IACzC,IAAI,KAAU,CAAC;IACf,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;QACtC,aAAa,GAAG,CAAC,aAAa,CAAC,CAAC;IAClC,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;QACjC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;gBAC/B,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,IAAI,oBAAoB,GAAG,4BAA4B,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;gBAE3F,IAAI,CAAC,oBAAoB,CAAC,aAAa,IAAI,cAAc,EAAE,CAAC;oBAC1D,oBAAoB,GAAG,4BAA4B,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;gBACrF,CAAC;gBAED,IAAI,eAAe,GAAG,KAAK,CAAC;gBAC5B,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,CAAC;oBACxC,eAAe;wBACb,eAAe,CAAC,QAAQ;4BACxB,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;gBACnE,CAAC;gBACD,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,oBAAoB,CAAC,aAAa,CAAC;YAC9F,CAAC;QACH,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC7B,KAAK,GAAG,EAAE,CAAC;QACb,CAAC;QAED,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;YACzC,MAAM,cAAc,GAAY,eAAmC,CAAC,IAAI,CAAC,eAAgB,CACvF,YAAY,CACb,CAAC;YACF,MAAM,YAAY,GAAkB,aAAa,CAAC,YAAY,CAAC,CAAC;YAChE,MAAM,aAAa,GAAQ,sCAAsC,CAC/D,kBAAkB,EAClB;gBACE,aAAa,EAAE,YAAY;gBAC3B,MAAM,EAAE,cAAc;aACvB,EACD,cAAc,CACf,CAAC;YACF,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gBAChC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,KAAK,GAAG,EAAE,CAAC;gBACb,CAAC;gBACD,KAAK,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;YACtC,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAOD,SAAS,4BAA4B,CACnC,MAAwC,EACxC,aAAuB;IAEvB,MAAM,MAAM,GAAyB,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;IAC9D,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACrC,MAAM,iBAAiB,GAAW,aAAa,CAAC,CAAC,CAAC,CAAC;QACnD,8EAA8E;QAC9E,IAAI,MAAM,IAAI,iBAAiB,IAAI,MAAM,EAAE,CAAC;YAC1C,MAAM,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,MAAM;QACR,CAAC;IACH,CAAC;IACD,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,EAAE,CAAC;QAC/B,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC;QAC9B,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,qBAAqB,GAAG,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;AAEhF,SAAS,kBAAkB,CACzB,OAAyB;IAEzB,OAAO,qBAAqB,IAAI,OAAO,CAAC;AAC1C,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,OAAyB;IAC/D,IAAI,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;QAChC,OAAO,uBAAuB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,IAAI,GAAG,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAElD,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,IAAI,GAAG,EAAE,CAAC;QACV,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  CompositeM<PERSON><PERSON>,\n  Mapper,\n  OperationArguments,\n  OperationParameter,\n  OperationRequest,\n  OperationRequestInfo,\n  ParameterPath,\n} from \"./interfaces.js\";\n\nimport { state } from \"./state.js\";\n\n/**\n * @internal\n * Retrieves the value to use for a given operation argument\n * @param operationArguments - The arguments passed from the generated client\n * @param parameter - The parameter description\n * @param fallbackObject - If something isn't found in the arguments bag, look here.\n *  Generally used to look at the service client properties.\n */\nexport function getOperationArgumentValueFromParameter(\n  operationArguments: OperationArguments,\n  parameter: OperationParameter,\n  fallbackObject?: { [parameterName: string]: any },\n): any {\n  let parameterPath = parameter.parameterPath;\n  const parameterMapper = parameter.mapper;\n  let value: any;\n  if (typeof parameterPath === \"string\") {\n    parameterPath = [parameterPath];\n  }\n  if (Array.isArray(parameterPath)) {\n    if (parameterPath.length > 0) {\n      if (parameterMapper.isConstant) {\n        value = parameterMapper.defaultValue;\n      } else {\n        let propertySearchResult = getPropertyFromParameterPath(operationArguments, parameterPath);\n\n        if (!propertySearchResult.propertyFound && fallbackObject) {\n          propertySearchResult = getPropertyFromParameterPath(fallbackObject, parameterPath);\n        }\n\n        let useDefaultValue = false;\n        if (!propertySearchResult.propertyFound) {\n          useDefaultValue =\n            parameterMapper.required ||\n            (parameterPath[0] === \"options\" && parameterPath.length === 2);\n        }\n        value = useDefaultValue ? parameterMapper.defaultValue : propertySearchResult.propertyValue;\n      }\n    }\n  } else {\n    if (parameterMapper.required) {\n      value = {};\n    }\n\n    for (const propertyName in parameterPath) {\n      const propertyMapper: Mapper = (parameterMapper as CompositeMapper).type.modelProperties![\n        propertyName\n      ];\n      const propertyPath: ParameterPath = parameterPath[propertyName];\n      const propertyValue: any = getOperationArgumentValueFromParameter(\n        operationArguments,\n        {\n          parameterPath: propertyPath,\n          mapper: propertyMapper,\n        },\n        fallbackObject,\n      );\n      if (propertyValue !== undefined) {\n        if (!value) {\n          value = {};\n        }\n        value[propertyName] = propertyValue;\n      }\n    }\n  }\n  return value;\n}\n\ninterface PropertySearchResult {\n  propertyValue?: any;\n  propertyFound: boolean;\n}\n\nfunction getPropertyFromParameterPath(\n  parent: { [parameterName: string]: any },\n  parameterPath: string[],\n): PropertySearchResult {\n  const result: PropertySearchResult = { propertyFound: false };\n  let i = 0;\n  for (; i < parameterPath.length; ++i) {\n    const parameterPathPart: string = parameterPath[i];\n    // Make sure to check inherited properties too, so don't use hasOwnProperty().\n    if (parent && parameterPathPart in parent) {\n      parent = parent[parameterPathPart];\n    } else {\n      break;\n    }\n  }\n  if (i === parameterPath.length) {\n    result.propertyValue = parent;\n    result.propertyFound = true;\n  }\n  return result;\n}\n\nconst originalRequestSymbol = Symbol.for(\"@azure/core-client original request\");\n\nfunction hasOriginalRequest(\n  request: OperationRequest,\n): request is OperationRequest & { [originalRequestSymbol]: OperationRequest } {\n  return originalRequestSymbol in request;\n}\n\nexport function getOperationRequestInfo(request: OperationRequest): OperationRequestInfo {\n  if (hasOriginalRequest(request)) {\n    return getOperationRequestInfo(request[originalRequestSymbol]);\n  }\n  let info = state.operationRequestMap.get(request);\n\n  if (!info) {\n    info = {};\n    state.operationRequestMap.set(request, info);\n  }\n  return info;\n}\n"]}