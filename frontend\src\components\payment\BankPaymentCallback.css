/* Bank Payment Callback Styles */
.bank-payment-callback {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
}

.callback-container {
    max-width: 600px;
    width: 100%;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.callback-content {
    padding: 3rem 2rem;
    text-align: center;
}

.callback-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.callback-status h2 {
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
    color: #1f2937;
}

.callback-status p {
    font-size: 1.125rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.6;
}

/* Status Icons */
.success-icon,
.error-icon,
.pending-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.success-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.error-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.pending-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Processing Status */
.callback-status.processing {
    color: #6b7280;
}

.callback-status.processing h2 {
    color: #374151;
}

/* Success Status */
.callback-status.success {
    color: #10b981;
}

.callback-status.success h2 {
    color: #059669;
}

.redirect-notice {
    font-size: 0.875rem !important;
    color: #9ca3af !important;
    font-style: italic;
    margin-top: 1rem !important;
}

/* Failed Status */
.callback-status.failed {
    color: #ef4444;
}

.callback-status.failed h2 {
    color: #dc2626;
}

/* Pending Status */
.callback-status.pending {
    color: #f59e0b;
}

.callback-status.pending h2 {
    color: #d97706;
}

/* Error Status */
.callback-status.error {
    color: #ef4444;
}

.callback-status.error h2 {
    color: #dc2626;
}

/* Action Buttons */
.callback-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.callback-actions .btn {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
}

.callback-actions .btn-primary {
    background: linear-gradient(135deg, #F0B21B, #e6a617);
    color: white;
}

.callback-actions .btn-primary:hover {
    background: linear-gradient(135deg, #e6a617, #d99914);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(240, 178, 27, 0.3);
}

.callback-actions .btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.callback-actions .btn-secondary:hover {
    background: #e5e7eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading Animation */
.callback-status.processing .loading-spinner {
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bank-payment-callback {
        padding: 1rem;
    }

    .callback-content {
        padding: 2rem 1.5rem;
    }

    .callback-status h2 {
        font-size: 1.5rem;
    }

    .callback-status p {
        font-size: 1rem;
    }

    .success-icon,
    .error-icon,
    .pending-icon {
        width: 64px;
        height: 64px;
    }

    .success-icon svg,
    .error-icon svg,
    .pending-icon svg {
        width: 48px;
        height: 48px;
    }

    .callback-actions {
        flex-direction: column;
        align-items: center;
    }

    .callback-actions .btn {
        width: 100%;
        max-width: 280px;
    }
}

@media (max-width: 480px) {
    .callback-content {
        padding: 1.5rem 1rem;
    }

    .callback-status h2 {
        font-size: 1.25rem;
    }

    .callback-status p {
        font-size: 0.875rem;
    }
}

/* Animation for status changes */
.callback-status {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse animation for processing */
.callback-status.processing .success-icon,
.callback-status.processing .error-icon,
.callback-status.processing .pending-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}
