{"version": 3, "file": "authorizeRequestOnClaimChallenge.js", "sourceRoot": "", "sources": ["../../src/authorizeRequestOnClaimChallenge.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,MAAM,IAAI,gBAAgB,EAAE,MAAM,UAAU,CAAC;AACtD,OAAO,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAEnD;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,UAAkB;IAClD,MAAM,gBAAgB,GAAG,KAAK,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACtF,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;QACxC,MAAM,cAAc,GAAG,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7E,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CACpD,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CACpE,CAAC;QACF,mCAAmC;QACnC,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,iCAAM,CAAC,GAAK,CAAC,EAAG,EAAE,EAAE,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;AACL,CAAC;AAUD;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,MAAM,CAAC,KAAK,UAAU,gCAAgC,CACpD,kBAAsD;;IAEtD,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,kBAAkB,CAAC;IAChD,MAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,IAAI,gBAAgB,CAAC;IAE7D,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAC3D,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,CAAC,IAAI,CACT,kHAAkH,CACnH,CAAC;QACF,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,UAAU,GAAmB,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IAEtE,MAAM,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;IACzD,IAAI,CAAC,eAAe,EAAE,CAAC;QACrB,MAAM,CAAC,IAAI,CACT,iIAAiI,CAClI,CAAC;QACF,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,WAAW,GAAG,MAAM,kBAAkB,CAAC,cAAc,CACzD,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EACxD;QACE,MAAM,EAAE,oBAAoB,CAAC,eAAe,CAAC,MAAM,CAAC;KACrD,CACF,CAAC;IAEF,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CACpC,eAAe,EACf,GAAG,MAAA,WAAW,CAAC,SAAS,mCAAI,QAAQ,IAAI,WAAW,CAAC,KAAK,EAAE,CAC5D,CAAC;IACF,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorizeRequestOnChallengeOptions } from \"@azure/core-rest-pipeline\";\nimport { logger as coreClientLogger } from \"./log.js\";\nimport { decodeStringToString } from \"./base64.js\";\n\n/**\n * Converts: `Bearer a=\"b\", c=\"d\", Bearer d=\"e\", f=\"g\"`.\n * Into: `[ { a: 'b', c: 'd' }, { d: 'e', f: 'g' } ]`.\n *\n * @internal\n */\nexport function parseCAEChallenge(challenges: string): any[] {\n  const bearerChallenges = `, ${challenges.trim()}`.split(\", Bearer \").filter((x) => x);\n  return bearerChallenges.map((challenge) => {\n    const challengeParts = `${challenge.trim()}, `.split('\", ').filter((x) => x);\n    const keyValuePairs = challengeParts.map((keyValue) =>\n      (([key, value]) => ({ [key]: value }))(keyValue.trim().split('=\"')),\n    );\n    // Key-value pairs to plain object:\n    return keyValuePairs.reduce((a, b) => ({ ...a, ...b }), {});\n  });\n}\n\n/**\n * CAE Challenge structure\n */\nexport interface CAEChallenge {\n  scope: string;\n  claims: string;\n}\n\n/**\n * This function can be used as a callback for the `bearerTokenAuthenticationPolicy` of `@azure/core-rest-pipeline`, to support CAE challenges:\n * [Continuous Access Evaluation](https://learn.microsoft.com/azure/active-directory/conditional-access/concept-continuous-access-evaluation).\n *\n * Call the `bearerTokenAuthenticationPolicy` with the following options:\n *\n * ```ts snippet:AuthorizeRequestOnClaimChallenge\n * import { bearerTokenAuthenticationPolicy } from \"@azure/core-rest-pipeline\";\n * import { authorizeRequestOnClaimChallenge } from \"@azure/core-client\";\n *\n * const policy = bearerTokenAuthenticationPolicy({\n *   challengeCallbacks: {\n *     authorizeRequestOnChallenge: authorizeRequestOnClaimChallenge,\n *   },\n *   scopes: [\"https://service/.default\"],\n * });\n * ```\n *\n * Once provided, the `bearerTokenAuthenticationPolicy` policy will internally handle Continuous Access Evaluation (CAE) challenges.\n * When it can't complete a challenge it will return the 401 (unauthorized) response from ARM.\n *\n * Example challenge with claims:\n *\n * ```\n * Bearer authorization_uri=\"https://login.windows-ppe.net/\", error=\"invalid_token\",\n * error_description=\"User session has been revoked\",\n * claims=\"eyJhY2Nlc3NfdG9rZW4iOnsibmJmIjp7ImVzc2VudGlhbCI6dHJ1ZSwgInZhbHVlIjoiMTYwMzc0MjgwMCJ9fX0=\"\n * ```\n */\nexport async function authorizeRequestOnClaimChallenge(\n  onChallengeOptions: AuthorizeRequestOnChallengeOptions,\n): Promise<boolean> {\n  const { scopes, response } = onChallengeOptions;\n  const logger = onChallengeOptions.logger || coreClientLogger;\n\n  const challenge = response.headers.get(\"WWW-Authenticate\");\n  if (!challenge) {\n    logger.info(\n      `The WWW-Authenticate header was missing. Failed to perform the Continuous Access Evaluation authentication flow.`,\n    );\n    return false;\n  }\n  const challenges: CAEChallenge[] = parseCAEChallenge(challenge) || [];\n\n  const parsedChallenge = challenges.find((x) => x.claims);\n  if (!parsedChallenge) {\n    logger.info(\n      `The WWW-Authenticate header was missing the necessary \"claims\" to perform the Continuous Access Evaluation authentication flow.`,\n    );\n    return false;\n  }\n\n  const accessToken = await onChallengeOptions.getAccessToken(\n    parsedChallenge.scope ? [parsedChallenge.scope] : scopes,\n    {\n      claims: decodeStringToString(parsedChallenge.claims),\n    },\n  );\n\n  if (!accessToken) {\n    return false;\n  }\n\n  onChallengeOptions.request.headers.set(\n    \"Authorization\",\n    `${accessToken.tokenType ?? \"Bearer\"} ${accessToken.token}`,\n  );\n  return true;\n}\n"]}