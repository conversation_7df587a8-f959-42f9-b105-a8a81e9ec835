import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import UnauthorizedAccess from './UnauthorizedAccess';

/**
 * AdminRoute Component
 * Specifically for admin-only routes
 */
const AdminRoute = ({ children, allowEmployee = false }) => {
    const { user, isAuthenticated, loading } = useAuth();
    const location = useLocation();

    if (loading) {
        return (
            <div className="auth-loading">
                <div className="loading-spinner"></div>
                <p>Checking admin permissions...</p>
            </div>
        );
    }

    // Not authenticated - redirect to login
    if (!isAuthenticated) {
        return <Navigate to="/login" state={{ from: location }} replace />;
    }

    // Check admin permissions
    const isAdmin = user.role === 'Admin';
    const isEmployee = user.role === 'Employee';
    const hasPermission = isAdmin || (allowEmployee && isEmployee);

    if (!hasPermission) {
        return (
            <UnauthorizedAccess 
                userRole={user.role} 
                requiredRoles={allowEmployee ? ['Admin', 'Employee'] : ['Admin']}
                isAdminArea={true}
            />
        );
    }

    return children;
};

export default AdminRoute;
