.inventory-validation {
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
  border: 1px solid #e2e8f0;
}

.inventory-validation.empty-cart {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.inventory-validation.validating {
  background: #fef3c7;
  border-color: #fbbf24;
  display: flex;
  align-items: center;
  gap: 12px;
}

.inventory-validation.no-result {
  background: #fef3c7;
  border-color: #fbbf24;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.inventory-validation.valid {
  background: #dcfce7;
  border-color: #22c55e;
}

.inventory-validation.invalid {
  background: #fef2f2;
  border-color: #ef4444;
}

.validation-message {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  margin-bottom: 8px;
}

.validation-message.success {
  color: #166534;
  margin-bottom: 12px;
}

.validation-message.error {
  color: #dc2626;
  margin-bottom: 12px;
}

.success-icon {
  color: #22c55e;
  font-size: 16px;
}

.error-icon {
  color: #ef4444;
  font-size: 16px;
}

.warning-icon {
  color: #f59e0b;
  font-size: 16px;
}

.info-icon {
  color: #3b82f6;
  font-size: 16px;
}

.validation-spinner {
  display: flex;
  align-items: center;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #fbbf24;
  border-top: 2px solid #f59e0b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.validation-summary {
  display: flex;
  gap: 16px;
  margin: 12px 0;
  padding: 12px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
}

.validation-summary.error {
  background: rgba(239, 68, 68, 0.1);
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.summary-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.summary-value {
  font-size: 14px;
  font-weight: 600;
}

.summary-value.success {
  color: #22c55e;
}

.summary-value.error {
  color: #ef4444;
}

.validation-issues {
  margin: 12px 0;
}

.validation-issues h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #dc2626;
}

.issues-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.issue-item {
  padding: 12px;
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 6px;
}

.issue-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.issue-icon {
  color: #f59e0b;
  font-size: 14px;
}

.issue-product {
  font-weight: 600;
  color: #1e293b;
}

.issue-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.issue-message {
  font-size: 13px;
  color: #64748b;
}

.issue-quantities {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.requested {
  color: #dc2626;
  font-weight: 500;
}

.available {
  color: #22c55e;
  font-weight: 500;
}

.validation-actions {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.validate-btn,
.revalidate-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.validate-btn {
  background: #3b82f6;
  color: white;
}

.validate-btn:hover:not(:disabled) {
  background: #2563eb;
}

.revalidate-btn {
  background: #64748b;
  color: white;
  align-self: flex-start;
}

.revalidate-btn:hover:not(:disabled) {
  background: #475569;
}

.validate-btn:disabled,
.revalidate-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-note {
  font-size: 12px;
  color: #64748b;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .inventory-validation {
    padding: 12px;
  }
  
  .validation-summary {
    flex-direction: column;
    gap: 8px;
  }
  
  .inventory-validation.no-result {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .issue-quantities {
    flex-direction: column;
    gap: 4px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .inventory-validation {
    background: #1e293b;
    border-color: #334155;
    color: #e2e8f0;
  }
  
  .inventory-validation.valid {
    background: #064e3b;
    border-color: #059669;
  }
  
  .inventory-validation.invalid {
    background: #7f1d1d;
    border-color: #dc2626;
  }
  
  .validation-summary {
    background: rgba(0, 0, 0, 0.2);
  }
  
  .issue-item {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
  }
  
  .issue-product {
    color: #f1f5f9;
  }
}
