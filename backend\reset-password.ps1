# Reset password for DesignXcel login

Write-Host "=== Resetting DesignXcel Login Password ===" -ForegroundColor Green

try {
    # Connect with Windows Authentication to master
    $connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=master;Integrated Security=True;TrustServerCertificate=True;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to SQL Server" -ForegroundColor Green
    
    $command = $connection.CreateCommand()
    
    # Check login status
    Write-Host "Checking login status..." -ForegroundColor Cyan
    $command.CommandText = @"
SELECT 
    name, 
    is_disabled, 
    is_policy_checked, 
    is_expiration_checked,
    create_date,
    modify_date
FROM sys.server_principals 
WHERE name = 'DesignXcel'
"@
    $reader = $command.ExecuteReader()
    
    if ($reader.Read()) {
        Write-Host "Login Details:" -ForegroundColor Yellow
        Write-Host "  Name: $($reader['name'])" -ForegroundColor White
        Write-Host "  Disabled: $($reader['is_disabled'])" -ForegroundColor White
        Write-Host "  Policy Checked: $($reader['is_policy_checked'])" -ForegroundColor White
        Write-Host "  Expiration Checked: $($reader['is_expiration_checked'])" -ForegroundColor White
        Write-Host "  Created: $($reader['create_date'])" -ForegroundColor White
        Write-Host "  Modified: $($reader['modify_date'])" -ForegroundColor White
    }
    $reader.Close()
    
    # Reset the password
    Write-Host ""
    Write-Host "Resetting password..." -ForegroundColor Cyan
    $command.CommandText = @"
ALTER LOGIN [DesignXcel] WITH PASSWORD = '****************', 
CHECK_EXPIRATION = OFF, 
CHECK_POLICY = OFF
"@
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✅ Password reset successfully" -ForegroundColor Green
    
    # Enable the login if it's disabled
    $command.CommandText = "ALTER LOGIN [DesignXcel] ENABLE"
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✅ Login enabled" -ForegroundColor Green
    
    $connection.Close()
    
    # Test the connection immediately
    Write-Host ""
    Write-Host "Testing connection with reset password..." -ForegroundColor Cyan
    
    $testConnectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;User Id=DesignXcel;Password=****************;TrustServerCertificate=True;Connection Timeout=5;"
    $testConnection = New-Object System.Data.SqlClient.SqlConnection($testConnectionString)
    $testConnection.Open()
    
    $testCommand = $testConnection.CreateCommand()
    $testCommand.CommandText = @"
SELECT 
    DB_NAME() as DatabaseName, 
    USER_NAME() as UserName, 
    SYSTEM_USER as SystemUser,
    SUSER_NAME() as LoginName,
    IS_MEMBER('db_owner') as IsDbOwner,
    @@VERSION as SqlVersion
"@
    $testReader = $testCommand.ExecuteReader()
    
    if ($testReader.Read()) {
        Write-Host "✅ Connection test successful!" -ForegroundColor Green
        Write-Host "  Database: $($testReader['DatabaseName'])" -ForegroundColor Green
        Write-Host "  Database User: $($testReader['UserName'])" -ForegroundColor Green
        Write-Host "  System User: $($testReader['SystemUser'])" -ForegroundColor Green
        Write-Host "  Login Name: $($testReader['LoginName'])" -ForegroundColor Green
        Write-Host "  Is DB Owner: $($testReader['IsDbOwner'])" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "🎉 Database connection is now working!" -ForegroundColor Green
        Write-Host "Your DesignXcel backend application should now connect successfully." -ForegroundColor Green
        
        # Test a simple query
        $testReader.Close()
        $testCommand.CommandText = "SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
        $testReader = $testCommand.ExecuteReader()
        
        if ($testReader.Read()) {
            Write-Host "  Database has $($testReader['TableCount']) tables" -ForegroundColor Green
        }
        $testReader.Close()
        
    }
    
    $testConnection.Close()
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Additional troubleshooting
    if ($_.Exception.Message -like "*Login failed*") {
        Write-Host ""
        Write-Host "💡 Login still failing. Let's check SQL Server authentication mode..." -ForegroundColor Yellow
        
        try {
            $connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=master;Integrated Security=True;TrustServerCertificate=True;"
            $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
            $connection.Open()
            
            $command = $connection.CreateCommand()
            $command.CommandText = "SELECT SERVERPROPERTY('IsIntegratedSecurityOnly') as WindowsAuthOnly"
            $reader = $command.ExecuteReader()
            
            if ($reader.Read()) {
                $windowsOnly = $reader["WindowsAuthOnly"]
                if ($windowsOnly -eq 1) {
                    Write-Host "❌ SQL Server is configured for Windows Authentication only!" -ForegroundColor Red
                    Write-Host "You need to enable mixed mode authentication:" -ForegroundColor Yellow
                    Write-Host "1. Open SQL Server Management Studio" -ForegroundColor Yellow
                    Write-Host "2. Right-click server → Properties → Security" -ForegroundColor Yellow
                    Write-Host "3. Select 'SQL Server and Windows Authentication mode'" -ForegroundColor Yellow
                    Write-Host "4. Restart SQL Server service" -ForegroundColor Yellow
                } else {
                    Write-Host "✅ Mixed mode authentication is enabled" -ForegroundColor Green
                }
            }
            $reader.Close()
            $connection.Close()
            
        } catch {
            Write-Host "Could not check authentication mode: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "=== Password Reset Complete ===" -ForegroundColor Green
