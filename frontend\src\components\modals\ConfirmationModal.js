import React from 'react';

const ConfirmationModal = ({ 
    isOpen, 
    onClose, 
    onConfirm, 
    title = "Confirm Action",
    message = "Are you sure you want to proceed?",
    confirmText = "Confirm",
    cancelText = "Cancel",
    type = "warning" // warning, danger, info
}) => {
    if (!isOpen) return null;

    const handleConfirm = () => {
        onConfirm();
        onClose();
    };

    const getIconByType = () => {
        switch (type) {
            case 'danger':
                return (
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#fee2e2"/>
                        <path d="M15 9L9 15M9 9L15 15" stroke="#dc2626" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                );
            case 'info':
                return (
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#dbeafe"/>
                        <path d="M12 16V12M12 8H12.01" stroke="#2563eb" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                );
            default: // warning
                return (
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" fill="#fef3c7"/>
                        <path d="M12 9V13M12 17H12.01" stroke="#d97706" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                );
        }
    };

    return (
        <div className="confirmation-modal-overlay" onClick={onClose}>
            <div className="confirmation-modal" onClick={(e) => e.stopPropagation()}>
                {/* Close Button */}
                <button 
                    className="confirmation-modal-close" 
                    onClick={onClose}
                    aria-label="Close"
                >
                    ×
                </button>

                {/* Icon */}
                <div className="confirmation-modal-icon">
                    {getIconByType()}
                </div>

                {/* Content */}
                <div className="confirmation-modal-content">
                    <h3 className="confirmation-modal-title">{title}</h3>
                    <p className="confirmation-modal-message">{message}</p>
                </div>

                {/* Actions */}
                <div className="confirmation-modal-actions">
                    <button 
                        className="confirmation-btn confirmation-btn-cancel"
                        onClick={onClose}
                    >
                        {cancelText}
                    </button>
                    <button 
                        className={`confirmation-btn confirmation-btn-confirm confirmation-btn-${type}`}
                        onClick={handleConfirm}
                    >
                        {confirmText}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ConfirmationModal;
