import React, { useState, useEffect } from 'react';
import paymentService from '../../services/paymentService';
import './BankSelection.css';

const BankSelection = ({ selectedBank, onBankChange, fees, showFees = true }) => {
    const [banks, setBanks] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredBanks, setFilteredBanks] = useState([]);

    useEffect(() => {
        // Get supported banks from payment service
        const supportedBanks = paymentService.getSupportedBanks();
        setBanks(supportedBanks);
        setFilteredBanks(supportedBanks);
    }, []);

    useEffect(() => {
        // Filter banks based on search term
        if (searchTerm.trim() === '') {
            setFilteredBanks(banks);
        } else {
            const filtered = banks.filter(bank =>
                bank.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                bank.shortName.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredBanks(filtered);
        }
    }, [searchTerm, banks]);

    const handleBankSelect = (bank) => {
        onBankChange(bank.id);
    };

    const formatFee = (bankId) => {
        if (!fees || !showFees) return null;
        
        const bankFees = paymentService.getBankTransferFees(bankId);
        const totalFeePercentage = (bankFees.percentage * 100).toFixed(1);
        
        return (
            <div className="bank-fee-info">
                <span className="fee-percentage">{totalFeePercentage}%</span>
                {bankFees.fixed > 0 && (
                    <span className="fee-fixed">+ ₱{bankFees.fixed}</span>
                )}
            </div>
        );
    };

    const BankIcon = ({ bank }) => {
        // Create a simple colored icon based on bank color
        return (
            <div 
                className="bank-icon"
                style={{ backgroundColor: bank.color }}
            >
                <span className="bank-initial">
                    {bank.shortName.charAt(0)}
                </span>
            </div>
        );
    };

    return (
        <div className="bank-selection">
            <div className="bank-selection-header">
                <h3>Select Your Bank</h3>
                <p>Choose your preferred bank for online banking payment</p>
            </div>

            {/* Search Bar */}
            <div className="bank-search">
                <div className="search-input-container">
                    <svg 
                        className="search-icon" 
                        width="20" 
                        height="20" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <circle cx="11" cy="11" r="8" stroke="#666" strokeWidth="2"/>
                        <path d="m21 21-4.35-4.35" stroke="#666" strokeWidth="2" strokeLinecap="round"/>
                    </svg>
                    <input
                        type="text"
                        placeholder="Search banks..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="search-input"
                    />
                </div>
            </div>

            {/* Bank List */}
            <div className="bank-list">
                {filteredBanks.length === 0 ? (
                    <div className="no-banks-found">
                        <p>No banks found matching your search.</p>
                    </div>
                ) : (
                    filteredBanks.map((bank) => (
                        <div
                            key={bank.id}
                            className={`bank-option ${selectedBank === bank.id ? 'selected' : ''}`}
                            onClick={() => handleBankSelect(bank)}
                        >
                            <div className="bank-option-content">
                                <div className="bank-info">
                                    <BankIcon bank={bank} />
                                    <div className="bank-details">
                                        <h4 className="bank-name">{bank.shortName}</h4>
                                        <p className="bank-full-name">{bank.name}</p>
                                        <div className="bank-features">
                                            {bank.features.slice(0, 2).map((feature, index) => (
                                                <span key={index} className="feature-tag">
                                                    {feature}
                                                </span>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                                
                                <div className="bank-meta">
                                    {showFees && formatFee(bank.id)}
                                    <div className="processing-time">
                                        <span className="time-label">Processing:</span>
                                        <span className="time-value">{bank.processingTime}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Selection Indicator */}
                            <div className="selection-indicator">
                                {selectedBank === bank.id && (
                                    <svg 
                                        width="24" 
                                        height="24" 
                                        viewBox="0 0 24 24" 
                                        fill="none" 
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <circle cx="12" cy="12" r="12" fill="#F0B21B"/>
                                        <path 
                                            d="M8 12L11 15L16 9" 
                                            stroke="white" 
                                            strokeWidth="2" 
                                            strokeLinecap="round" 
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                )}
                            </div>
                        </div>
                    ))
                )}
            </div>

            {/* Selected Bank Summary */}
            {selectedBank && (
                <div className="selected-bank-summary">
                    {(() => {
                        const bank = banks.find(b => b.id === selectedBank);
                        if (!bank) return null;
                        
                        return (
                            <div className="summary-content">
                                <div className="summary-header">
                                    <BankIcon bank={bank} />
                                    <div>
                                        <h4>{bank.shortName} Selected</h4>
                                        <p>You will be redirected to {bank.shortName} online banking</p>
                                    </div>
                                </div>
                                
                                <div className="summary-details">
                                    <div className="detail-item">
                                        <span className="label">Processing Time:</span>
                                        <span className="value">{bank.processingTime}</span>
                                    </div>
                                    {showFees && (
                                        <div className="detail-item">
                                            <span className="label">Transaction Fee:</span>
                                            <span className="value">{formatFee(bank.id)}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        );
                    })()}
                </div>
            )}

            {/* Bank Payment Info */}
            <div className="bank-payment-info">
                <div className="info-header">
                    <svg 
                        width="20" 
                        height="20" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <circle cx="12" cy="12" r="10" stroke="#F0B21B" strokeWidth="2"/>
                        <path d="M12 6V12L16 14" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round"/>
                    </svg>
                    <h4>How Bank Transfer Works</h4>
                </div>
                
                <ol className="payment-steps">
                    <li>Select your preferred bank from the list above</li>
                    <li>You'll be redirected to your bank's secure online banking portal</li>
                    <li>Log in with your online banking credentials</li>
                    <li>Confirm the payment details and authorize the transfer</li>
                    <li>Return to our site to see your order confirmation</li>
                </ol>
                
                <div className="security-note">
                    <svg 
                        width="16" 
                        height="16" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path 
                            d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" 
                            fill="#28a745"
                        />
                        <path 
                            d="M9 12L11 14L15 10" 
                            stroke="white" 
                            strokeWidth="2" 
                            strokeLinecap="round" 
                            strokeLinejoin="round"
                        />
                    </svg>
                    <span>Your payment is secured by your bank's encryption and security protocols</span>
                </div>
            </div>
        </div>
    );
};

export default BankSelection;
