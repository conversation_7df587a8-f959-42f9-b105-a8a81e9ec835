import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import CartItem from './CartItem';

const CartSidebar = ({ isOpen, onClose }) => {
    const { items, getSubtotal, getTax, getShipping, getTotal, clearCart } = useCart();
    const [isClosing, setIsClosing] = useState(false);
    const [showClearConfirmation, setShowClearConfirmation] = useState(false);



    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP'
        }).format(price);
    };

    const subtotal = getSubtotal();
    const tax = getTax(subtotal);
    const shipping = getShipping(subtotal);
    const total = getTotal();

    // Handle smooth close animation
    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            onClose();
            setIsClosing(false);
        }, 300);
    };

    // Handle clear cart with confirmation
    const handleClearCart = () => {
        setShowClearConfirmation(true);
    };

    const confirmClearCart = () => {
        clearCart();
        setShowClearConfirmation(false);
    };

    // Close confirmation modal when cart closes
    useEffect(() => {
        if (!isOpen) {
            setShowClearConfirmation(false);
        }
    }, [isOpen]);

    // Prevent body scroll when cart is open and handle keyboard navigation
    useEffect(() => {
        if (isOpen) {
            document.body.style.overflow = 'hidden';

            // Handle escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    handleClose();
                }
            };

            document.addEventListener('keydown', handleEscape);

            return () => {
                document.body.style.overflow = 'unset';
                document.removeEventListener('keydown', handleEscape);
            };
        } else {
            document.body.style.overflow = 'unset';
        }
    }, [isOpen]);

    // Don't render anything if cart is not open and not closing
    if (!isOpen && !isClosing) {
        return null;
    }

    return (
        <>
            {/* Overlay */}
            {isOpen && (
                <div
                    className={`cart-overlay ${isClosing ? 'closing' : ''}`}
                    onClick={handleClose}
                    aria-hidden="true"
                />
            )}

            {/* Sidebar */}
            <div
                className={`cart-sidebar ${isOpen ? 'open' : ''} ${isClosing ? 'closing' : ''}`}
                role="dialog"
                aria-modal="true"
                aria-labelledby="cart-title"
                aria-describedby="cart-description"
            >
                <div className="cart-header">
                    <div className="cart-header-content">
                        <div className="cart-header-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M9 21C9.6 21 10 20.6 10 20S9.6 19 9 19 8 19.4 8 20 8.4 21 9 21ZM20 21C20.6 21 21 20.6 21 20S20.6 19 20 19 19 19.4 19 20 19.4 21 20 21Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </div>
                        <h2 id="cart-title">Shopping Cart</h2>
                        {items.length > 0 && (
                            <div className="cart-item-count">
                                <span>{items.length}</span>
                            </div>
                        )}
                    </div>
                    <button
                        className="cart-close-btn"
                        onClick={handleClose}
                        aria-label="Close shopping cart"
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="#808080" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    </button>
                </div>

                <div className="cart-content" id="cart-description">
                    {items.length === 0 ? (
                        <div className="cart-empty">
                            <div className="empty-cart-illustration">
                                <svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="60" cy="60" r="60" fill="#F8F9FA"/>
                                    <path d="M30 30H40L44 50M50 70H80L90 30H44M50 70L44 50M50 70L42 78C40 80 42 84 45 84H80M80 70V80C80 84 76 88 72 88H58C54 88 50 84 50 80V70M58 100C60 100 62 98 62 96S60 92 58 92 54 94 54 96 56 100 58 100ZM86 100C88 100 90 98 90 96S88 92 86 92 82 94 82 96 84 100 86 100Z" stroke="#E2E8F0" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
                                    <circle cx="60" cy="60" r="25" fill="#F0F0F0" opacity="0.5"/>
                                    <path d="M50 60H70M60 50V70" stroke="#C0C0C0" strokeWidth="2" strokeLinecap="round"/>
                                </svg>
                            </div>
                            <div className="empty-cart-content">
                                <h3>Your cart is empty</h3>
                                <p>Discover our premium office furniture collection and create your perfect workspace.</p>
                                <div className="empty-cart-actions">
                                    <Link
                                        to="/products"
                                        className="btn btn-primary btn-with-icon"
                                        onClick={handleClose}
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M16 11V7C16 4.8 14.2 3 12 3S8 4.8 8 7V11C6.9 11 6 11.9 6 13V19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V13C18 11.9 17.1 11 16 11ZM10 7C10 5.9 10.9 5 12 5S14 5.9 14 7V11H10V7Z" fill="currentColor"/>
                                        </svg>
                                        Browse Products
                                    </Link>
                                    <Link
                                        to="/"
                                        className="btn btn-secondary btn-with-icon"
                                        onClick={handleClose}
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 9L12 2L21 9V20C21 20.5 20.5 21 20 21H4C3.5 21 3 20.5 3 20V9Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                        Back to Home
                                    </Link>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <>
                            {/* Cart Items */}
                            <div className="cart-items">
                                {items.map(item => (
                                    <CartItem 
                                        key={item.id} 
                                        item={item} 
                                    />
                                ))}
                            </div>

                            {/* Cart Summary */}
                            <div className="cart-summary">
                                <div className="summary-header">
                                    <h3>Order Summary</h3>
                                </div>

                                <div className="summary-details">
                                    <div className="summary-row">
                                        <div className="summary-label">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9 12L11 14L15 10M21 12C21 16.9 16.9 21 12 21S3 16.9 3 12 7.1 3 12 3 21 7.1 21 12Z" stroke="#808080" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                            <span>Subtotal</span>
                                        </div>
                                        <span className="summary-value">{formatPrice(subtotal)}</span>
                                    </div>

                                    <div className="summary-row">
                                        <div className="summary-label">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M14 2H6C5.5 2 5 2.5 5 3V21L12 18L19 21V3C19 2.5 18.5 2 18 2H16" stroke="#808080" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M14 2V6H18" stroke="#808080" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                            <span>Tax (8%)</span>
                                        </div>
                                        <span className="summary-value">{formatPrice(tax)}</span>
                                    </div>

                                    <div className="summary-row">
                                        <div className="summary-label">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M16 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V8L16 3Z" stroke="#808080" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                <polyline points="16,3 16,8 21,8" stroke="#808080" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M12 12V16M10 14H14" stroke="#808080" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                            <span>Shipping</span>
                                        </div>
                                        <span className={`summary-value ${shipping === 0 ? 'free-shipping' : ''}`}>
                                            {shipping === 0 ? 'FREE' : formatPrice(shipping)}
                                        </span>
                                    </div>

                                    {shipping === 0 && (
                                        <div className="free-shipping-notice">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M9 12L11 14L15 10M21 12C21 16.9 16.9 21 12 21S3 16.9 3 12 7.1 3 12 3 21 7.1 21 12Z" stroke="#22C55E" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                            <span>Congratulations! You qualify for free shipping</span>
                                        </div>
                                    )}

                                    <div className="summary-divider"></div>

                                    <div className="summary-row total">
                                        <div className="summary-label">
                                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="12" cy="12" r="10" stroke="#F0B21B" strokeWidth="2"/>
                                                <path d="M12 6V12L16 14" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                            <span>Total</span>
                                        </div>
                                        <span className="summary-value total-amount">{formatPrice(total)}</span>
                                    </div>
                                </div>
                            </div>

                            {/* Cart Actions */}
                            <div className="cart-actions">
                                <Link
                                    to="/checkout"
                                    className="btn btn-primary btn-full btn-with-icon checkout-btn"
                                    onClick={handleClose}
                                >
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M9 12L11 14L15 10M21 12C21 16.9 16.9 21 12 21S3 16.9 3 12 7.1 3 12 3 21 7.1 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                    Proceed to Checkout
                                </Link>

                                <div className="cart-secondary-actions">
                                    <Link
                                        to="/cart"
                                        className="btn btn-secondary btn-with-icon"
                                        onClick={handleClose}
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                            <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="1.5"/>
                                        </svg>
                                        View Full Cart
                                    </Link>

                                    <button
                                        className="btn btn-text btn-with-icon clear-cart-btn"
                                        onClick={handleClearCart}
                                    >
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <polyline points="3,6 5,6 21,6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M19 6V20C19 21 18 22 17 22H7C6 22 5 21 5 20V6M8 6V4C8 3 9 2 10 2H14C15 2 16 3 16 4V6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                            <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                            <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                        Clear Cart
                                    </button>
                                </div>
                            </div>
                        </>
                    )}
                </div>

                {/* Clear Cart Confirmation Modal */}
                {showClearConfirmation && (
                    <div className="cart-confirmation-modal">
                        <div className="confirmation-content">
                            <div className="confirmation-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" stroke="#EF4444" strokeWidth="2"/>
                                    <line x1="15" y1="9" x2="9" y2="15" stroke="#EF4444" strokeWidth="2"/>
                                    <line x1="9" y1="9" x2="15" y2="15" stroke="#EF4444" strokeWidth="2"/>
                                </svg>
                            </div>
                            <h3>Clear Shopping Cart?</h3>
                            <p>Are you sure you want to remove all items from your cart? This action cannot be undone.</p>
                            <div className="confirmation-actions">
                                <button
                                    className="btn btn-secondary"
                                    onClick={() => setShowClearConfirmation(false)}
                                >
                                    Cancel
                                </button>
                                <button
                                    className="btn btn-danger btn-with-icon"
                                    onClick={confirmClearCart}
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <polyline points="3,6 5,6 21,6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                        <path d="M19 6V20C19 21 18 22 17 22H7C6 22 5 21 5 20V6M8 6V4C8 3 9 2 10 2H14C15 2 16 3 16 4V6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                    Clear Cart
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
};

export default CartSidebar;
