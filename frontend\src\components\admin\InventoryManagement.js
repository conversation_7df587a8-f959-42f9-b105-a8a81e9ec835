import React, { useState, useEffect } from 'react';
import { useWebSocket } from '../../hooks/useWebSocket';
import apiClient from '../../services/apiClient';
import websocketService from '../../services/websocketService';
import { toast } from 'react-toastify';
import './InventoryManagement.css';

const InventoryManagement = () => {
  const [inventoryItems, setInventoryItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [adjustmentData, setAdjustmentData] = useState({
    adjustment: '',
    reason: '',
    notes: ''
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    totalPages: 0
  });
  const { isConnected, showNotification } = useWebSocket();

  useEffect(() => {
    fetchInventoryData();
    setupWebSocketListeners();
  }, [pagination.currentPage, searchTerm, statusFilter]);

  const fetchInventoryData = async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams({
        page: pagination.currentPage.toString(),
        limit: pagination.itemsPerPage.toString(),
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      if (statusFilter && statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      const response = await apiClient.get(`/api/inventory?${params}`);

      if (response.success) {
        setInventoryItems(response.data.items);
        setPagination(response.data.pagination);
      } else {
        toast.error('Failed to fetch inventory data');
      }
    } catch (error) {
      console.error('Error fetching inventory data:', error);
      toast.error('Error loading inventory data');

      // Fallback to empty state
      setInventoryItems([]);
      setPagination({
        currentPage: 1,
        itemsPerPage: 10,
        totalItems: 0,
        totalPages: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const setupWebSocketListeners = () => {
    // Subscribe to inventory updates
    websocketService.subscribeToInventory();

    // Handle inventory updates
    const handleInventoryUpdate = (data) => {
      const { item, type } = data;

      // Update the item in the list
      setInventoryItems(prevItems =>
        prevItems.map(prevItem =>
          prevItem.variantId === item.variantId
            ? { ...prevItem, ...item }
            : prevItem
        )
      );

      // Show notification
      toast.info(`Inventory updated: ${item.productName}`, {
        position: "top-right",
        autoClose: 3000,
      });
    };

    // Handle low stock alerts
    const handleLowStockAlert = (data) => {
      const { item, alertLevel } = data;

      toast.warning(`${alertLevel}: ${item.productName} (${item.sku})`, {
        position: "top-right",
        autoClose: 5000,
      });
    };

    // Set up event listeners
    if (websocketService.socket) {
      websocketService.socket.on('inventory:updated', handleInventoryUpdate);
      websocketService.socket.on('inventory:low-stock-alert', handleLowStockAlert);
    }

    // Cleanup function
    return () => {
      if (websocketService.socket) {
        websocketService.socket.off('inventory:updated', handleInventoryUpdate);
        websocketService.socket.off('inventory:low-stock-alert', handleLowStockAlert);
      }
    };
  };

  const handleAdjustStock = (item) => {
    setSelectedItem(item);
    setShowAdjustModal(true);
    setAdjustmentData({ adjustment: '', reason: '', notes: '' });
  };

  const handleSubmitAdjustment = async () => {
    try {
      const adjustment = parseInt(adjustmentData.adjustment);

      if (isNaN(adjustment) || adjustment === 0) {
        toast.error('Please enter a valid adjustment amount');
        return;
      }

      const response = await apiClient.post('/api/inventory/adjust', {
        variantId: selectedItem.variantId,
        adjustment: adjustment,
        reason: adjustmentData.reason,
        notes: adjustmentData.notes
      });

      if (response.success) {
        // Update the item in the list
        setInventoryItems(prevItems =>
          prevItems.map(item =>
            item.variantId === selectedItem.variantId
              ? { ...item, ...response.data }
              : item
          )
        );

        setShowAdjustModal(false);
        setSelectedItem(null);
        setAdjustmentData({ adjustment: '', reason: '', notes: '' });

        toast.success('Inventory adjusted successfully');
      } else {
        toast.error(response.message || 'Failed to adjust inventory');
      }
    } catch (error) {
      console.error('Error adjusting inventory:', error);
      toast.error('Error adjusting inventory');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'in stock':
        return '#27ae60';
      case 'low stock':
        return '#f39c12';
      case 'critical':
        return '#e74c3c';
      case 'out of stock':
        return '#95a5a6';
      default:
        return '#95a5a6';
    }
  };

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.sku.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || item.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="inventory-loading">
        <div className="loading-spinner"></div>
        <p>Loading inventory...</p>
      </div>
    );
  }

  return (
    <div className="inventory-management">
      <div className="inventory-header">
        <div className="header-content">
          <h1>Inventory Management</h1>
          <p>Monitor and manage your product inventory levels</p>
        </div>
        <div className="realtime-status">
          <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
            {isConnected ? '🟢' : '🔴'}
          </span>
          <span className="status-text">
            {isConnected ? 'Real-time Updates Active' : 'Offline Mode'}
          </span>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="inventory-controls">
        <div className="search-container">
          <input
            type="text"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
        
        <div className="filter-container">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Status</option>
            <option value="in stock">In Stock</option>
            <option value="low stock">Low Stock</option>
            <option value="critical">Critical</option>
            <option value="out of stock">Out of Stock</option>
          </select>
        </div>

        <button className="admin-btn admin-btn-primary">
          Export Report
        </button>
      </div>

      {/* Inventory Table */}
      <div className="admin-card">
        <div className="table-container">
          <table className="admin-table">
            <thead>
              <tr>
                <th>Product</th>
                <th>SKU</th>
                <th>Current Stock</th>
                <th>Available</th>
                <th>Reserved</th>
                <th>Reorder Level</th>
                <th>Unit Cost</th>
                <th>Total Value</th>
                <th>Status</th>
                <th>Location</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredItems.map(item => (
                <tr key={item.id}>
                  <td>
                    <div className="product-cell">
                      <strong>{item.productName}</strong>
                    </div>
                  </td>
                  <td>{item.sku}</td>
                  <td>
                    <span className="stock-number">{item.currentStock}</span>
                  </td>
                  <td>{item.availableStock}</td>
                  <td>{item.reservedStock}</td>
                  <td>{item.reorderLevel}</td>
                  <td>{formatCurrency(item.unitCost)}</td>
                  <td>{formatCurrency(item.totalValue)}</td>
                  <td>
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(item.status) }}
                    >
                      {item.status}
                    </span>
                  </td>
                  <td>{item.location}</td>
                  <td>
                    <button
                      className="admin-btn admin-btn-secondary btn-small"
                      onClick={() => handleAdjustStock(item)}
                    >
                      Adjust
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Adjustment Modal */}
      {showAdjustModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Adjust Stock - {selectedItem?.productName}</h3>
              <button 
                className="modal-close"
                onClick={() => setShowAdjustModal(false)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              <div className="current-stock-info">
                <p><strong>Current Stock:</strong> {selectedItem?.currentStock}</p>
                <p><strong>Available:</strong> {selectedItem?.availableStock}</p>
              </div>

              <div className="admin-form-group">
                <label className="admin-form-label">Adjustment Amount</label>
                <input
                  type="number"
                  className="admin-form-input"
                  placeholder="Enter positive or negative number"
                  value={adjustmentData.adjustment}
                  onChange={(e) => setAdjustmentData({
                    ...adjustmentData,
                    adjustment: e.target.value
                  })}
                />
              </div>

              <div className="admin-form-group">
                <label className="admin-form-label">Reason</label>
                <select
                  className="admin-form-input"
                  value={adjustmentData.reason}
                  onChange={(e) => setAdjustmentData({
                    ...adjustmentData,
                    reason: e.target.value
                  })}
                >
                  <option value="">Select reason</option>
                  <option value="Stock Count">Stock Count</option>
                  <option value="Damaged Goods">Damaged Goods</option>
                  <option value="Returned Items">Returned Items</option>
                  <option value="New Shipment">New Shipment</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="admin-form-group">
                <label className="admin-form-label">Notes (Optional)</label>
                <textarea
                  className="admin-form-input"
                  rows="3"
                  placeholder="Additional notes..."
                  value={adjustmentData.notes}
                  onChange={(e) => setAdjustmentData({
                    ...adjustmentData,
                    notes: e.target.value
                  })}
                />
              </div>
            </div>

            <div className="modal-footer">
              <button
                className="admin-btn admin-btn-secondary"
                onClick={() => setShowAdjustModal(false)}
              >
                Cancel
              </button>
              <button
                className="admin-btn admin-btn-primary"
                onClick={handleSubmitAdjustment}
                disabled={!adjustmentData.adjustment || !adjustmentData.reason}
              >
                Apply Adjustment
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryManagement;
