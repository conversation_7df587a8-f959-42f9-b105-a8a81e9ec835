$connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;User Id=DesignXcel;Password=****************;TrustServerCertificate=True;"

function Execute-SQL {
    param([string]$sql, [string]$description)
    
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    try {
        $connection.Open()
        $command = $connection.CreateCommand()
        $command.CommandText = $sql
        $result = $command.ExecuteNonQuery()
        Write-Host "✓ $description" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "✗ $description - Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    } finally {
        if ($connection.State -eq 'Open') {
            $connection.Close()
        }
    }
}

Write-Host "Finishing database migration..." -ForegroundColor Cyan

# Create remaining tables
Execute-SQL "CREATE TABLE CustomerAddresses (AddressID INT PRIMARY KEY IDENTITY, CustomerID INT FOREIGN KEY REFERENCES Customers(CustomerID), Label NVARCHAR(50), HouseNumber NVARCHAR(50), Street NVARCHAR(100), Barangay NVARCHAR(100), City NVARCHAR(100), Province NVARCHAR(100), Region NVARCHAR(100), PostalCode NVARCHAR(20), Country NVARCHAR(100) DEFAULT 'Philippines', IsDefault BIT DEFAULT 0, CreatedAt DATETIME DEFAULT GETDATE());" "Creating CustomerAddresses table"

Execute-SQL "CREATE TABLE ActivityLogs (LogID INT PRIMARY KEY IDENTITY, UserID INT FOREIGN KEY REFERENCES Users(UserID), Action NVARCHAR(50), TableAffected NVARCHAR(100), RecordID INT, Description NVARCHAR(MAX), Timestamp DATETIME DEFAULT GETDATE());" "Creating ActivityLogs table"

Execute-SQL "CREATE TABLE ProductStockAdjustments (AdjustmentID INT PRIMARY KEY IDENTITY, ProductID INT FOREIGN KEY REFERENCES Products(ProductID), QuantityAdded INT, Reason NVARCHAR(255), PerformedBy INT FOREIGN KEY REFERENCES Users(UserID), Timestamp DATETIME DEFAULT GETDATE());" "Creating ProductStockAdjustments table"

Execute-SQL "CREATE TABLE UserPermissions (PermissionID INT PRIMARY KEY IDENTITY, UserID INT FOREIGN KEY REFERENCES Users(UserID), Section NVARCHAR(50), CanAccess BIT DEFAULT 0);" "Creating UserPermissions table"

Execute-SQL "ALTER TABLE UserPermissions ADD CONSTRAINT UQ_UserSection UNIQUE (UserID, Section);" "Adding unique constraint to UserPermissions"

Execute-SQL "CREATE TABLE Orders (OrderID INT PRIMARY KEY IDENTITY, CustomerID INT FOREIGN KEY REFERENCES Customers(CustomerID), OrderDate DATETIME DEFAULT GETDATE(), Status NVARCHAR(50), TotalAmount DECIMAL(10,2), ShippingAddressID INT FOREIGN KEY REFERENCES CustomerAddresses(AddressID));" "Creating Orders table"

Execute-SQL "CREATE TABLE OrderItems (OrderItemID INT PRIMARY KEY IDENTITY, OrderID INT FOREIGN KEY REFERENCES Orders(OrderID), ProductID INT FOREIGN KEY REFERENCES Products(ProductID), Quantity INT, PriceAtPurchase DECIMAL(10,2));" "Creating OrderItems table"

Execute-SQL "ALTER TABLE Orders ADD PaymentMethod NVARCHAR(50), Currency NVARCHAR(10) DEFAULT 'PHP', PaymentDate DATETIME NULL;" "Adding payment columns to Orders"

Execute-SQL "ALTER TABLE Orders ADD CONSTRAINT CHK_PaymentMethod CHECK (PaymentMethod IN ('Cash on Delivery', 'E-Wallet', 'Bank Transfer'));" "Adding payment method constraint"

Execute-SQL "CREATE TABLE CMSContent (ContentID INT PRIMARY KEY IDENTITY, Page NVARCHAR(100), Section NVARCHAR(100), Content NVARCHAR(MAX), LastUpdated DATETIME DEFAULT GETDATE());" "Creating CMSContent table"

Execute-SQL "CREATE TABLE Testimonials (ID INT PRIMARY KEY IDENTITY, Name NVARCHAR(100) NOT NULL, Title NVARCHAR(100), Text NVARCHAR(MAX) NOT NULL, ImageUrl NVARCHAR(255), CreatedAt DATETIME DEFAULT GETDATE(), Rating INT NOT NULL DEFAULT 5);" "Creating Testimonials table"

Execute-SQL "CREATE TABLE ChatMessages (MessageID INT PRIMARY KEY IDENTITY, CustomerID INT FOREIGN KEY REFERENCES Customers(CustomerID), SupportUserID INT NULL, SenderType NVARCHAR(20) NOT NULL CHECK (SenderType IN ('customer', 'support')), MessageText NVARCHAR(MAX) NOT NULL, SentAt DATETIME DEFAULT GETDATE(), IsRead BIT DEFAULT 0);" "Creating ChatMessages table"

Execute-SQL "CREATE TABLE HeaderOfferBar (ID INT IDENTITY(1,1) PRIMARY KEY, OfferText NVARCHAR(500) NOT NULL, ButtonText NVARCHAR(100) NOT NULL, StartDate DATE NOT NULL, EndDate DATE NOT NULL, Status NVARCHAR(20) NOT NULL DEFAULT 'active', BackgroundColor NVARCHAR(7) NOT NULL DEFAULT '#ffc107', TextColor NVARCHAR(7) NOT NULL DEFAULT '#ffffff', CreatedAt DATETIME2 DEFAULT GETDATE(), UpdatedAt DATETIME2 DEFAULT GETDATE());" "Creating HeaderOfferBar table"

Write-Host "Tables created. Now inserting initial data..." -ForegroundColor Yellow
