# Simple SQL Server connection test

Write-Host "=== SQL Server Connection Test ===" -ForegroundColor Green

# Test different connection strings
$instances = @(
    "DESKTOP-F4OI6BT\SQLEXPRESS",
    "localhost\SQLEXPRESS", 
    ".\SQLEXPRESS",
    "(local)\SQLEXPRESS"
)

$workingConnection = $null

foreach ($instance in $instances) {
    Write-Host "Testing: $instance" -NoNewline
    
    try {
        $connectionString = "Server=$instance;Database=master;Integrated Security=True;TrustServerCertificate=True;Connection Timeout=5;"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT @@SERVERNAME as ServerName, SERVERPROPERTY('IsIntegratedSecurityOnly') as WindowsAuthOnly"
        $reader = $command.ExecuteReader()
        
        if ($reader.Read()) {
            $serverName = $reader["ServerName"]
            $windowsAuthOnly = $reader["WindowsAuthOnly"]
            Write-Host " ✅ SUCCESS" -ForegroundColor Green
            Write-Host "  Server: $serverName"
            Write-Host "  Windows Auth Only: $windowsAuthOnly"
            
            $workingConnection = @{
                Instance = $instance
                ServerName = $serverName
                WindowsAuthOnly = $windowsAuthOnly
            }
        }
        
        $reader.Close()
        $connection.Close()
        break
    }
    catch {
        Write-Host " ❌ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

if ($workingConnection) {
    Write-Host ""
    Write-Host "🎉 Found working connection: $($workingConnection.Instance)" -ForegroundColor Green
    
    # Try to create database and user
    try {
        $connectionString = "Server=$($workingConnection.Instance);Database=master;Integrated Security=True;TrustServerCertificate=True;"
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        
        # Create database
        $command = $connection.CreateCommand()
        $command.CommandText = "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'DesignXcelDB') CREATE DATABASE [DesignXcelDB]"
        $command.ExecuteNonQuery() | Out-Null
        Write-Host "✅ DesignXcelDB database ready" -ForegroundColor Green
        
        # Enable mixed mode authentication
        if ($workingConnection.WindowsAuthOnly) {
            Write-Host "Enabling mixed mode authentication..." -ForegroundColor Yellow
            $command.CommandText = "EXEC xp_instance_regwrite N'HKEY_LOCAL_MACHINE', N'Software\Microsoft\MSSQLServer\MSSQLServer', N'LoginMode', REG_DWORD, 2"
            try {
                $command.ExecuteNonQuery() | Out-Null
                Write-Host "✅ Mixed mode enabled - SQL Server restart required" -ForegroundColor Green
            }
            catch {
                Write-Host "⚠️  Could not enable mixed mode via SQL: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        
        # Create login
        $command.CommandText = @"
IF NOT EXISTS (SELECT name FROM sys.server_principals WHERE name = 'DesignXcel')
BEGIN
    CREATE LOGIN [DesignXcel] WITH PASSWORD = 'Azwrathfrozen22@', 
    DEFAULT_DATABASE = [DesignXcelDB], 
    CHECK_EXPIRATION = OFF, 
    CHECK_POLICY = OFF
END
"@
        try {
            $command.ExecuteNonQuery() | Out-Null
            Write-Host "✅ DesignXcel login created" -ForegroundColor Green
        }
        catch {
            Write-Host "⚠️  Login creation failed: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        # Create database user
        $command.CommandText = @"
USE [DesignXcelDB];
IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = 'DesignXcel')
BEGIN
    CREATE USER [DesignXcel] FOR LOGIN [DesignXcel];
    ALTER ROLE [db_owner] ADD MEMBER [DesignXcel];
END
"@
        try {
            $command.ExecuteNonQuery() | Out-Null
            Write-Host "✅ DesignXcel database user created with db_owner permissions" -ForegroundColor Green
        }
        catch {
            Write-Host "⚠️  Database user creation failed: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        $connection.Close()
        
        # Update .env file
        Write-Host ""
        Write-Host "Updating .env file..." -ForegroundColor Cyan
        $envPath = ".\.env"
        if (Test-Path $envPath) {
            $content = Get-Content $envPath
            $newContent = @()
            
            foreach ($line in $content) {
                if ($line.StartsWith("DB_SERVER=")) {
                    $newContent += "DB_SERVER=$($workingConnection.Instance)"
                }
                elseif ($line.StartsWith("DB_PORT=")) {
                    $newContent += "# DB_PORT=1433  # Not needed for named instances"
                }
                else {
                    $newContent += $line
                }
            }
            
            $newContent | Set-Content $envPath
            Write-Host "✅ .env file updated" -ForegroundColor Green
        }
        
    }
    catch {
        Write-Host "❌ Error setting up database: $($_.Exception.Message)" -ForegroundColor Red
    }
}
else {
    Write-Host ""
    Write-Host "❌ No working SQL Server connection found" -ForegroundColor Red
    Write-Host "Please ensure SQL Server Express is running" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
