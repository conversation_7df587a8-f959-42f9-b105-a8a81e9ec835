/* Bank Selection Component Styles */
.bank-selection {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.bank-selection-header {
    text-align: center;
    margin-bottom: 2rem;
}

.bank-selection-header h3 {
    color: #2c3e50;
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.bank-selection-header p {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* Search Bar */
.bank-search {
    margin-bottom: 1.5rem;
}

.search-input-container {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.search-input:focus {
    outline: none;
    border-color: #F0B21B;
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

/* Bank List */
.bank-list {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.bank-option {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.bank-option:hover {
    border-color: #F0B21B;
    box-shadow: 0 4px 20px rgba(240, 178, 27, 0.15);
    transform: translateY(-2px);
}

.bank-option.selected {
    border-color: #F0B21B;
    background: linear-gradient(135deg, rgba(240, 178, 27, 0.05) 0%, rgba(240, 178, 27, 0.02) 100%);
    box-shadow: 0 6px 25px rgba(240, 178, 27, 0.2);
}

.bank-option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.bank-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.bank-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.bank-initial {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.bank-details {
    flex: 1;
    min-width: 0;
}

.bank-name {
    color: #2c3e50;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.bank-full-name {
    color: #666;
    font-size: 0.875rem;
    margin: 0 0 0.75rem 0;
    line-height: 1.4;
}

.bank-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature-tag {
    background: rgba(240, 178, 27, 0.1);
    color: #B8860B;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.bank-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
    text-align: right;
}

.bank-fee-info {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #28a745;
    font-weight: 600;
    font-size: 0.875rem;
}

.fee-percentage {
    color: #28a745;
}

.fee-fixed {
    color: #666;
    font-size: 0.75rem;
}

.processing-time {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.time-label {
    color: #666;
    font-size: 0.75rem;
    font-weight: 500;
}

.time-value {
    color: #2c3e50;
    font-size: 0.875rem;
    font-weight: 600;
}

.selection-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bank-option.selected .selection-indicator {
    opacity: 1;
}

/* No Banks Found */
.no-banks-found {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.no-banks-found p {
    font-size: 1rem;
    margin: 0;
}

/* Selected Bank Summary */
.selected-bank-summary {
    background: linear-gradient(135deg, #F0B21B 0%, #d4a017 100%);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: white;
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.summary-header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.summary-header h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1.125rem;
    font-weight: 600;
}

.summary-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.875rem;
}

.summary-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.detail-item .label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.detail-item .value {
    font-weight: 600;
    font-size: 0.875rem;
}

/* Bank Payment Info */
.bank-payment-info {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
}

.info-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.info-header h4 {
    color: #2c3e50;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.payment-steps {
    margin: 0 0 1.5rem 0;
    padding-left: 1.25rem;
    color: #555;
}

.payment-steps li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
    font-size: 0.875rem;
}

.security-note {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 8px;
    color: #155724;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .bank-selection {
        padding: 0 1rem;
    }
    
    .bank-option {
        padding: 1rem;
    }
    
    .bank-option-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .bank-info {
        width: 100%;
    }
    
    .bank-meta {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .summary-details {
        grid-template-columns: 1fr;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .selection-indicator {
        position: static;
        align-self: flex-end;
    }
}
