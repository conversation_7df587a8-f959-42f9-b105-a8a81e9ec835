import React, { useState, useEffect } from 'react';
import './SearchFilters.css';

const SearchFilters = ({
  searchTerm,
  selectedCategory,
  selectedStatus,
  categories,
  onSearch,
  onCategoryFilter,
  onStatusFilter,
  onSort,
  sortBy,
  sortDirection
}) => {
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearch(localSearchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearchTerm, onSearch]);

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'draft', label: 'Draft' }
  ];

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'basePrice', label: 'Price' },
    { value: 'category', label: 'Category' },
    { value: 'createdAt', label: 'Date Created' },
    { value: 'updatedAt', label: 'Last Updated' }
  ];

  const handleClearFilters = () => {
    setLocalSearchTerm('');
    onSearch('');
    onCategoryFilter('');
    onStatusFilter('');
  };

  const hasActiveFilters = searchTerm || selectedCategory || selectedStatus;

  return (
    <div className="search-filters">
      <div className="sf-container">
        {/* Search Bar */}
        <div className="sf-search-section">
          <div className="sf-search-wrapper">
            <div className="sf-search-icon">🔍</div>
            <input
              type="text"
              className="sf-search-input"
              placeholder="Search products by name, SKU, or description..."
              value={localSearchTerm}
              onChange={(e) => setLocalSearchTerm(e.target.value)}
            />
            {localSearchTerm && (
              <button
                className="sf-search-clear"
                onClick={() => setLocalSearchTerm('')}
                title="Clear search"
              >
                ×
              </button>
            )}
          </div>
        </div>

        {/* Filters Row */}
        <div className="sf-filters-row">
          {/* Category Filter */}
          <div className="sf-filter-group">
            <label className="sf-filter-label">Category</label>
            <select
              className="sf-filter-select"
              value={selectedCategory}
              onChange={(e) => onCategoryFilter(e.target.value)}
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category.id || category.name || category} value={category.name || category}>
                  {category.name || category}
                </option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div className="sf-filter-group">
            <label className="sf-filter-label">Status</label>
            <select
              className="sf-filter-select"
              value={selectedStatus}
              onChange={(e) => onStatusFilter(e.target.value)}
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Sort Options */}
          <div className="sf-filter-group">
            <label className="sf-filter-label">Sort By</label>
            <div className="sf-sort-wrapper">
              <select
                className="sf-filter-select sf-sort-select"
                value={sortBy}
                onChange={(e) => onSort(e.target.value)}
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <button
                className={`sf-sort-direction ${sortDirection === 'DESC' ? 'desc' : 'asc'}`}
                onClick={() => onSort(sortBy)}
                title={`Sort ${sortDirection === 'ASC' ? 'Descending' : 'Ascending'}`}
              >
                {sortDirection === 'ASC' ? '↑' : '↓'}
              </button>
            </div>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="sf-filter-group">
              <button
                className="sf-clear-filters"
                onClick={handleClearFilters}
                title="Clear all filters"
              >
                Clear Filters
              </button>
            </div>
          )}
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="sf-active-filters">
            <span className="sf-active-label">Active Filters:</span>
            <div className="sf-active-tags">
              {searchTerm && (
                <span className="sf-active-tag">
                  Search: "{searchTerm}"
                  <button onClick={() => { setLocalSearchTerm(''); onSearch(''); }}>×</button>
                </span>
              )}
              {selectedCategory && (
                <span className="sf-active-tag">
                  Category: {selectedCategory}
                  <button onClick={() => onCategoryFilter('')}>×</button>
                </span>
              )}
              {selectedStatus && (
                <span className="sf-active-tag">
                  Status: {statusOptions.find(opt => opt.value === selectedStatus)?.label}
                  <button onClick={() => onStatusFilter('')}>×</button>
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchFilters;
