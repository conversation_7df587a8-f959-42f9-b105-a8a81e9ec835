/* Bank Payment Form Styles */
.bank-payment-form {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

/* Form Header */
.form-header {
    margin-bottom: 2rem;
}

.back-button {
    background: none;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    color: #666;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.back-button:hover:not(:disabled) {
    border-color: #F0B21B;
    color: #F0B21B;
    background: rgba(240, 178, 27, 0.05);
}

.back-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.form-title {
    text-align: center;
}

.form-title h2 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.form-title p {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* Form Sections */
.form-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-section h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.section-description {
    color: #666;
    font-size: 0.875rem;
    margin: 0 0 1.5rem 0;
    line-height: 1.5;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    color: #2c3e50;
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0;
}

.form-input {
    padding: 0.875rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #F0B21B;
    box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.form-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-input:disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.error-message {
    color: #dc3545;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.25rem;
}

/* Payment Summary */
.payment-summary {
    background: linear-gradient(135deg, rgba(240, 178, 27, 0.05) 0%, rgba(240, 178, 27, 0.02) 100%);
    border: 1px solid rgba(240, 178, 27, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
}

.payment-summary h3 {
    color: #2c3e50;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
}

.summary-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    color: #555;
    font-size: 0.875rem;
}

.summary-row.total {
    border-top: 2px solid rgba(240, 178, 27, 0.3);
    padding-top: 0.75rem;
    margin-top: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    color: #2c3e50;
}

/* Error Banner */
.error-banner {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    color: #721c24;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.submit-button {
    background: linear-gradient(135deg, #F0B21B 0%, #d4a017 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 200px;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(240, 178, 27, 0.3);
}

.submit-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(240, 178, 27, 0.4);
}

.submit-button:active:not(:disabled) {
    transform: translateY(0);
}

.submit-button.disabled,
.submit-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Loading Spinner */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .bank-payment-form {
        padding: 1rem;
    }
    
    .form-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-title h2 {
        font-size: 1.75rem;
    }
    
    .back-button {
        width: 100%;
        justify-content: center;
    }
    
    .submit-button {
        width: 100%;
        padding: 1.25rem 2rem;
    }
    
    .summary-row {
        font-size: 0.8rem;
    }
    
    .summary-row.total {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .bank-payment-form {
        padding: 0.5rem;
    }
    
    .form-section {
        padding: 1rem;
        border-radius: 12px;
    }
    
    .form-title h2 {
        font-size: 1.5rem;
    }
    
    .form-title p {
        font-size: 0.875rem;
    }
    
    .payment-summary {
        padding: 1rem;
    }
    
    .error-banner {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }
}
