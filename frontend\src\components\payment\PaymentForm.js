import React, { useState } from 'react';
import paymentService from '../../services/paymentService';

const PaymentForm = ({ 
    paymentMethod, 
    billingInfo, 
    cardInfo, 
    onBillingInfoChange, 
    onCardInfoChange 
}) => {
    const [showCardDetails, setShowCardDetails] = useState(false);
    const [cardBrand, setCardBrand] = useState('');

    const handleCardNumberChange = (value) => {
        // Format card number with spaces
        const formatted = paymentService.formatCardNumber(value);
        onCardInfoChange('cardNumber', formatted);
        
        // Detect card brand
        const brand = paymentService.getCardBrand(value);
        setCardBrand(brand);
    };

    const handleExpiryDateChange = (value) => {
        // Format expiry date as MM/YY
        let formatted = value.replace(/\D/g, '');
        if (formatted.length >= 2) {
            formatted = formatted.substring(0, 2) + '/' + formatted.substring(2, 4);
        }
        onCardInfoChange('expiryDate', formatted);
    };

    const handleCvvChange = (value) => {
        // Limit CVV to 4 digits
        const formatted = value.replace(/\D/g, '').substring(0, 4);
        onCardInfoChange('cvv', formatted);
    };

    const getCardBrandIcon = () => {
        switch (cardBrand) {
            case 'visa':
                return (
                    <svg width="32" height="20" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="40" height="24" rx="4" fill="#1A1F71"/>
                        <text x="20" y="15" textAnchor="middle" fill="white" fontSize="8" fontWeight="bold">VISA</text>
                    </svg>
                );
            case 'mastercard':
                return (
                    <svg width="32" height="20" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="40" height="24" rx="4" fill="#EB001B"/>
                        <circle cx="15" cy="12" r="8" fill="#EB001B"/>
                        <circle cx="25" cy="12" r="8" fill="#FF5F00"/>
                    </svg>
                );
            case 'amex':
                return (
                    <svg width="32" height="20" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="40" height="24" rx="4" fill="#006FCF"/>
                        <text x="20" y="15" textAnchor="middle" fill="white" fontSize="6" fontWeight="bold">AMEX</text>
                    </svg>
                );
            default:
                return (
                    <svg width="32" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="2" y="4" width="20" height="16" rx="3" stroke="#ccc" strokeWidth="2" fill="none"/>
                        <rect x="2" y="8" width="20" height="2" fill="#ccc"/>
                    </svg>
                );
        }
    };

    return (
        <div className="payment-form-section">
            <h3>
                {paymentMethod === 'card' ? 'Payment & Billing Information' : 'Billing Information'}
            </h3>
            <p className="section-description">
                Please provide your billing information to complete the payment.
            </p>

            <form className="payment-form">
                {/* Card Information - Only for card payments */}
                {paymentMethod === 'card' && (
                    <div className="form-section card-section">
                        <h4>Card Information</h4>
                        
                        <div className="form-group">
                            <label htmlFor="cardNumber">Card Number</label>
                            <div className="card-input-wrapper">
                                <input
                                    type="text"
                                    id="cardNumber"
                                    value={cardInfo.cardNumber}
                                    onChange={(e) => handleCardNumberChange(e.target.value)}
                                    placeholder="1234 5678 9012 3456"
                                    maxLength="19"
                                    className="form-control card-number-input"
                                    autoComplete="cc-number"
                                />
                                <div className="card-brand-icon">
                                    {getCardBrandIcon()}
                                </div>
                            </div>
                        </div>

                        <div className="form-row">
                            <div className="form-group">
                                <label htmlFor="expiryDate">Expiry Date</label>
                                <input
                                    type="text"
                                    id="expiryDate"
                                    value={cardInfo.expiryDate}
                                    onChange={(e) => handleExpiryDateChange(e.target.value)}
                                    placeholder="MM/YY"
                                    maxLength="5"
                                    className="form-control"
                                    autoComplete="cc-exp"
                                />
                            </div>
                            <div className="form-group">
                                <label htmlFor="cvv">
                                    CVV
                                    <span className="cvv-help" title="3-4 digit security code on the back of your card">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                                            <path d="M9.09 9A3 3 0 0 1 12 6C13.66 6 15 7.34 15 9C15 10.66 13.66 12 12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                                            <circle cx="12" cy="16" r="1" fill="currentColor"/>
                                        </svg>
                                    </span>
                                </label>
                                <input
                                    type="text"
                                    id="cvv"
                                    value={cardInfo.cvv}
                                    onChange={(e) => handleCvvChange(e.target.value)}
                                    placeholder="123"
                                    maxLength="4"
                                    className="form-control"
                                    autoComplete="cc-csc"
                                />
                            </div>
                        </div>

                        <div className="form-group">
                            <label htmlFor="cardName">Cardholder Name</label>
                            <input
                                type="text"
                                id="cardName"
                                value={cardInfo.cardName}
                                onChange={(e) => onCardInfoChange('cardName', e.target.value)}
                                placeholder="John Doe"
                                className="form-control"
                                autoComplete="cc-name"
                            />
                        </div>
                    </div>
                )}

                {/* Billing Information */}
                <div className="form-section billing-section">
                    <h4>Billing Information</h4>
                    
                    <div className="form-row">
                        <div className="form-group">
                            <label htmlFor="firstName">First Name *</label>
                            <input
                                type="text"
                                id="firstName"
                                value={billingInfo.firstName}
                                onChange={(e) => onBillingInfoChange('firstName', e.target.value)}
                                placeholder="John"
                                className="form-control"
                                required
                                autoComplete="given-name"
                            />
                        </div>
                        <div className="form-group">
                            <label htmlFor="lastName">Last Name *</label>
                            <input
                                type="text"
                                id="lastName"
                                value={billingInfo.lastName}
                                onChange={(e) => onBillingInfoChange('lastName', e.target.value)}
                                placeholder="Doe"
                                className="form-control"
                                required
                                autoComplete="family-name"
                            />
                        </div>
                    </div>

                    <div className="form-group">
                        <label htmlFor="email">Email Address *</label>
                        <input
                            type="email"
                            id="email"
                            value={billingInfo.email}
                            onChange={(e) => onBillingInfoChange('email', e.target.value)}
                            placeholder="<EMAIL>"
                            className="form-control"
                            required
                            autoComplete="email"
                        />
                    </div>

                    <div className="form-group">
                        <label htmlFor="phone">Phone Number</label>
                        <input
                            type="tel"
                            id="phone"
                            value={billingInfo.phone}
                            onChange={(e) => onBillingInfoChange('phone', e.target.value)}
                            placeholder="+63 ************"
                            className="form-control"
                            autoComplete="tel"
                        />
                    </div>

                    <div className="form-group">
                        <label htmlFor="address">Address *</label>
                        <input
                            type="text"
                            id="address"
                            value={billingInfo.address}
                            onChange={(e) => onBillingInfoChange('address', e.target.value)}
                            placeholder="123 Main Street, Barangay"
                            className="form-control"
                            required
                            autoComplete="street-address"
                        />
                    </div>

                    <div className="form-row">
                        <div className="form-group">
                            <label htmlFor="city">City *</label>
                            <input
                                type="text"
                                id="city"
                                value={billingInfo.city}
                                onChange={(e) => onBillingInfoChange('city', e.target.value)}
                                placeholder="Manila"
                                className="form-control"
                                required
                                autoComplete="address-level2"
                            />
                        </div>
                        <div className="form-group">
                            <label htmlFor="state">State/Province</label>
                            <input
                                type="text"
                                id="state"
                                value={billingInfo.state}
                                onChange={(e) => onBillingInfoChange('state', e.target.value)}
                                placeholder="Metro Manila"
                                className="form-control"
                                autoComplete="address-level1"
                            />
                        </div>
                    </div>

                    <div className="form-row">
                        <div className="form-group">
                            <label htmlFor="zipCode">ZIP/Postal Code *</label>
                            <input
                                type="text"
                                id="zipCode"
                                value={billingInfo.zipCode}
                                onChange={(e) => onBillingInfoChange('zipCode', e.target.value)}
                                placeholder="1000"
                                className="form-control"
                                required
                                autoComplete="postal-code"
                            />
                        </div>
                        <div className="form-group">
                            <label htmlFor="country">Country *</label>
                            <select
                                id="country"
                                value={billingInfo.country}
                                onChange={(e) => onBillingInfoChange('country', e.target.value)}
                                className="form-control"
                                required
                                autoComplete="country"
                            >
                                <option value="Philippines">Philippines</option>
                                <option value="Singapore">Singapore</option>
                                <option value="Malaysia">Malaysia</option>
                                <option value="Thailand">Thailand</option>
                                <option value="Indonesia">Indonesia</option>
                                <option value="Vietnam">Vietnam</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Security Notice */}
                <div className="security-notice">
                    <div className="security-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#10B981"/>
                            <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                    </div>
                    <div className="security-text">
                        <p>Your information is encrypted and secure. We never store your payment details.</p>
                    </div>
                </div>
            </form>
        </div>
    );
};

export default PaymentForm;
