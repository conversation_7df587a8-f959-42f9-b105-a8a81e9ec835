/* Inventory Management Styles */
.inventory-management {
  width: 100%;
}

.inventory-header {
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
}

.header-content p {
  color: #7f8c8d;
  margin: 0;
  font-size: 1.1rem;
}

.realtime-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.realtime-status .status-indicator {
  font-size: 0.8rem;
}

.realtime-status .status-text {
  font-size: 0.85rem;
  font-weight: 500;
}

.realtime-status.connected .status-text {
  color: #28a745;
}

.realtime-status.disconnected .status-text {
  color: #dc3545;
}

/* Controls */
.inventory-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-container {
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.filter-container {
  min-width: 150px;
}

.filter-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.95rem;
  background: white;
  cursor: pointer;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: 8px;
}

.product-cell {
  display: flex;
  flex-direction: column;
}

.stock-number {
  font-weight: 600;
  color: #2c3e50;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  color: white;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.modal-body {
  padding: 1.5rem;
}

.current-stock-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.current-stock-info p {
  margin: 0.25rem 0;
  color: #2c3e50;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #eee;
  justify-content: flex-end;
}

/* Loading State */
.inventory-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #7f8c8d;
}

.inventory-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #F0B21B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .inventory-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container,
  .filter-container {
    min-width: auto;
  }

  .admin-table {
    font-size: 0.85rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.75rem 0.5rem;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-footer .admin-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .inventory-header h1 {
    font-size: 1.5rem;
  }

  .inventory-header p {
    font-size: 1rem;
  }

  .admin-table {
    font-size: 0.8rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem 0.25rem;
  }

  .status-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }

  .btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Table hover effects */
.admin-table tbody tr {
  transition: background-color 0.3s ease;
}

.admin-table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Button disabled state */
.admin-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.admin-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Form textarea */
.admin-form-input[rows] {
  resize: vertical;
  min-height: 80px;
}

/* Stock level indicators */
.stock-number {
  position: relative;
}

.stock-number::after {
  content: '';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #27ae60;
}

tr:has(.status-badge[style*="rgb(243, 156, 18)"]) .stock-number::after {
  background: #f39c12;
}

tr:has(.status-badge[style*="rgb(231, 76, 60)"]) .stock-number::after {
  background: #e74c3c;
}

tr:has(.status-badge[style*="rgb(149, 165, 166)"]) .stock-number::after {
  background: #95a5a6;
}
