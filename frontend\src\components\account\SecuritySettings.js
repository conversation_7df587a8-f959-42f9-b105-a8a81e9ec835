import React, { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';

const SecuritySettings = () => {
    const { user } = useAuth();
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');
    const [activeSection, setActiveSection] = useState(null);
    
    // Password Change State
    const [passwordData, setPasswordData] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });

    // Two-Factor Authentication State
    const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
    const [showQRCode, setShowQRCode] = useState(false);
    const [verificationCode, setVerificationCode] = useState('');

    // Login Sessions State
    const [sessions] = useState([
        {
            id: 1,
            device: 'Chrome on Windows',
            location: 'Manila, Philippines',
            lastActive: '2024-01-15 14:30',
            current: true,
            ipAddress: '*************'
        },
        {
            id: 2,
            device: 'Safari on iPhone',
            location: 'Quezon City, Philippines',
            lastActive: '2024-01-14 09:15',
            current: false,
            ipAddress: '*************'
        },
        {
            id: 3,
            device: 'Firefox on MacOS',
            location: 'Makati, Philippines',
            lastActive: '2024-01-13 16:45',
            current: false,
            ipAddress: '*************'
        }
    ]);

    const handlePasswordChange = (e) => {
        setPasswordData({
            ...passwordData,
            [e.target.name]: e.target.value
        });
    };

    const handlePasswordSubmit = async (e) => {
        e.preventDefault();
        
        if (passwordData.newPassword !== passwordData.confirmPassword) {
            setMessage('New passwords do not match');
            return;
        }

        if (passwordData.newPassword.length < 8) {
            setMessage('Password must be at least 8 characters long');
            return;
        }

        setLoading(true);
        setMessage('');

        try {
            // Mock API call - replace with actual password change API
            await new Promise(resolve => setTimeout(resolve, 1500));
            setMessage('Password changed successfully!');
            setPasswordData({
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
            });
            setActiveSection(null);
        } catch (error) {
            setMessage('Failed to change password. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleEnable2FA = async () => {
        setLoading(true);
        try {
            // Mock API call - replace with actual 2FA setup API
            await new Promise(resolve => setTimeout(resolve, 1000));
            setShowQRCode(true);
        } catch (error) {
            setMessage('Failed to setup 2FA. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleVerify2FA = async (e) => {
        e.preventDefault();
        setLoading(true);
        
        try {
            // Mock API call - replace with actual 2FA verification API
            await new Promise(resolve => setTimeout(resolve, 1000));
            setTwoFactorEnabled(true);
            setShowQRCode(false);
            setVerificationCode('');
            setMessage('Two-factor authentication enabled successfully!');
        } catch (error) {
            setMessage('Invalid verification code. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleDisable2FA = async () => {
        if (window.confirm('Are you sure you want to disable two-factor authentication? This will make your account less secure.')) {
            setLoading(true);
            try {
                // Mock API call - replace with actual 2FA disable API
                await new Promise(resolve => setTimeout(resolve, 1000));
                setTwoFactorEnabled(false);
                setMessage('Two-factor authentication disabled.');
            } catch (error) {
                setMessage('Failed to disable 2FA. Please try again.');
            } finally {
                setLoading(false);
            }
        }
    };

    const handleTerminateSession = async (sessionId) => {
        if (window.confirm('Are you sure you want to terminate this session?')) {
            try {
                // Mock API call - replace with actual session termination API
                await new Promise(resolve => setTimeout(resolve, 500));
                setMessage('Session terminated successfully.');
            } catch (error) {
                setMessage('Failed to terminate session. Please try again.');
            }
        }
    };

    const getPasswordStrength = (password) => {
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        return strength;
    };

    const getStrengthLabel = (strength) => {
        switch (strength) {
            case 0:
            case 1: return { label: 'Very Weak', color: '#e74c3c' };
            case 2: return { label: 'Weak', color: '#f39c12' };
            case 3: return { label: 'Fair', color: '#f1c40f' };
            case 4: return { label: 'Good', color: '#27ae60' };
            case 5: return { label: 'Strong', color: '#2ecc71' };
            default: return { label: 'Unknown', color: '#95a5a6' };
        }
    };

    const passwordStrength = getPasswordStrength(passwordData.newPassword);
    const strengthInfo = getStrengthLabel(passwordStrength);

    return (
        <div className="security-settings">
            <div className="section-header">
                <div>
                    <h2 className="section-title">Security Settings</h2>
                    <p className="section-subtitle">
                        Manage your account security and authentication settings
                    </p>
                </div>
            </div>

            {message && (
                <div className={`message ${message.includes('success') ? 'success' : 'error'}`}>
                    {message}
                </div>
            )}

            <div className="security-sections">
                {/* Password Change Section */}
                <div className="security-card">
                    <div className="security-card-header">
                        <div className="security-info">
                            <h3 className="security-title">Password</h3>
                            <p className="security-description">
                                Change your account password to keep your account secure
                            </p>
                        </div>
                        <button
                            className="btn-secondary"
                            onClick={() => setActiveSection(activeSection === 'password' ? null : 'password')}
                        >
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                <circle cx="12" cy="16" r="1"/>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                            </svg>
                            Change Password
                        </button>
                    </div>

                    {activeSection === 'password' && (
                        <div className="security-content">
                            <form onSubmit={handlePasswordSubmit} className="password-form">
                                <div className="form-group">
                                    <label className="form-label">Current Password</label>
                                    <input
                                        type="password"
                                        name="currentPassword"
                                        className="form-input"
                                        value={passwordData.currentPassword}
                                        onChange={handlePasswordChange}
                                        required
                                    />
                                </div>

                                <div className="form-group">
                                    <label className="form-label">New Password</label>
                                    <input
                                        type="password"
                                        name="newPassword"
                                        className="form-input"
                                        value={passwordData.newPassword}
                                        onChange={handlePasswordChange}
                                        required
                                    />
                                    {passwordData.newPassword && (
                                        <div className="password-strength">
                                            <div className="strength-bar">
                                                <div 
                                                    className="strength-fill"
                                                    style={{ 
                                                        width: `${(passwordStrength / 5) * 100}%`,
                                                        backgroundColor: strengthInfo.color
                                                    }}
                                                ></div>
                                            </div>
                                            <span 
                                                className="strength-label"
                                                style={{ color: strengthInfo.color }}
                                            >
                                                {strengthInfo.label}
                                            </span>
                                        </div>
                                    )}
                                </div>

                                <div className="form-group">
                                    <label className="form-label">Confirm New Password</label>
                                    <input
                                        type="password"
                                        name="confirmPassword"
                                        className="form-input"
                                        value={passwordData.confirmPassword}
                                        onChange={handlePasswordChange}
                                        required
                                    />
                                </div>

                                <div className="form-actions">
                                    <button
                                        type="submit"
                                        className="btn-primary"
                                        disabled={loading}
                                    >
                                        {loading ? 'Changing Password...' : 'Change Password'}
                                    </button>
                                    <button
                                        type="button"
                                        className="btn-secondary"
                                        onClick={() => setActiveSection(null)}
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    )}
                </div>

                {/* Two-Factor Authentication Section */}
                <div className="security-card">
                    <div className="security-card-header">
                        <div className="security-info">
                            <h3 className="security-title">Two-Factor Authentication</h3>
                            <p className="security-description">
                                Add an extra layer of security to your account
                            </p>
                            <div className="security-status">
                                <span className={`status-indicator ${twoFactorEnabled ? 'enabled' : 'disabled'}`}>
                                    {twoFactorEnabled ? 'Enabled' : 'Disabled'}
                                </span>
                            </div>
                        </div>
                        {!twoFactorEnabled ? (
                            <button
                                className="btn-primary"
                                onClick={handleEnable2FA}
                                disabled={loading}
                            >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                </svg>
                                Enable 2FA
                            </button>
                        ) : (
                            <button
                                className="btn-danger"
                                onClick={handleDisable2FA}
                                disabled={loading}
                            >
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                                    <line x1="9" y1="9" x2="15" y2="15"/>
                                    <line x1="15" y1="9" x2="9" y2="15"/>
                                </svg>
                                Disable 2FA
                            </button>
                        )}
                    </div>

                    {showQRCode && (
                        <div className="security-content">
                            <div className="two-factor-setup">
                                <h4>Setup Two-Factor Authentication</h4>
                                <p>Scan this QR code with your authenticator app:</p>
                                
                                <div className="qr-code-placeholder">
                                    <svg width="150" height="150" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1">
                                        <rect x="3" y="3" width="7" height="7"/>
                                        <rect x="14" y="3" width="7" height="7"/>
                                        <rect x="3" y="14" width="7" height="7"/>
                                        <rect x="14" y="14" width="7" height="7"/>
                                    </svg>
                                    <p>QR Code Placeholder</p>
                                </div>

                                <form onSubmit={handleVerify2FA} className="verification-form">
                                    <div className="form-group">
                                        <label className="form-label">Verification Code</label>
                                        <input
                                            type="text"
                                            className="form-input"
                                            value={verificationCode}
                                            onChange={(e) => setVerificationCode(e.target.value)}
                                            placeholder="Enter 6-digit code"
                                            maxLength="6"
                                            required
                                        />
                                    </div>
                                    <div className="form-actions">
                                        <button
                                            type="submit"
                                            className="btn-primary"
                                            disabled={loading}
                                        >
                                            {loading ? 'Verifying...' : 'Verify & Enable'}
                                        </button>
                                        <button
                                            type="button"
                                            className="btn-secondary"
                                            onClick={() => setShowQRCode(false)}
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    )}
                </div>

                {/* Active Sessions Section */}
                <div className="security-card">
                    <div className="security-card-header">
                        <div className="security-info">
                            <h3 className="security-title">Active Sessions</h3>
                            <p className="security-description">
                                Monitor and manage your active login sessions
                            </p>
                        </div>
                    </div>

                    <div className="security-content">
                        <div className="sessions-list">
                            {sessions.map(session => (
                                <div key={session.id} className="session-item">
                                    <div className="session-info">
                                        <div className="session-device">
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                                <line x1="8" y1="21" x2="16" y2="21"/>
                                                <line x1="12" y1="17" x2="12" y2="21"/>
                                            </svg>
                                            <div>
                                                <h4 className="device-name">{session.device}</h4>
                                                <p className="device-location">{session.location}</p>
                                                <p className="device-details">
                                                    Last active: {session.lastActive} • IP: {session.ipAddress}
                                                </p>
                                            </div>
                                        </div>
                                        {session.current && (
                                            <span className="current-session">Current Session</span>
                                        )}
                                    </div>
                                    {!session.current && (
                                        <button
                                            className="btn-danger session-terminate"
                                            onClick={() => handleTerminateSession(session.id)}
                                        >
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                                <line x1="18" y1="6" x2="6" y2="18"/>
                                                <line x1="6" y1="6" x2="18" y2="18"/>
                                            </svg>
                                            Terminate
                                        </button>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SecuritySettings;
