/* PayMongo Checkout Component Styles */
.paymongo-checkout {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: #fafafa;
  min-height: 100vh;
}

/* Header */
.checkout-header {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.checkout-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 0.5rem;
}

.checkout-header p {
  color: #6c757d;
  margin: 0;
}

/* Order Summary & Payment Methods */
.order-summary,
.payment-methods {
  background: white;
  padding: 1.5rem;
  border-radius: 16px;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.order-summary h3,
.payment-methods h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 1rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row.total {
  font-weight: 700;
  font-size: 1.1rem;
  color: #F0B21B;
  border-top: 2px solid #F0B21B;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.method-grid {
  display: grid;
  gap: 1rem;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.payment-method:hover {
  border-color: #F0B21B;
  background: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.15);
}

.payment-method.selected {
  border-color: #F0B21B;
  background: #fff;
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.2);
}

.method-icon {
  font-size: 2rem;
  margin-right: 1rem;
  min-width: 60px;
  text-align: center;
}

.method-info {
  flex: 1;
}

.method-name {
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.25rem;
}

.method-description {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
}

.method-fee {
  font-size: 0.75rem;
  color: #F0B21B;
  font-weight: 500;
}

/* Error Message */
.error-message {
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  color: #c33;
}

.error-icon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.error-actions {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
}

/* Buttons */
.payment-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.btn {
  flex: 1;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: #F0B21B;
  color: white;
  box-shadow: 0 4px 12px rgba(240, 178, 27, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: #d4a017;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(240, 178, 27, 0.4);
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Button Variants */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  min-height: auto;
}

.btn-outline {
  background: transparent;
  border: 2px solid #6c757d;
  color: #6c757d;
}

.btn-outline:hover:not(:disabled) {
  background: #6c757d;
  color: white;
}

.btn-text {
  background: transparent;
  color: #6c757d;
  box-shadow: none;
}

.btn-text:hover:not(:disabled) {
  background: #f8f9fa;
  transform: none;
}

/* Loading & Icons */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.payment-icon {
  font-size: 1.25rem;
}

/* Payment Status & Security */
.payment-link-status {
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.status-header,
.security-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-weight: 600;
}

.status-header {
  color: #2d5a2d;
}

.security-header {
  color: #495057;
  margin-bottom: 0.75rem;
}

.status-icon,
.security-icon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.payment-link-status p {
  color: #2d5a2d;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.payment-link-info {
  background: white;
  border-radius: 8px;
  padding: 1rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-row:last-child {
  border-bottom: none;
}

.reference-number {
  font-family: monospace;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.status-badge {
  background: #F0B21B;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.security-notice {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
}

.security-notice p {
  color: #6c757d;
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .paymongo-checkout {
    padding: 1rem;
  }

  .checkout-header {
    padding: 1.5rem;
  }

  .payment-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .method-grid {
    gap: 0.75rem;
  }

  .payment-method {
    padding: 0.75rem;
  }

  .method-icon {
    font-size: 1.5rem;
    min-width: 50px;
  }
}
