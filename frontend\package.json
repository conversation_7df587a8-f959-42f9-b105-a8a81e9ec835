{"name": "office-ecommerce-frontend", "version": "1.0.0", "description": "Frontend for Office Furniture E-commerce Platform", "dependencies": {"@react-three/drei": "^9.70.0", "@react-three/fiber": "^8.13.7", "ajv": "^8.12.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "dotenv": "^17.0.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.14.2", "react-scripts": "^5.0.1", "react-toastify": "^11.0.5", "react-use-measure": "^2.1.7", "socket.io-client": "^4.7.2", "styled-components": "^6.0.7", "three": "^0.154.0", "uuid": "^11.1.0", "web-vitals": "^5.0.3"}, "scripts": {"start": "react-scripts start", "start:dev": "REACT_APP_ENVIRONMENT=development react-scripts start", "start:prod": "REACT_APP_ENVIRONMENT=production react-scripts start", "build": "react-scripts build", "build:dev": "REACT_APP_ENVIRONMENT=development react-scripts build", "build:prod": "REACT_APP_ENVIRONMENT=production react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject", "serve": "npx serve -s build -l 3000", "analyze": "npx source-map-explorer 'build/static/js/*.js'", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "check-backend": "node scripts/check-backend.js"}, "devDependencies": {"chalk": "^4.1.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}