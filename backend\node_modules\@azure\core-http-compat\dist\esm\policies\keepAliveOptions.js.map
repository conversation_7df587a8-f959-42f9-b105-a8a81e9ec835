{"version": 3, "file": "keepAliveOptions.js", "sourceRoot": "", "sources": ["../../../src/policies/keepAliveOptions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Keep Alive Options for how HTTP connections.\n */\nexport interface KeepAliveOptions {\n  /**\n   * When true, connections will be kept alive for multiple requests.\n   * Defaults to true.\n   */\n  enable?: boolean;\n}\n"]}