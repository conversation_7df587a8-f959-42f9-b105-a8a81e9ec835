{"version": 3, "file": "extendedClient.js", "sourceRoot": "", "sources": ["../../src/extendedClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EACL,4BAA4B,EAC5B,sCAAsC,GACvC,MAAM,sCAAsC,CAAC;AAE9C,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAS/D,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AA0BjD;;GAEG;AACH,MAAM,OAAO,qBAAsB,SAAQ,aAAa;IACtD,YAAY,OAAqC;;QAC/C,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IACE,CAAA,MAAA,OAAO,CAAC,gBAAgB,0CAAE,MAAM,MAAK,KAAK;YAC1C,CAAC,sCAAsC,CAAC,IAAI,CAAC,QAAQ,CAAC,EACtD,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,4BAA4B,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAA,MAAA,OAAO,CAAC,eAAe,0CAAE,eAAe,MAAK,KAAK,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;gBACzB,IAAI,EAAE,kBAAkB;aACzB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,oBAAoB,CACxB,kBAAsC,EACtC,aAA4B;;QAE5B,MAAM,oBAAoB,GACxB,MAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,OAAO,0CAAE,UAAU,CAAC;QAE1C,IAAI,YAA+C,CAAC;QAEpD,SAAS,UAAU,CACjB,WAAkC,EAClC,YAAqB,EACrB,KAAe;YAEf,YAAY,GAAG,WAAW,CAAC;YAC3B,IAAI,oBAAoB,EAAE,CAAC;gBACzB,oBAAoB,CAAC,WAAW,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAED,kBAAkB,CAAC,OAAO,mCACrB,kBAAkB,CAAC,OAAO,KAC7B,UAAU,GACX,CAAC;QAEF,MAAM,MAAM,GAAM,MAAM,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAEtF,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE;gBACzC,KAAK,EAAE,gBAAgB,CAAC,YAAY,CAAC;aACtC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { KeepAliveOptions } from \"./policies/keepAliveOptions.js\";\nimport {\n  createDisableKeepAlivePolicy,\n  pipelineContainsDisableKeepAlivePolicy,\n} from \"./policies/disableKeepAlivePolicy.js\";\nimport type { RedirectOptions } from \"./policies/redirectOptions.js\";\nimport { redirectPolicyName } from \"@azure/core-rest-pipeline\";\nimport type {\n  CommonClientOptions,\n  FullOperationResponse,\n  OperationArguments,\n  OperationSpec,\n  RawResponseCallback,\n  ServiceClientOptions,\n} from \"@azure/core-client\";\nimport { ServiceClient } from \"@azure/core-client\";\nimport { toCompatResponse } from \"./response.js\";\n\n/**\n * Options specific to Shim Clients.\n */\nexport interface ExtendedClientOptions {\n  /**\n   * Options to disable keep alive.\n   */\n  keepAliveOptions?: KeepAliveOptions;\n  /**\n   * Options to redirect requests.\n   */\n  redirectOptions?: RedirectOptions;\n}\n\n/**\n * Options that shim clients are expected to expose.\n */\nexport type ExtendedServiceClientOptions = ServiceClientOptions & ExtendedClientOptions;\n\n/**\n * The common set of options that custom shim clients are expected to expose.\n */\nexport type ExtendedCommonClientOptions = CommonClientOptions & ExtendedClientOptions;\n\n/**\n * Client to provide compatability between core V1 & V2.\n */\nexport class ExtendedServiceClient extends ServiceClient {\n  constructor(options: ExtendedServiceClientOptions) {\n    super(options);\n\n    if (\n      options.keepAliveOptions?.enable === false &&\n      !pipelineContainsDisableKeepAlivePolicy(this.pipeline)\n    ) {\n      this.pipeline.addPolicy(createDisableKeepAlivePolicy());\n    }\n\n    if (options.redirectOptions?.handleRedirects === false) {\n      this.pipeline.removePolicy({\n        name: redirectPolicyName,\n      });\n    }\n  }\n\n  /**\n   * Compatible send operation request function.\n   *\n   * @param operationArguments - Operation arguments\n   * @param operationSpec - Operation Spec\n   * @returns\n   */\n  async sendOperationRequest<T>(\n    operationArguments: OperationArguments,\n    operationSpec: OperationSpec,\n  ): Promise<T> {\n    const userProvidedCallBack: RawResponseCallback | undefined =\n      operationArguments?.options?.onResponse;\n\n    let lastResponse: FullOperationResponse | undefined;\n\n    function onResponse(\n      rawResponse: FullOperationResponse,\n      flatResponse: unknown,\n      error?: unknown,\n    ): void {\n      lastResponse = rawResponse;\n      if (userProvidedCallBack) {\n        userProvidedCallBack(rawResponse, flatResponse, error);\n      }\n    }\n\n    operationArguments.options = {\n      ...operationArguments.options,\n      onResponse,\n    };\n\n    const result: T = await super.sendOperationRequest(operationArguments, operationSpec);\n\n    if (lastResponse) {\n      Object.defineProperty(result, \"_response\", {\n        value: toCompatResponse(lastResponse),\n      });\n    }\n\n    return result;\n  }\n}\n"]}