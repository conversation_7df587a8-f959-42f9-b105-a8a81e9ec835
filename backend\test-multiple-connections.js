const sql = require('mssql');
require('dotenv').config();

async function testConnection(config, description) {
    console.log(`\n=== Testing ${description} ===`);
    console.log('Configuration:', JSON.stringify({
        ...config,
        password: config.password ? '[HIDDEN]' : undefined
    }, null, 2));
    
    let pool;
    try {
        pool = await sql.connect(config);
        console.log('✅ Connection successful!');
        
        const result = await pool.request().query('SELECT @@VERSION as Version, @@SERVERNAME as ServerName, DB_NAME() as DatabaseName');
        if (result.recordset && result.recordset.length > 0) {
            console.log(`Server: ${result.recordset[0].ServerName}`);
            console.log(`Database: ${result.recordset[0].DatabaseName}`);
        }
        return true;
    } catch (err) {
        console.log('❌ Connection failed:', err.message);
        return false;
    } finally {
        if (pool) {
            try {
                await pool.close();
            } catch (closeErr) {
                // Ignore close errors
            }
        }
    }
}

async function runTests() {
    console.log('Testing multiple SQL Server connection configurations...\n');
    
    const baseServer = process.env.DB_SERVER;
    const database = process.env.DB_DATABASE;
    const username = process.env.DB_USERNAME;
    const password = process.env.DB_PASSWORD;
    
    const configs = [
        // Test 1: Original config with port
        {
            server: baseServer,
            port: 1433,
            user: username,
            password: password,
            database: database,
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true
            }
        },
        
        // Test 2: Without explicit port (let SQL Server handle it)
        {
            server: baseServer,
            user: username,
            password: password,
            database: database,
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true
            }
        },
        
        // Test 3: Windows Authentication with port
        {
            server: baseServer,
            port: 1433,
            database: database,
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                trustedConnection: true
            }
        },
        
        // Test 4: Windows Authentication without port
        {
            server: baseServer,
            database: database,
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                trustedConnection: true
            }
        },
        
        // Test 5: Try connecting to master database first
        {
            server: baseServer,
            user: username,
            password: password,
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true
            }
        },
        
        // Test 6: Windows Auth to master
        {
            server: baseServer,
            database: 'master',
            options: {
                encrypt: false,
                trustServerCertificate: true,
                enableArithAbort: true,
                trustedConnection: true
            }
        }
    ];
    
    const descriptions = [
        'SQL Auth with port to DesignXcelDB',
        'SQL Auth without port to DesignXcelDB',
        'Windows Auth with port to DesignXcelDB',
        'Windows Auth without port to DesignXcelDB',
        'SQL Auth to master database',
        'Windows Auth to master database'
    ];
    
    let successfulConfig = null;
    
    for (let i = 0; i < configs.length; i++) {
        const success = await testConnection(configs[i], descriptions[i]);
        if (success && !successfulConfig) {
            successfulConfig = { config: configs[i], description: descriptions[i] };
        }
    }
    
    if (successfulConfig) {
        console.log('\n🎉 Found working configuration!');
        console.log(`Working config: ${successfulConfig.description}`);
        
        // If we connected to master, try to create the database and user
        if (successfulConfig.config.database === 'master') {
            console.log('\nAttempting to set up database and user...');
            await setupDatabaseAndUser(successfulConfig.config);
        }
    } else {
        console.log('\n❌ No working configuration found.');
        console.log('\n💡 Troubleshooting suggestions:');
        console.log('1. Verify SQL Server Express is installed and running');
        console.log('2. Check SQL Server Configuration Manager for instance settings');
        console.log('3. Ensure SQL Server Authentication is enabled (mixed mode)');
        console.log('4. Check if TCP/IP protocol is enabled for SQL Server');
        console.log('5. Verify firewall settings allow SQL Server connections');
    }
}

async function setupDatabaseAndUser(masterConfig) {
    let pool;
    try {
        pool = await sql.connect(masterConfig);
        
        // Check if database exists
        console.log('Checking if DesignXcelDB exists...');
        const dbCheck = await pool.request().query(`
            SELECT name FROM sys.databases WHERE name = '${process.env.DB_DATABASE}'
        `);
        
        if (dbCheck.recordset.length === 0) {
            console.log('Creating DesignXcelDB database...');
            await pool.request().query(`CREATE DATABASE [${process.env.DB_DATABASE}]`);
            console.log('✅ Database created successfully.');
        } else {
            console.log('✅ Database already exists.');
        }
        
        // Check if login exists
        console.log('Checking if DesignXcel login exists...');
        const loginCheck = await pool.request().query(`
            SELECT name FROM sys.server_principals WHERE name = '${process.env.DB_USERNAME}'
        `);
        
        if (loginCheck.recordset.length === 0) {
            console.log('Creating DesignXcel login...');
            await pool.request().query(`
                CREATE LOGIN [${process.env.DB_USERNAME}] 
                WITH PASSWORD = '${process.env.DB_PASSWORD}', 
                DEFAULT_DATABASE = [${process.env.DB_DATABASE}], 
                CHECK_EXPIRATION = OFF, 
                CHECK_POLICY = OFF
            `);
            console.log('✅ Login created successfully.');
        } else {
            console.log('✅ Login already exists.');
        }
        
        // Create database user
        console.log('Setting up database user...');
        await pool.request().query(`
            USE [${process.env.DB_DATABASE}];
            IF NOT EXISTS (SELECT * FROM sys.database_principals WHERE name = '${process.env.DB_USERNAME}')
            BEGIN
                CREATE USER [${process.env.DB_USERNAME}] FOR LOGIN [${process.env.DB_USERNAME}];
                ALTER ROLE [db_owner] ADD MEMBER [${process.env.DB_USERNAME}];
            END
        `);
        console.log('✅ Database user configured successfully.');
        
    } catch (err) {
        console.error('❌ Error setting up database/user:', err.message);
    } finally {
        if (pool) {
            await pool.close();
        }
    }
}

runTests();
