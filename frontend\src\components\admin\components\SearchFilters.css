/* Search Filters Component */
.search-filters {
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.sf-container {
  padding: 24px;
}

/* Search Section */
.sf-search-section {
  margin-bottom: 20px;
}

.sf-search-wrapper {
  position: relative;
  max-width: 500px;
}

.sf-search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #f8f9fa;
  box-sizing: border-box;
}

.sf-search-input:focus {
  outline: none;
  border-color: #F0B21B;
  background: white;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.sf-search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 16px;
  pointer-events: none;
}

.sf-search-clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sf-search-clear:hover {
  background: #f1f3f4;
  color: #666;
}

/* Filters Row */
.sf-filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  align-items: end;
}

.sf-filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.sf-filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.sf-filter-select {
  padding: 10px 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 44px;
  box-sizing: border-box;
}

.sf-filter-select:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.sf-filter-select:hover {
  border-color: #F0B21B;
}

/* Sort Wrapper */
.sf-sort-wrapper {
  display: flex;
  gap: 8px;
}

.sf-sort-select {
  flex: 1;
}

.sf-sort-direction {
  padding: 10px 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sf-sort-direction:hover {
  border-color: #F0B21B;
  background: #F0B21B10;
}

.sf-sort-direction.desc {
  background: #F0B21B;
  color: white;
  border-color: #F0B21B;
}

/* Clear Filters Button */
.sf-clear-filters {
  padding: 10px 16px;
  background: #f8f9fa;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-height: 44px;
  align-self: end;
}

.sf-clear-filters:hover {
  background: #fff5f5;
  border-color: #ff6b6b;
  color: #ff6b6b;
}

/* Active Filters */
.sf-active-filters {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e1e8ed;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.sf-active-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.sf-active-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.sf-active-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #F0B21B20;
  border: 1px solid #F0B21B40;
  border-radius: 20px;
  font-size: 13px;
  color: #F0B21B;
  font-weight: 500;
}

.sf-active-tag button {
  background: none;
  border: none;
  color: #F0B21B;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 0;
  margin-left: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.sf-active-tag button:hover {
  background: #F0B21B;
  color: white;
}

/* Responsive Design */
@media (max-width: 1199px) {
  .sf-filters-row {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 767px) {
  .sf-container {
    padding: 16px;
  }
  
  .sf-search-section {
    margin-bottom: 16px;
  }
  
  .sf-search-wrapper {
    max-width: none;
  }
  
  .sf-search-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .sf-filters-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .sf-sort-wrapper {
    flex-direction: column;
    gap: 8px;
  }
  
  .sf-active-filters {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .sf-active-tags {
    width: 100%;
  }
  
  .sf-clear-filters {
    width: 100%;
    justify-content: center;
  }
}

/* Touch targets for mobile */
@media (max-width: 767px) {
  .sf-filter-select,
  .sf-sort-direction,
  .sf-clear-filters {
    min-height: 44px;
  }
  
  .sf-search-clear {
    min-width: 44px;
    min-height: 44px;
  }
}
