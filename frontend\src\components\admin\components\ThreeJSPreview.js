import React, { useRef, useEffect, useState, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, ContactShadows, useGLTF } from '@react-three/drei';
import * as THREE from 'three';
import './ThreeJSPreview.css';

// Loading component
const LoadingSpinner = () => (
  <div className="preview-loading">
    <div className="loading-spinner"></div>
    <p>Loading 3D model...</p>
  </div>
);

// Error component
const ErrorDisplay = ({ error }) => (
  <div className="preview-error">
    <div className="error-icon">⚠️</div>
    <h3>Failed to load 3D model</h3>
    <p>{error.message || 'Unknown error occurred'}</p>
  </div>
);

// Model component for file-based loading
const Model = ({ modelFile, onLoad, onError }) => {
  const meshRef = useRef();
  const [modelUrl, setModelUrl] = useState(null);

  useEffect(() => {
    if (!modelFile) return;

    // Create object URL from file
    const url = URL.createObjectURL(modelFile);
    setModelUrl(url);

    return () => {
      URL.revokeObjectURL(url);
      setModelUrl(null);
    };
  }, [modelFile]);

  useFrame((state) => {
    if (meshRef.current) {
      // Gentle rotation animation
      meshRef.current.rotation.y += 0.005;
    }
  });

  if (!modelUrl) return null;

  return <ModelLoader url={modelUrl} meshRef={meshRef} onLoad={onLoad} onError={onError} />;
};

// Separate component for GLTF loading
const ModelLoader = ({ url, meshRef, onLoad, onError }) => {
  try {
    const { scene } = useGLTF(url);

    useEffect(() => {
      if (scene && onLoad) {
        onLoad({ scene });
      }
    }, [scene, onLoad]);

    return (
      <primitive
        ref={meshRef}
        object={scene.clone()}
        scale={1}
        position={[0, 0, 0]}
      />
    );
  } catch (error) {
    useEffect(() => {
      if (onError) {
        onError(error);
      }
    }, [error, onError]);

    return null;
  }
};

// Camera controller
const CameraController = () => {
  useFrame((state) => {
    // Smooth camera movement
    state.camera.lookAt(0, 0, 0);
  });
  return null;
};

const ThreeJSPreview = ({ modelFile, className = '' }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [modelInfo, setModelInfo] = useState(null);

  const handleModelLoad = (gltf) => {
    setLoading(false);
    setError(null);
    
    // Extract model information
    const scene = gltf.scene;
    const animations = gltf.animations;
    
    // Calculate bounding box
    const box = new THREE.Box3().setFromObject(scene);
    const size = box.getSize(new THREE.Vector3());
    
    setModelInfo({
      triangles: 0, // Would need to traverse geometry to count
      materials: scene.traverse((child) => {
        if (child.isMesh) return child.material;
      }),
      animations: animations.length,
      size: {
        width: size.x.toFixed(2),
        height: size.y.toFixed(2),
        depth: size.z.toFixed(2)
      }
    });
  };

  const handleModelError = (error) => {
    setLoading(false);
    setError(error);
  };

  if (!modelFile) {
    return (
      <div className={`threejs-preview ${className}`}>
        <div className="preview-placeholder">
          <div className="placeholder-icon">🎯</div>
          <h3>No 3D Model Selected</h3>
          <p>Upload a GLB or GLTF file to see the 3D preview</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`threejs-preview ${className}`}>
        <ErrorDisplay error={error} />
      </div>
    );
  }

  return (
    <div className={`threejs-preview ${className}`}>
      <div className="preview-container">
        {loading && <LoadingSpinner />}
        
        <Canvas
          camera={{ position: [5, 5, 5], fov: 50 }}
          style={{ background: 'linear-gradient(135deg, #f8f9fa 0%, #F0B21B20 100%)' }}
        >
          <Suspense fallback={null}>
            {/* Lighting */}
            <ambientLight intensity={0.6} />
            <directionalLight
              position={[10, 10, 5]}
              intensity={1.2}
              castShadow
              shadow-mapSize-width={2048}
              shadow-mapSize-height={2048}
            />
            <directionalLight
              position={[-10, 5, -5]}
              intensity={0.5}
            />
            <pointLight position={[-10, -10, -10]} intensity={0.4} />
            <pointLight position={[10, 10, 10]} intensity={0.3} color="#F0B21B" />
            
            {/* Model */}
            <Model 
              modelFile={modelFile}
              onLoad={handleModelLoad}
              onError={handleModelError}
            />
            
            {/* Ground shadow */}
            <ContactShadows 
              position={[0, -1, 0]} 
              opacity={0.4} 
              scale={10} 
              blur={2} 
              far={4} 
            />
            
            {/* Controls */}
            <OrbitControls 
              enablePan={true}
              enableZoom={true}
              enableRotate={true}
              minDistance={2}
              maxDistance={20}
              autoRotate={false}
              autoRotateSpeed={0.5}
            />
            
            {/* Camera controller */}
            <CameraController />
          </Suspense>
        </Canvas>
      </div>

      {/* Model Information Panel */}
      {modelInfo && (
        <div className="model-info-panel">
          <h4>Model Information</h4>
          <div className="info-grid">
            <div className="info-item">
              <span className="info-label">File:</span>
              <span className="info-value">{modelFile.name}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Size:</span>
              <span className="info-value">{(modelFile.size / 1024 / 1024).toFixed(2)} MB</span>
            </div>
            <div className="info-item">
              <span className="info-label">Dimensions:</span>
              <span className="info-value">
                {modelInfo.size.width} × {modelInfo.size.height} × {modelInfo.size.depth}
              </span>
            </div>
            <div className="info-item">
              <span className="info-label">Animations:</span>
              <span className="info-value">{modelInfo.animations}</span>
            </div>
          </div>
        </div>
      )}

      {/* Controls Panel */}
      <div className="preview-controls">
        <div className="control-group">
          <span className="control-label">Controls:</span>
          <div className="control-hints">
            <span>🖱️ Drag to rotate</span>
            <span>🔍 Scroll to zoom</span>
            <span>⌨️ Right-click + drag to pan</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThreeJSPreview;
