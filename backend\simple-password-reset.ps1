# Simple password reset and test

Write-Host "=== Simple Password Reset and Test ===" -ForegroundColor Green

try {
    # Connect with Windows Authentication
    $connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=master;Integrated Security=True;TrustServerCertificate=True;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to SQL Server" -ForegroundColor Green
    
    $command = $connection.CreateCommand()
    
    # Check authentication mode first
    Write-Host "Checking authentication mode..." -ForegroundColor Cyan
    $command.CommandText = "SELECT SERVERPROPERTY('IsIntegratedSecurityOnly') as WindowsAuthOnly"
    $reader = $command.ExecuteReader()
    
    if ($reader.Read()) {
        $windowsOnly = $reader["WindowsAuthOnly"]
        if ($windowsOnly -eq 1) {
            Write-Host "❌ SQL Server is in Windows Authentication only mode!" -ForegroundColor Red
            Write-Host "Enabling mixed mode authentication..." -ForegroundColor Yellow
            $reader.Close()
            
            # Enable mixed mode
            $command.CommandText = "EXEC xp_instance_regwrite N'HKEY_LOCAL_MACHINE', N'Software\Microsoft\MSSQLServer\MSSQLServer', N'LoginMode', REG_DWORD, 2"
            $command.ExecuteNonQuery() | Out-Null
            Write-Host "✅ Mixed mode enabled - restarting SQL Server..." -ForegroundColor Green
            
            $connection.Close()
            
            # Restart SQL Server service
            Restart-Service -Name "MSSQL`$SQLEXPRESS" -Force
            Write-Host "✅ SQL Server restarted" -ForegroundColor Green
            
            # Wait for service to start
            Start-Sleep -Seconds 10
            
            # Reconnect
            $connection.Open()
            $command = $connection.CreateCommand()
            
        } else {
            Write-Host "✅ Mixed mode authentication is already enabled" -ForegroundColor Green
            $reader.Close()
        }
    } else {
        $reader.Close()
    }
    
    # Reset password
    Write-Host "Resetting password..." -ForegroundColor Cyan
    $command.CommandText = "ALTER LOGIN [DesignXcel] WITH PASSWORD = '****************'"
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✅ Password reset" -ForegroundColor Green
    
    # Enable login
    $command.CommandText = "ALTER LOGIN [DesignXcel] ENABLE"
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✅ Login enabled" -ForegroundColor Green
    
    $connection.Close()
    
    # Test connection
    Write-Host ""
    Write-Host "Testing SQL Server authentication..." -ForegroundColor Cyan
    
    $testConnectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;User Id=DesignXcel;Password=****************;TrustServerCertificate=True;"
    $testConnection = New-Object System.Data.SqlClient.SqlConnection($testConnectionString)
    $testConnection.Open()
    
    $testCommand = $testConnection.CreateCommand()
    $testCommand.CommandText = "SELECT DB_NAME() as DatabaseName, USER_NAME() as UserName, SYSTEM_USER as SystemUser"
    $testReader = $testCommand.ExecuteReader()
    
    if ($testReader.Read()) {
        Write-Host "✅ SQL Server authentication successful!" -ForegroundColor Green
        Write-Host "  Database: $($testReader['DatabaseName'])" -ForegroundColor Green
        Write-Host "  User: $($testReader['UserName'])" -ForegroundColor Green
        Write-Host "  System User: $($testReader['SystemUser'])" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "🎉 Database connection is working!" -ForegroundColor Green
        Write-Host "Your Node.js application should now be able to connect." -ForegroundColor Green
    }
    
    $testReader.Close()
    $testConnection.Close()
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
