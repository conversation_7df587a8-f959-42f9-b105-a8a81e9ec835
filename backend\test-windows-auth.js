const sql = require('mssql');
require('dotenv').config();

// Test with Windows Authentication first
const windowsAuthConfig = {
    server: process.env.DB_SERVER,
    port: parseInt(process.env.DB_PORT) || 1433,
    database: process.env.DB_DATABASE,
    options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true,
        trustedConnection: true
    }
};

async function testWindowsAuth() {
    console.log('Testing Windows Authentication connection...');
    console.log('Configuration:');
    console.log(`- Server: ${windowsAuthConfig.server}`);
    console.log(`- Port: ${windowsAuthConfig.port}`);
    console.log(`- Database: ${windowsAuthConfig.database}`);
    console.log('- Authentication: Windows (Trusted Connection)');
    console.log('');

    let pool;
    try {
        console.log('Attempting to connect with Windows Authentication...');
        pool = await sql.connect(windowsAuthConfig);
        console.log('✅ Successfully connected with Windows Authentication!');
        
        // Test a simple query
        console.log('\nTesting basic query...');
        const result = await pool.request().query('SELECT @@VERSION as Version, DB_NAME() as DatabaseName, GETDATE() as CurrentTime, SYSTEM_USER as CurrentUser');
        
        if (result.recordset && result.recordset.length > 0) {
            console.log('✅ Query executed successfully!');
            console.log('\nConnection Information:');
            console.log(`- Database Name: ${result.recordset[0].DatabaseName}`);
            console.log(`- Current User: ${result.recordset[0].CurrentUser}`);
            console.log(`- Current Time: ${result.recordset[0].CurrentTime}`);
        }

        // Now let's check if the DesignXcel user exists and create it if needed
        console.log('\nChecking for SQL Server login "DesignXcel"...');
        const loginCheck = await pool.request().query(`
            SELECT name, type_desc, is_disabled 
            FROM sys.server_principals 
            WHERE name = 'DesignXcel'
        `);

        if (loginCheck.recordset.length === 0) {
            console.log('❌ SQL Server login "DesignXcel" does not exist.');
            console.log('\nCreating SQL Server login and database user...');
            
            try {
                // Create SQL Server login
                await pool.request().query(`
                    CREATE LOGIN [DesignXcel] WITH PASSWORD = '${process.env.DB_PASSWORD}', 
                    DEFAULT_DATABASE = [${process.env.DB_DATABASE}], 
                    CHECK_EXPIRATION = OFF, 
                    CHECK_POLICY = OFF
                `);
                console.log('✅ SQL Server login "DesignXcel" created successfully.');

                // Create database user and assign permissions
                await pool.request().query(`
                    USE [${process.env.DB_DATABASE}];
                    CREATE USER [DesignXcel] FOR LOGIN [DesignXcel];
                    ALTER ROLE [db_owner] ADD MEMBER [DesignXcel];
                `);
                console.log('✅ Database user "DesignXcel" created and granted db_owner permissions.');
                
            } catch (createErr) {
                console.error('❌ Error creating login/user:', createErr.message);
            }
        } else {
            console.log('✅ SQL Server login "DesignXcel" exists.');
            const login = loginCheck.recordset[0];
            console.log(`- Type: ${login.type_desc}`);
            console.log(`- Disabled: ${login.is_disabled ? 'Yes' : 'No'}`);
            
            if (login.is_disabled) {
                console.log('⚠️  Login is disabled. Enabling...');
                try {
                    await pool.request().query('ALTER LOGIN [DesignXcel] ENABLE');
                    console.log('✅ Login enabled successfully.');
                } catch (enableErr) {
                    console.error('❌ Error enabling login:', enableErr.message);
                }
            }
        }

        // Check database user
        console.log('\nChecking database user permissions...');
        const userCheck = await pool.request().query(`
            USE [${process.env.DB_DATABASE}];
            SELECT 
                dp.name AS principal_name,
                dp.type_desc AS principal_type,
                r.name AS role_name
            FROM sys.database_principals dp
            LEFT JOIN sys.database_role_members rm ON dp.principal_id = rm.member_principal_id
            LEFT JOIN sys.database_principals r ON rm.role_principal_id = r.principal_id
            WHERE dp.name = 'DesignXcel'
        `);

        if (userCheck.recordset.length > 0) {
            console.log('✅ Database user "DesignXcel" exists with roles:');
            userCheck.recordset.forEach(row => {
                console.log(`  - ${row.role_name || 'No specific roles'}`);
            });
        } else {
            console.log('❌ Database user "DesignXcel" does not exist in this database.');
        }

        console.log('\n🎉 Windows Authentication test completed!');
        
    } catch (err) {
        console.error('❌ Windows Authentication failed!');
        console.error('Error details:', err.message);
        
        if (err.code) {
            console.error('Error code:', err.code);
        }
        
        console.error('\n💡 This suggests SQL Server may not be running or accessible.');
        console.error('Please ensure:');
        console.error('- SQL Server Express is installed and running');
        console.error('- The instance name "SQLEXPRESS" is correct');
        console.error('- SQL Server Browser service is running (for named instances)');
        console.error('- Windows Firewall allows SQL Server connections');
        
    } finally {
        if (pool) {
            try {
                await pool.close();
                console.log('\nConnection closed.');
            } catch (closeErr) {
                console.error('Error closing connection:', closeErr.message);
            }
        }
    }
}

// Run the test
testWindowsAuth();
