import api from './api';

// Mock data for when backend is not available - New DesignXcel Office Furniture Catalog
const mockProducts = [
    // DOORS CATEGORY
    {
        id: 1,
        name: '<PERSON><PERSON><PERSON> Single Leaf (Half Glass Frosted)',
        description: 'Premium single leaf door with half frosted glass panel. Perfect for modern office spaces requiring privacy while maintaining natural light flow.',
        price: 450.00,
        discountPrice: null,
        categoryName: 'Doors',
        sku: 'DOR-MSL-001',
        images: [
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800'
        ],
        model3D: '/models/machlan-door.glb',
        specifications: {
            material: 'Wood + Frosted Glass',
            dimensions: '210x90x4 cm',
            weight: '25 kg',
            finish: 'Satin',
            color: 'Natural Wood',
            warranty: '2 years'
        },
        stock: 5,
        featured: true
    },
    {
        id: 2,
        name: 'Panel Type Door (Solid Wood+Glass)',
        description: 'Elegant panel door combining solid wood construction with glass inserts. Ideal for executive offices and conference rooms.',
        price: 520.00,
        discountPrice: null,
        categoryName: 'Doors',
        sku: 'DOR-PTD-002',
        images: [
            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800',
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
        ],
        model3D: '/models/panel-door.glb',
        specifications: {
            material: 'Solid Wood + Clear Glass',
            dimensions: '210x90x4.5 cm',
            weight: '30 kg',
            finish: 'Glossy',
            color: 'Dark Mahogany',
            warranty: '3 years'
        },
        stock: 2,
        featured: false
    },
    {
        id: 3,
        name: 'Folding Door System (Double Opening)',
        description: 'Space-saving folding door system with double opening mechanism. Custom-made to fit your specific requirements. Price per meter.',
        price: 680.00,
        discountPrice: null,
        categoryName: 'Doors',
        sku: 'DOR-FDS-003',
        images: [
            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800',
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800'
        ],
        model3D: '/models/folding-door.glb',
        specifications: {
            material: 'Aluminum + Glass',
            dimensions: 'Custom',
            weight: '35 kg',
            finish: 'Anodized',
            color: 'Silver',
            warranty: '5 years'
        },
        stock: 0,
        featured: false,
        customizable: true
    },
    // SEATING CATEGORY
    {
        id: 4,
        name: 'Wooden Horn Chair (Scandinavian)',
        description: 'Scandinavian-inspired wooden chair with distinctive horn-shaped backrest. Combines comfort with minimalist design aesthetics.',
        price: 120.00,
        discountPrice: null,
        categoryName: 'Seating',
        sku: 'SEA-WHC-101',
        images: [
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',
            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800'
        ],
        model3D: '/models/wooden-horn-chair.glb',
        specifications: {
            material: 'Solid Oak Wood',
            dimensions: '45x50x80 cm',
            weight: '8.5 kg',
            finish: 'Oil Finish',
            color: 'Natural Oak',
            warranty: '3 years'
        },
        stock: 21,
        featured: true
    },
    {
        id: 5,
        name: 'Executive High Back (Chrome Base)',
        description: 'Premium executive chair with high backrest and chrome base. Features ergonomic design with lumbar support for extended comfort.',
        price: 280.00,
        discountPrice: null,
        categoryName: 'Seating',
        sku: 'SEA-EHC-102',
        images: [
            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800',
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800'
        ],
        model3D: '/models/executive-chair.glb',
        specifications: {
            material: 'Leather + Chrome',
            dimensions: '65x70x125 cm',
            weight: '22 kg',
            finish: 'Premium',
            color: 'Black Leather',
            warranty: '5 years'
        },
        stock: 0,
        featured: true
    },
    {
        id: 6,
        name: 'Visitor Mid Back (PU Leatherette)',
        description: 'Comfortable visitor chair with mid-back design. Upholstered in durable PU leatherette with sturdy frame construction.',
        price: 95.00,
        discountPrice: null,
        categoryName: 'Seating',
        sku: 'SEA-VMC-103',
        images: [
            'https://images.unsplash.com/photo-1541558869434-2840d308329a?w=800',
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800'
        ],
        model3D: '/models/visitor-chair.glb',
        specifications: {
            material: 'PU Leatherette + Steel',
            dimensions: '55x60x90 cm',
            weight: '12 kg',
            finish: 'Textured',
            color: 'Brown',
            warranty: '2 years'
        },
        stock: 15,
        featured: false
    },
    // TABLES CATEGORY
    {
        id: 7,
        name: 'Executive Table (2.6m+Side Table)',
        description: 'Spacious executive table set including main desk (2.6m) and matching side table. Premium finish suitable for executive offices.',
        price: 1200.00,
        discountPrice: null,
        categoryName: 'Tables',
        sku: 'TAB-EXT-201',
        images: [
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
            'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=800'
        ],
        model3D: '/models/executive-table.glb',
        specifications: {
            material: 'Premium Wood Veneer',
            dimensions: '260x120x75 + 120x60x75 cm',
            weight: '85 kg',
            finish: 'High Gloss',
            color: 'Walnut',
            warranty: '5 years'
        },
        stock: 0,
        featured: true,
        customizable: true
    },
    {
        id: 8,
        name: 'Conference Table (12-Seater)',
        description: 'Large conference table accommodating up to 12 people. Features cable management system and premium wood finish.',
        price: 2800.00,
        discountPrice: null,
        categoryName: 'Tables',
        sku: 'TAB-CON-202',
        images: [
            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800',
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
        ],
        model3D: '/models/conference-table.glb',
        specifications: {
            material: 'Solid Wood + Metal Base',
            dimensions: '400x140x75 cm',
            weight: '120 kg',
            finish: 'Satin',
            color: 'Dark Cherry',
            warranty: '10 years'
        },
        stock: 1,
        featured: true
    },
    {
        id: 9,
        name: 'Pantry Table (Black Metal Legs)',
        description: 'Modern pantry table with sleek black metal legs. Perfect for break rooms and casual meeting spaces.',
        price: 320.00,
        discountPrice: null,
        categoryName: 'Tables',
        sku: 'TAB-RPT-203',
        images: [
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',
            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800'
        ],
        model3D: '/models/pantry-table.glb',
        specifications: {
            material: 'Laminate Top + Metal',
            dimensions: '120x80x75 cm',
            weight: '25 kg',
            finish: 'Matte',
            color: 'White Top/Black Legs',
            warranty: '3 years'
        },
        stock: 4,
        featured: false
    },
    // STORAGE CATEGORY
    {
        id: 10,
        name: '4-Drawer Cabinet (Light Gray)',
        description: 'Functional 4-drawer filing cabinet in light gray finish. Features full-extension drawers with smooth-glide mechanism.',
        price: 380.00,
        discountPrice: null,
        categoryName: 'Storage',
        sku: 'STO-FLC-301',
        images: [
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800'
        ],
        model3D: '/models/four-drawer-cabinet.glb',
        specifications: {
            material: 'Steel',
            dimensions: '40x60x132 cm',
            weight: '45 kg',
            finish: 'Powder Coated',
            color: 'Light Gray',
            warranty: '5 years'
        },
        stock: 5,
        featured: false
    },
    {
        id: 11,
        name: 'Mobile Pedestal (3-Drawer Steel)',
        description: 'Compact mobile pedestal with 3 drawers and smooth-rolling casters. Ideal for under-desk storage solutions.',
        price: 150.00,
        discountPrice: null,
        categoryName: 'Storage',
        sku: 'STO-MPS-302',
        images: [
            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800',
            'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800'
        ],
        model3D: '/models/mobile-pedestal.glb',
        specifications: {
            material: 'Steel',
            dimensions: '40x60x65 cm',
            weight: '18 kg',
            finish: 'Powder Coated',
            color: 'Charcoal Gray',
            warranty: '3 years'
        },
        stock: 0,
        featured: false
    },
    {
        id: 12,
        name: 'Block Shelf (Multi-Color)',
        description: 'Modular block shelf system available in multiple colors. Create custom storage configurations to suit your space.',
        price: 210.00,
        discountPrice: null,
        categoryName: 'Storage',
        sku: 'STO-BLS-303',
        images: [
            'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=800',
            'https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800'
        ],
        model3D: '/models/block-shelf.glb',
        specifications: {
            material: 'Engineered Wood',
            dimensions: '80x30x80 cm',
            weight: '15 kg',
            finish: 'Laminate',
            color: 'Multi-Color Options',
            warranty: '2 years'
        },
        stock: 2,
        featured: false,
        customizable: true
    }
];

const mockCategories = [
    { id: 1, name: 'Doors', description: 'Office doors and entrance solutions', productCount: 3 },
    { id: 2, name: 'Seating', description: 'Office chairs and seating solutions', productCount: 3 },
    { id: 3, name: 'Tables', description: 'Office tables, desks, and work surfaces', productCount: 3 },
    { id: 4, name: 'Storage', description: 'Storage solutions and organizational furniture', productCount: 3 }
];

// Helper function to simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

export const productService = {
    async getAllProducts(params = {}) {
        try {
            // Try real API first
            const queryParams = new URLSearchParams();

            if (params.page) queryParams.append('page', params.page);
            if (params.limit) queryParams.append('limit', params.limit);
            if (params.category) queryParams.append('category', params.category);
            if (params.search) queryParams.append('search', params.search);
            if (params.minPrice) queryParams.append('minPrice', params.minPrice);
            if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice);
            if (params.inStock !== undefined) queryParams.append('inStock', params.inStock);
            if (params.sortBy) queryParams.append('sortBy', params.sortBy);
            if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

            const response = await api.get(`/products?${queryParams.toString()}`);

            if (response.data.success) {
                return {
                    products: response.data.data.products || [],
                    total: response.data.data.total || 0,
                    page: response.data.data.page || 1,
                    totalPages: response.data.data.totalPages || 1
                };
            } else {
                throw new Error(response.data.message || 'Failed to fetch products');
            }
        } catch (error) {
            console.warn('API call failed, using mock data:', error.message);

            // Fallback to mock data
            await delay(500);
            const { page = 1, limit = 10, category, search } = params;
            let filteredProducts = [...mockProducts];

            if (category) {
                filteredProducts = filteredProducts.filter(p =>
                    p.categoryName.toLowerCase().includes(category.toLowerCase())
                );
            }

            if (search) {
                filteredProducts = filteredProducts.filter(p =>
                    p.name.toLowerCase().includes(search.toLowerCase()) ||
                    p.description.toLowerCase().includes(search.toLowerCase())
                );
            }

            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

            return {
                products: paginatedProducts,
                total: filteredProducts.length,
                page: parseInt(page),
                totalPages: Math.ceil(filteredProducts.length / limit)
            };
        }
    },

    async getProductById(id) {
        try {
            // Try real API first
            const response = await api.get(`/products/${id}`);

            if (response.data.success) {
                return { product: response.data.data };
            } else {
                throw new Error(response.data.message || 'Product not found');
            }
        } catch (error) {
            console.warn('API call failed, using mock data:', error.message);

            // Fallback to mock data
            await delay(300);
            const product = mockProducts.find(p => p.id === parseInt(id));
            if (!product) {
                throw new Error('Product not found');
            }
            return { product };
        }
    },

    async getFeaturedProducts(limit = 8) {
        try {
            // Try real API first
            const response = await api.get(`/products/featured?limit=${limit}`);

            if (response.data.success) {
                return { products: response.data.data || [] };
            } else {
                throw new Error(response.data.message || 'Failed to fetch featured products');
            }
        } catch (error) {
            console.warn('API call failed, using mock data:', error.message);

            // Fallback to mock data
            await delay(400);
            const featuredProducts = mockProducts.filter(p => p.featured);
            return { products: featuredProducts };
        }
    },

    async getCategories() {
        try {
            // Try real API first
            const response = await api.get('/products/categories');

            if (response.data.success) {
                return {
                    categories: response.data.data.map(cat => ({
                        id: cat.CategoryID,
                        name: cat.CategoryName,
                        description: cat.Description,
                        productCount: cat.ProductCount
                    }))
                };
            } else {
                throw new Error(response.data.message || 'Failed to fetch categories');
            }
        } catch (error) {
            console.warn('API call failed, using mock data:', error.message);

            // Fallback to mock data
            await delay(200);
            return { categories: mockCategories };
        }
    }
};

// Export individual functions for easier imports
export const getAllProducts = productService.getAllProducts;
export const getProductById = productService.getProductById;
export const getFeaturedProducts = productService.getFeaturedProducts;
export const getCategories = productService.getCategories;
