import React, { useState, useEffect } from 'react';
import apiClient from '../../services/apiClient';
import './InventoryStatus.css';

const InventoryStatus = ({ productId, variantId, quantity = 1, onStockChange }) => {
  const [inventory, setInventory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (variantId) {
      checkVariantStock();
    } else if (productId) {
      fetchProductInventory();
    }
  }, [productId, variantId, quantity]);

  const checkVariantStock = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/api/inventory/check/${variantId}?quantity=${quantity}`);
      
      if (response.success) {
        setInventory(response.data);
        if (onStockChange) {
          onStockChange(response.data);
        }
      } else {
        setError('Failed to check stock availability');
      }
    } catch (err) {
      console.error('Error checking variant stock:', err);
      setError('Error checking stock availability');
    } finally {
      setLoading(false);
    }
  };

  const fetchProductInventory = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get(`/api/products/${productId}/inventory`);
      
      if (response.success) {
        setInventory(response.data);
        if (onStockChange) {
          onStockChange(response.data);
        }
      } else {
        setError('Failed to fetch inventory information');
      }
    } catch (err) {
      console.error('Error fetching product inventory:', err);
      setError('Error fetching inventory information');
    } finally {
      setLoading(false);
    }
  };

  const getStockStatusColor = (status) => {
    switch (status) {
      case 'In Stock':
        return '#10B981'; // Green
      case 'Low Stock':
        return '#F59E0B'; // Yellow
      case 'Critical':
        return '#EF4444'; // Red
      case 'Out of Stock':
        return '#6B7280'; // Gray
      default:
        return '#6B7280';
    }
  };

  const getStockStatusIcon = (status) => {
    switch (status) {
      case 'In Stock':
        return '✓';
      case 'Low Stock':
        return '⚠';
      case 'Critical':
        return '⚠';
      case 'Out of Stock':
        return '✗';
      default:
        return '?';
    }
  };

  if (loading) {
    return (
      <div className="inventory-status loading">
        <div className="loading-spinner"></div>
        <span>Checking availability...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="inventory-status error">
        <span className="error-icon">⚠</span>
        <span>{error}</span>
      </div>
    );
  }

  if (!inventory) {
    return (
      <div className="inventory-status unavailable">
        <span className="unavailable-icon">?</span>
        <span>Stock information unavailable</span>
      </div>
    );
  }

  // Handle single variant check
  if (inventory.variantId) {
    const { available, currentStock, stockStatus, canFulfill } = inventory;
    
    return (
      <div className="inventory-status variant">
        <div 
          className="stock-indicator"
          style={{ color: getStockStatusColor(stockStatus) }}
        >
          <span className="stock-icon">{getStockStatusIcon(stockStatus)}</span>
          <span className="stock-text">{stockStatus}</span>
        </div>
        
        <div className="stock-details">
          <span className="stock-quantity">
            {currentStock > 0 ? `${currentStock} available` : 'Out of stock'}
          </span>
          
          {quantity > 1 && (
            <span className={`fulfillment-status ${canFulfill ? 'can-fulfill' : 'cannot-fulfill'}`}>
              {canFulfill 
                ? `✓ Can fulfill quantity of ${quantity}` 
                : `✗ Cannot fulfill quantity of ${quantity}`
              }
            </span>
          )}
        </div>
      </div>
    );
  }

  // Handle product with multiple variants
  if (inventory.summary) {
    const { summary, variants } = inventory;
    
    return (
      <div className="inventory-status product">
        <div className="product-stock-summary">
          <div className="summary-item">
            <span className="summary-label">Total Stock:</span>
            <span className="summary-value">{summary.totalStock}</span>
          </div>
          
          <div className="summary-item">
            <span className="summary-label">Available:</span>
            <span className="summary-value">{summary.totalAvailable}</span>
          </div>
          
          <div className="summary-item">
            <span className="summary-label">Variants:</span>
            <span className="summary-value">
              {summary.inStockVariants}/{summary.totalVariants} in stock
            </span>
          </div>
        </div>

        {summary.lowStockVariants > 0 && (
          <div className="low-stock-warning">
            <span className="warning-icon">⚠</span>
            <span>{summary.lowStockVariants} variant(s) running low</span>
          </div>
        )}

        {variants && variants.length > 0 && (
          <div className="variants-stock">
            <h4>Variant Availability:</h4>
            <div className="variants-list">
              {variants.map(variant => (
                <div key={variant.variantId} className="variant-item">
                  <div className="variant-info">
                    <span className="variant-name">{variant.variantName}</span>
                    <span className="variant-sku">{variant.sku}</span>
                  </div>
                  
                  <div 
                    className="variant-stock"
                    style={{ color: getStockStatusColor(variant.inventory.stockStatus) }}
                  >
                    <span className="stock-icon">
                      {getStockStatusIcon(variant.inventory.stockStatus)}
                    </span>
                    <span className="stock-quantity">
                      {variant.inventory.quantityOnHand}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return null;
};

export default InventoryStatus;
