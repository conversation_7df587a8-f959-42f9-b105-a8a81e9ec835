import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import './CheckoutPage.css';

// Modern SVG Icons
const ShippingIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V10H17L15 7H3Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M3 7L5 5H13L15 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <circle cx="7.5" cy="15.5" r="1.5" stroke="currentColor" strokeWidth="2"/>
        <circle cx="16.5" cy="15.5" r="1.5" stroke="currentColor" strokeWidth="2"/>
    </svg>
);

const TruckIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M16 3H1V16H16V3Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M16 8H20L23 11V16H16V8Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <circle cx="5.5" cy="18.5" r="2.5" stroke="currentColor" strokeWidth="2"/>
        <circle cx="18.5" cy="18.5" r="2.5" stroke="currentColor" strokeWidth="2"/>
    </svg>
);

const InfoIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
        <path d="M12 16V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M12 8H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
);

const PaymentIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="4" width="22" height="16" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
        <line x1="1" y1="10" x2="23" y2="10" stroke="currentColor" strokeWidth="2"/>
    </svg>
);

const CartIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="21" r="1" stroke="currentColor" strokeWidth="2"/>
        <circle cx="19" cy="21" r="1" stroke="currentColor" strokeWidth="2"/>
        <path d="M2.05 2.05H4L6.2 12.2C6.37 13.37 7.39 14.2 8.6 14.2H19.4C20.61 14.2 21.63 13.37 21.8 12.2L23 6H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
);

const BankIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2L2 7V10H22V7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M2 17H22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M2 21H22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <line x1="5" y1="10" x2="5" y2="17" stroke="currentColor" strokeWidth="2"/>
        <line x1="9" y1="10" x2="9" y2="17" stroke="currentColor" strokeWidth="2"/>
        <line x1="15" y1="10" x2="15" y2="17" stroke="currentColor" strokeWidth="2"/>
        <line x1="19" y1="10" x2="19" y2="17" stroke="currentColor" strokeWidth="2"/>
    </svg>
);

const CreditCardIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="4" width="22" height="16" rx="2" ry="2" stroke="currentColor" strokeWidth="2"/>
        <line x1="1" y1="10" x2="23" y2="10" stroke="currentColor" strokeWidth="2"/>
        <line x1="5" y1="14" x2="7" y2="14" stroke="currentColor" strokeWidth="2"/>
        <line x1="9" y1="14" x2="13" y2="14" stroke="currentColor" strokeWidth="2"/>
    </svg>
);

const CheckoutPage = () => {
    const navigate = useNavigate();
    const { items, getSubtotal, getTax, getShipping, getTotal, clearCart } = useCart();
    
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [paymentMethod, setPaymentMethod] = useState('paymongo');
    
    const [shippingAddress, setShippingAddress] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        province: '',
        postalCode: '',
        country: 'Philippines'
    });

    const [billingAddress, setBillingAddress] = useState({
        sameAsShipping: true,
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        province: '',
        postalCode: '',
        country: 'Philippines'
    });

    useEffect(() => {
        // Redirect if cart is empty
        if (!items || items.length === 0) {
            navigate('/cart');
        }
    }, [items, navigate]);

    const handleInputChange = (section, field, value) => {
        if (section === 'shipping') {
            setShippingAddress(prev => ({ ...prev, [field]: value }));
        } else if (section === 'billing') {
            setBillingAddress(prev => ({ ...prev, [field]: value }));
        }
    };

    const handlePaymentSuccess = (paymentData) => {
        console.log('Payment successful:', paymentData);
        clearCart();
        navigate('/order-confirmation', { state: { paymentData } });
    };

    const handlePaymentError = (error) => {
        console.error('Payment error:', error);
        setError('Payment failed. Please try again.');
    };

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP'
        }).format(price);
    };

    if (!items || items.length === 0) {
        return (
            <div className="checkout-page">
                <div className="empty-cart">
                    <h2>Your cart is empty</h2>
                    <button onClick={() => navigate('/products')} className="btn btn-primary">
                        Continue Shopping
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="checkout-page">
            <div className="checkout-container">
                {/* Main Checkout Form */}
                <div className="checkout-form-area">
                    {error && (
                        <div className="error-alert">
                            <span className="error-icon">⚠️</span>
                            <span className="error-text">{error}</span>
                        </div>
                    )}

                    {/* Shipping Address Section */}
                    <div className="checkout-section">
                        <div className="section-header">
                            <div className="section-number">1</div>
                            <ShippingIcon />
                            <span>Shipping Address</span>
                        </div>
                        <div className="section-content">
                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="firstName">First Name *</label>
                                    <input
                                        type="text"
                                        id="firstName"
                                        value={shippingAddress.firstName}
                                        onChange={(e) => handleInputChange('shipping', 'firstName', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="lastName">Last Name *</label>
                                    <input
                                        type="text"
                                        id="lastName"
                                        value={shippingAddress.lastName}
                                        onChange={(e) => handleInputChange('shipping', 'lastName', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="email">Email *</label>
                                    <input
                                        type="email"
                                        id="email"
                                        value={shippingAddress.email}
                                        onChange={(e) => handleInputChange('shipping', 'email', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="phone">Phone *</label>
                                    <input
                                        type="tel"
                                        id="phone"
                                        value={shippingAddress.phone}
                                        onChange={(e) => handleInputChange('shipping', 'phone', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                            </div>

                            <div className="form-row single">
                                <div className="form-group">
                                    <label htmlFor="address">Address *</label>
                                    <input
                                        type="text"
                                        id="address"
                                        value={shippingAddress.address}
                                        onChange={(e) => handleInputChange('shipping', 'address', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="city">City *</label>
                                    <input
                                        type="text"
                                        id="city"
                                        value={shippingAddress.city}
                                        onChange={(e) => handleInputChange('shipping', 'city', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="province">Province *</label>
                                    <input
                                        type="text"
                                        id="province"
                                        value={shippingAddress.province}
                                        onChange={(e) => handleInputChange('shipping', 'province', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label htmlFor="postalCode">Postal Code *</label>
                                    <input
                                        type="text"
                                        id="postalCode"
                                        value={shippingAddress.postalCode}
                                        onChange={(e) => handleInputChange('shipping', 'postalCode', e.target.value)}
                                        required
                                        disabled={loading}
                                    />
                                </div>
                                <div className="form-group">
                                    <label htmlFor="country">Country *</label>
                                    <select
                                        id="country"
                                        value={shippingAddress.country}
                                        onChange={(e) => handleInputChange('shipping', 'country', e.target.value)}
                                        required
                                        disabled={loading}
                                    >
                                        <option value="Philippines">Philippines</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Shipping Methods Section */}
                    <div className="checkout-section">
                        <div className="section-header">
                            <div className="section-number">2</div>
                            <TruckIcon />
                            <span>Shipping Methods</span>
                        </div>
                        <div className="section-content">
                            <div className="payment-methods">
                                <div className="payment-method selected">
                                    <div className="payment-method-header">
                                        <div className="payment-method-radio"></div>
                                        <span className="payment-method-name">Manual Shipping Costs Calculation</span>
                                    </div>
                                    <div className="payment-method-description">
                                        To keep costs as low as possible, our customer service will manually calculate 
                                        shipping costs after an order is placed (at the moment they show ₱0.00). After 
                                        calculating costs, you will decide whether to confirm an order or change.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Additional Information Section */}
                    <div className="checkout-section">
                        <div className="section-header">
                            <div className="section-number">3</div>
                            <InfoIcon />
                            <span>Additional Information</span>
                        </div>
                        <div className="section-content">
                            <div className="form-row single">
                                <div className="form-group">
                                    <label htmlFor="orderComments">Order Comments</label>
                                    <textarea
                                        id="orderComments"
                                        rows="4"
                                        placeholder="Notes about your order, e.g. special notes for delivery."
                                        disabled={loading}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Payment Method Section */}
                    <div className="checkout-section">
                        <div className="section-header">
                            <div className="section-number">4</div>
                            <PaymentIcon />
                            <span>Payment Method</span>
                        </div>
                        <div className="section-content">
                            <div className="payment-methods">
                                <div
                                    className={`payment-method ${paymentMethod === 'bank' ? 'selected' : ''}`}
                                    onClick={() => setPaymentMethod('bank')}
                                >
                                    <div className="payment-method-header">
                                        <div className="payment-method-radio"></div>
                                        <BankIcon />
                                        <span className="payment-method-name">Bank Transfer</span>
                                    </div>
                                    <div className="payment-method-description">
                                        Transfer the money to the account indicated on the invoice. Your order will be
                                        processed after the payment is credited.
                                    </div>
                                </div>

                                <div
                                    className={`payment-method ${paymentMethod === 'paymongo' ? 'selected' : ''}`}
                                    onClick={() => setPaymentMethod('paymongo')}
                                >
                                    <div className="payment-method-header">
                                        <div className="payment-method-radio"></div>
                                        <CreditCardIcon />
                                        <span className="payment-method-name">PayMongo</span>
                                    </div>
                                    <div className="payment-method-description">
                                        Pay securely using PayMongo payment gateway with credit/debit cards, GCash, and other payment methods.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Order Summary Sidebar */}
                <div className="order-summary">
                    <div className="order-summary-header">
                        <h3>
                            <span className="summary-icon">
                                <CartIcon />
                            </span>
                            ORDER SUMMARY
                        </h3>
                    </div>
                    <div className="order-summary-content">
                        {/* Cart Items */}
                        <div className="cart-items">
                            {items.map((item) => (
                                <div key={item.id} className="cart-item">
                                    <img 
                                        src={item.image || '/api/placeholder/60/60'} 
                                        alt={item.name}
                                        className="cart-item-image"
                                    />
                                    <div className="cart-item-details">
                                        <div className="cart-item-name">{item.name}</div>
                                        <div className="cart-item-quantity">Qty: {item.quantity}</div>
                                    </div>
                                    <div className="cart-item-price">
                                        {formatPrice(item.price * item.quantity)}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Order Totals */}
                        <div className="order-totals">
                            <div className="total-row">
                                <span>Cart Subtotal:</span>
                                <span>{formatPrice(getSubtotal())}</span>
                            </div>
                            <div className="total-row">
                                <span>Shipping:</span>
                                <span>{formatPrice(getShipping())}</span>
                            </div>
                            <div className="total-row final">
                                <span>Order Total Excl. Tax:</span>
                                <span>{formatPrice(getTotal())}</span>
                            </div>
                            <div className="total-row final">
                                <span>Order Total Incl. Tax:</span>
                                <span>{formatPrice(getTotal())}</span>
                            </div>
                        </div>

                        {/* PayMongo Checkout */}
                        {paymentMethod === 'paymongo' && (
                            <button
                                className="place-order-btn"
                                disabled={loading}
                                onClick={() => {
                                    // Navigate to modern payment page with order data
                                    const orderData = {
                                        items: items,
                                        shippingAddress: shippingAddress,
                                        subtotal: getSubtotal(),
                                        tax: getTax(),
                                        shipping: getShipping(),
                                        total: getTotal(),
                                        orderId: `ORDER_${Date.now()}`,
                                        currency: 'PHP'
                                    };
                                    navigate('/payment', { state: { orderData } });
                                }}
                            >
                                Proceed to Payment
                            </button>
                        )}

                        {/* Place Order Button for Bank Transfer */}
                        {paymentMethod === 'bank' && (
                            <button 
                                className="place-order-btn"
                                disabled={loading}
                                onClick={() => {
                                    // Handle bank transfer order placement
                                    console.log('Bank transfer order placed');
                                }}
                            >
                                {loading ? 'Processing...' : 'Place Order'}
                            </button>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CheckoutPage;
