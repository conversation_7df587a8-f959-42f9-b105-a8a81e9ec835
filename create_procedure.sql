CREATE PROCEDURE AddProductStock
    @ProductID INT,
    @QuantityToAdd INT,
    @PerformedBy INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Validate inputs
    IF @QuantityToAdd <= 0
    BEGIN
        RAISERROR('QuantityToAdd must be greater than 0', 16, 1);
        RETURN;
    END;

    -- Start a transaction
    BEGIN TRANSACTION;

    -- Check material availability
    IF EXISTS (
        SELECT 1
        FROM ProductMaterials pm
        JOIN RawMaterials rm ON pm.MaterialID = rm.MaterialID
        WHERE pm.ProductID = @ProductID
        AND (pm.QuantityRequired * @QuantityToAdd) > rm.QuantityAvailable
    )
    BEGIN
        ROLLBACK;
        RAISERROR('Not enough raw materials available for production.', 16, 1);
        RETURN;
    END;

    -- Deduct raw materials
    UPDATE rm
    SET rm.QuantityAvailable = rm.QuantityAvailable - (pm.QuantityRequired * @QuantityToAdd),
        rm.LastUpdated = GETDATE()
    FROM RawMaterials rm
    JOIN ProductMaterials pm ON rm.MaterialID = pm.MaterialID
    WHERE pm.ProductID = @ProductID;

    -- Increase product stock
    UPDATE Products
    SET StockQuantity = StockQuantity + @QuantityToAdd
    WHERE ProductID = @ProductID;

    -- Log adjustment (optional)
    INSERT INTO ProductStockAdjustments (ProductID, QuantityAdded, Reason, PerformedBy)
    VALUES (@ProductID, @QuantityToAdd, 'Production', @PerformedBy);

    COMMIT;
END;
