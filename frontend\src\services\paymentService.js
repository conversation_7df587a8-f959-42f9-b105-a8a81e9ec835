import apiClient from './apiClient';
import apiConfig from './apiConfig';

class PaymentService {
    constructor() {
        // Check if payment processing is enabled
        if (!apiConfig.isFeatureEnabled('paymentProcessing')) {
            console.warn('⚠️ Payment processing is disabled');
        }

        // Get payment configuration
        this.config = apiConfig.getPaymentConfig();

        // Supported Philippine banks for online banking
        this.supportedBanks = [
            {
                id: 'bpi',
                name: 'Bank of the Philippine Islands (BPI)',
                shortName: 'BPI',
                code: 'bpi',
                logo: '/images/banks/bpi-logo.png',
                color: '#E31E24',
                processingTime: '1-3 business days',
                features: ['Real-time processing', 'Mobile banking', '24/7 availability']
            },
            {
                id: 'bdo',
                name: 'Banco de Oro (BDO)',
                shortName: 'BDO',
                code: 'bdo',
                logo: '/images/banks/bdo-logo.png',
                color: '#003087',
                processingTime: '1-3 business days',
                features: ['Instant verification', 'SMS notifications', 'Secure banking']
            },
            {
                id: 'metrobank',
                name: 'Metropolitan Bank & Trust Co.',
                shortName: 'Metrobank',
                code: 'metrobank',
                logo: '/images/banks/metrobank-logo.png',
                color: '#FF6B00',
                processingTime: '1-2 business days',
                features: ['Fast processing', 'Online banking', 'Mobile app support']
            },
            {
                id: 'unionbank',
                name: 'Union Bank of the Philippines',
                shortName: 'UnionBank',
                code: 'unionbank',
                logo: '/images/banks/unionbank-logo.png',
                color: '#00A651',
                processingTime: '1-2 business days',
                features: ['Digital banking', 'QR payments', 'Real-time transfers']
            },
            {
                id: 'landbank',
                name: 'Land Bank of the Philippines',
                shortName: 'LandBank',
                code: 'landbank',
                logo: '/images/banks/landbank-logo.png',
                color: '#228B22',
                processingTime: '2-3 business days',
                features: ['Government banking', 'Agricultural loans', 'Rural banking']
            },
            {
                id: 'pnb',
                name: 'Philippine National Bank',
                shortName: 'PNB',
                code: 'pnb',
                logo: '/images/banks/pnb-logo.png',
                color: '#1E3A8A',
                processingTime: '1-3 business days',
                features: ['Traditional banking', 'International remittance', 'Business banking']
            },
            {
                id: 'rcbc',
                name: 'Rizal Commercial Banking Corporation',
                shortName: 'RCBC',
                code: 'rcbc',
                logo: '/images/banks/rcbc-logo.png',
                color: '#C41E3A',
                processingTime: '1-2 business days',
                features: ['Commercial banking', 'Investment services', 'Digital solutions']
            },
            {
                id: 'securitybank',
                name: 'Security Bank Corporation',
                shortName: 'Security Bank',
                code: 'securitybank',
                logo: '/images/banks/securitybank-logo.png',
                color: '#FF8C00',
                processingTime: '1-2 business days',
                features: ['Premium banking', 'Investment banking', 'Wealth management']
            }
        ];

        if (apiConfig.debugMode) {
            console.log('💳 Payment Service initialized with bank support');
        }
    }





    /**
     * Get supported banks for online banking
     * @returns {Array} List of supported banks
     */
    getSupportedBanks() {
        return this.supportedBanks;
    }

    /**
     * Get bank by ID
     * @param {string} bankId - Bank ID
     * @returns {Object|null} Bank information
     */
    getBankById(bankId) {
        return this.supportedBanks.find(bank => bank.id === bankId) || null;
    }

    /**
     * Create PayMongo payment link for order
     * @param {Object} orderData - Order data
     * @returns {Promise<Object>} Payment link data
     */
    async createPaymentLink(orderData) {
        try {
            if (!apiConfig.isFeatureEnabled('paymentProcessing')) {
                throw new Error('Payment processing is disabled');
            }

            // Direct PayMongo API integration (frontend-only)
            const paymongoPublicKey = apiConfig.payment.paymongoPublicKey;
            if (!paymongoPublicKey) {
                throw new Error('PayMongo public key not configured');
            }

            // Determine payment method and prepare data accordingly
            const paymentMethod = orderData.paymentMethod || 'card';
            let paymentLinkData;

            if (paymentMethod === 'bank') {
                // Bank transfer payment
                paymentLinkData = await this.createBankPaymentLink(orderData);
            } else {
                // Card or other payment methods
                paymentLinkData = await this.createCardPaymentLink(orderData);
            }

            // For now, return a mock response since we're removing backend
            // In a real implementation, you would call PayMongo API directly
            return {
                success: true,
                paymentLink: {
                    id: `pl_${Date.now()}`,
                    url: paymentLinkData.url,
                    amount: orderData.totalAmount,
                    description: paymentLinkData.description,
                    paymentMethod: paymentMethod,
                    bankCode: orderData.bankCode || null
                }
            };
        } catch (error) {
            console.error('Create payment link error:', error);
            throw error;
        }
    }

    /**
     * Create bank payment link data
     * @param {Object} orderData - Order data
     * @returns {Promise<Object>} Bank payment link data
     */
    async createBankPaymentLink(orderData) {
        const bankCode = orderData.bankCode;
        const bank = this.getBankById(bankCode);

        if (!bank) {
            throw new Error('Invalid bank selected');
        }

        // PayMongo bank transfer payment link structure
        const paymentLinkData = {
            data: {
                attributes: {
                    amount: orderData.totalAmount, // Amount in centavos
                    description: `Order ${orderData.orderId} - ${bank.shortName} Bank Transfer`,
                    remarks: `Bank transfer payment for order ${orderData.orderId} via ${bank.name}`,
                    payment_method_types: ['bank_transfer'],
                    payment_method_options: {
                        bank_transfer: {
                            bank_code: bank.code
                        }
                    },
                    redirect: {
                        success: `${window.location.origin}/order-success?payment_method=bank&bank=${bankCode}`,
                        failed: `${window.location.origin}/payment-failed?payment_method=bank&bank=${bankCode}`
                    },
                    checkout_url: `${window.location.origin}/order-success`,
                    metadata: {
                        order_id: orderData.orderId,
                        payment_method: 'bank',
                        bank_code: bankCode,
                        bank_name: bank.name
                    }
                }
            },
            url: `https://checkout.paymongo.com/pay/bank_${Date.now()}`,
            description: `Order ${orderData.orderId} - ${bank.shortName} Bank Transfer`
        };

        return paymentLinkData;
    }

    /**
     * Create card payment link data
     * @param {Object} orderData - Order data
     * @returns {Promise<Object>} Card payment link data
     */
    async createCardPaymentLink(orderData) {
        const paymentLinkData = {
            data: {
                attributes: {
                    amount: orderData.totalAmount, // Amount in centavos
                    description: `Order ${orderData.orderId}`,
                    remarks: `Payment for order ${orderData.orderId}`,
                    payment_method_types: ['card', 'gcash', 'grabpay', 'paymaya'],
                    redirect: {
                        success: `${window.location.origin}/order-success`,
                        failed: `${window.location.origin}/payment-failed`
                    },
                    checkout_url: `${window.location.origin}/order-success`,
                    metadata: {
                        order_id: orderData.orderId,
                        payment_method: orderData.paymentMethod || 'card'
                    }
                }
            },
            url: `https://checkout.paymongo.com/pay/${Date.now()}`,
            description: `Order ${orderData.orderId}`
        };

        return paymentLinkData;
    }

    /**
     * Get payment status for an order
     * @param {string} orderId - Order ID
     * @param {string} paymentLinkId - Payment link ID
     * @param {string} paymentMethod - Payment method
     * @returns {Promise<Object>} Payment status data
     */
    async getPaymentStatus(orderId, paymentLinkId, paymentMethod = 'card') {
        try {
            // Frontend-only implementation - in a real app, you would check PayMongo API directly
            // Simulate different status scenarios based on payment method
            const statuses = this.getBankPaymentStatuses(paymentMethod);
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

            return {
                success: true,
                status: randomStatus.status,
                statusDescription: randomStatus.description,
                orderId: orderId,
                paymentLinkId: paymentLinkId,
                paymentMethod: paymentMethod,
                timestamp: new Date().toISOString(),
                estimatedCompletion: randomStatus.estimatedCompletion
            };
        } catch (error) {
            console.error('Get payment status error:', error);
            throw error;
        }
    }

    /**
     * Get possible payment statuses for different payment methods
     * @param {string} paymentMethod - Payment method
     * @returns {Array} Possible statuses
     */
    getBankPaymentStatuses(paymentMethod) {
        if (paymentMethod === 'bank') {
            return [
                {
                    status: 'pending',
                    description: 'Bank transfer is being processed',
                    estimatedCompletion: '1-3 business days'
                },
                {
                    status: 'processing',
                    description: 'Payment is being verified by the bank',
                    estimatedCompletion: '2-24 hours'
                },
                {
                    status: 'completed',
                    description: 'Bank transfer completed successfully',
                    estimatedCompletion: null
                },
                {
                    status: 'failed',
                    description: 'Bank transfer failed or was declined',
                    estimatedCompletion: null
                },
                {
                    status: 'expired',
                    description: 'Payment link has expired',
                    estimatedCompletion: null
                }
            ];
        } else {
            return [
                {
                    status: 'pending',
                    description: 'Payment is being processed',
                    estimatedCompletion: '5-10 minutes'
                },
                {
                    status: 'completed',
                    description: 'Payment completed successfully',
                    estimatedCompletion: null
                },
                {
                    status: 'failed',
                    description: 'Payment failed or was declined',
                    estimatedCompletion: null
                }
            ];
        }
    }

    /**
     * Handle bank payment callback
     * @param {Object} callbackData - Callback data from bank
     * @returns {Promise<Object>} Processed callback result
     */
    async handleBankPaymentCallback(callbackData) {
        try {
            const { orderId, paymentLinkId, status, bankCode, transactionId } = callbackData;

            // Validate callback data
            if (!orderId || !paymentLinkId || !status) {
                throw new Error('Invalid callback data');
            }

            // Process the callback based on status
            const result = {
                success: true,
                orderId: orderId,
                paymentLinkId: paymentLinkId,
                status: status,
                bankCode: bankCode,
                transactionId: transactionId,
                processedAt: new Date().toISOString()
            };

            // Update order status in localStorage (frontend-only)
            this.updateOrderStatus(orderId, status, result);

            return result;
        } catch (error) {
            console.error('Bank payment callback error:', error);
            throw error;
        }
    }

    /**
     * Update order status in localStorage
     * @param {string} orderId - Order ID
     * @param {string} status - New status
     * @param {Object} paymentData - Payment data
     */
    updateOrderStatus(orderId, status, paymentData) {
        try {
            const orders = JSON.parse(localStorage.getItem('orders') || '[]');
            const orderIndex = orders.findIndex(order => order.orderId === orderId);

            if (orderIndex !== -1) {
                orders[orderIndex].status = status;
                orders[orderIndex].paymentData = paymentData;
                orders[orderIndex].updatedAt = new Date().toISOString();
                localStorage.setItem('orders', JSON.stringify(orders));
            }
        } catch (error) {
            console.error('Update order status error:', error);
        }
    }

    /**
     * Cancel payment link
     * @param {string} paymentLinkId - Payment link ID
     * @returns {Promise<Object>} Cancellation result
     */
    async cancelPaymentLink(paymentLinkId) {
        try {
            // Frontend-only implementation - in a real app, you would call PayMongo API directly
            console.log('Payment link cancelled:', paymentLinkId);
            return {
                success: true,
                message: 'Payment link cancelled',
                paymentLinkId: paymentLinkId
            };
        } catch (error) {
            console.error('Cancel payment link error:', error);
            throw error;
        }
    }

    /**
     * Calculate payment fees
     * @param {number} amount - Amount in centavos
     * @param {string} paymentMethod - Payment method
     * @param {string} bankCode - Bank code for bank transfers
     * @returns {Promise<Object>} Fee calculation
     */
    async calculateFees(amount, paymentMethod = 'card', bankCode = null) {
        try {
            // Frontend-only fee calculation based on PayMongo's fee structure
            const amountInPHP = amount / 100;
            let feePercentage = 0;
            let fixedFee = 0;
            let description = '';

            switch (paymentMethod) {
                case 'card':
                    feePercentage = 0.035; // 3.5%
                    fixedFee = 15; // ₱15
                    description = 'Credit/Debit Card Processing Fee';
                    break;
                case 'gcash':
                    feePercentage = 0.025; // 2.5%
                    description = 'GCash Processing Fee';
                    break;
                case 'grabpay':
                    feePercentage = 0.025; // 2.5%
                    description = 'GrabPay Processing Fee';
                    break;
                case 'paymaya':
                    feePercentage = 0.025; // 2.5%
                    description = 'PayMaya Processing Fee';
                    break;
                case 'bank':
                    // Bank transfer fees vary by bank
                    const bankFees = this.getBankTransferFees(bankCode);
                    feePercentage = bankFees.percentage;
                    fixedFee = bankFees.fixed;
                    description = `${bankFees.bankName} Bank Transfer Fee`;
                    break;
                default:
                    feePercentage = 0.035;
                    fixedFee = 15;
                    description = 'Payment Processing Fee';
            }

            const percentageFee = amountInPHP * feePercentage;
            const totalFee = percentageFee + fixedFee;
            const totalAmount = amountInPHP + totalFee;

            return {
                success: true,
                data: {
                    amount: amountInPHP,
                    percentageFee: percentageFee,
                    fixedFee: fixedFee,
                    totalFee: totalFee,
                    totalAmount: totalAmount,
                    paymentMethod: paymentMethod,
                    bankCode: bankCode,
                    description: description,
                    breakdown: {
                        subtotal: amountInPHP,
                        processingFee: totalFee,
                        total: totalAmount
                    }
                }
            };
        } catch (error) {
            console.error('Calculate fees error:', error);
            throw error;
        }
    }

    /**
     * Get bank transfer fees by bank code
     * @param {string} bankCode - Bank code
     * @returns {Object} Bank fee structure
     */
    getBankTransferFees(bankCode) {
        const bank = this.getBankById(bankCode);

        // Default bank transfer fees
        const defaultFees = {
            percentage: 0.015, // 1.5%
            fixed: 10, // ₱10
            bankName: 'Bank'
        };

        if (!bank) {
            return defaultFees;
        }

        // Bank-specific fee structures
        const bankFees = {
            'bpi': { percentage: 0.015, fixed: 10, bankName: 'BPI' },
            'bdo': { percentage: 0.015, fixed: 10, bankName: 'BDO' },
            'metrobank': { percentage: 0.012, fixed: 8, bankName: 'Metrobank' },
            'unionbank': { percentage: 0.010, fixed: 5, bankName: 'UnionBank' },
            'landbank': { percentage: 0.018, fixed: 12, bankName: 'LandBank' },
            'pnb': { percentage: 0.015, fixed: 10, bankName: 'PNB' },
            'rcbc': { percentage: 0.015, fixed: 10, bankName: 'RCBC' },
            'securitybank': { percentage: 0.012, fixed: 8, bankName: 'Security Bank' }
        };

        return bankFees[bankCode] || defaultFees;
    }

    /**
     * Create an order (frontend-only, no backend persistence)
     * @param {Object} orderData - Order data
     * @returns {Promise<Object>} Created order data
     */
    async createOrder(orderData) {
        try {
            if (!apiConfig.isFeatureEnabled('paymentProcessing')) {
                throw new Error('Payment processing is disabled');
            }

            // Frontend-only order creation - store in localStorage for demo purposes
            const orderId = `ORDER_${Date.now()}`;
            const order = {
                ...orderData,
                orderId: orderId,
                status: 'pending',
                createdAt: new Date().toISOString()
            };

            // Store order in localStorage
            const existingOrders = JSON.parse(localStorage.getItem('orders') || '[]');
            existingOrders.push(order);
            localStorage.setItem('orders', JSON.stringify(existingOrders));

            return {
                success: true,
                data: order
            };
        } catch (error) {
            console.error('Create order error:', error);
            throw error;
        }
    }

    /**
     * Get user addresses
     * @returns {Promise<Object>} User addresses
     */
    async getUserAddresses() {
        try {
            const response = await this.api.get('/users/addresses');
            return response.data;
        } catch (error) {
            console.error('Get user addresses error:', error);
            throw new Error(error.response?.data?.error || 'Failed to get user addresses');
        }
    }

    /**
     * Create user address
     * @param {Object} addressData - Address data
     * @returns {Promise<Object>} Created address data
     */
    async createUserAddress(addressData) {
        try {
            const response = await this.api.post('/users/addresses', addressData);
            return response.data;
        } catch (error) {
            console.error('Create user address error:', error);
            throw new Error(error.response?.data?.error || 'Failed to create address');
        }
    }

    /**
     * Format amount for display
     * @param {number} amount - Amount in PHP
     * @param {string} currency - Currency code
     * @returns {string} Formatted amount
     */
    formatAmount(amount, currency = 'PHP') {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }

    /**
     * Get payment method display name
     * @param {string} paymentMethod - Payment method ID
     * @returns {string} Display name
     */
    getPaymentMethodDisplayName(paymentMethod) {
        const displayNames = {
            'card': 'Credit/Debit Card',
            'gcash': 'GCash',
            'grabpay': 'GrabPay',
            'paymaya': 'PayMaya',
            'bank': 'Online Banking'
        };

        return displayNames[paymentMethod] || 'Unknown Payment Method';
    }



    /**
     * Get payment method display name
     * @param {string} type - Payment method type
     * @returns {string} Display name
     */
    getPaymentMethodDisplayName(type) {
        const displayNames = {
            'card': 'Credit/Debit Card',
            'gcash': 'GCash',
            'grabpay': 'GrabPay',
            'paymaya': 'PayMaya',
            'bank_transfer': 'Bank Transfer'
        };
        return displayNames[type] || type;
    }

    /**
     * Get payment method icon
     * @param {string} type - Payment method type
     * @returns {string} Icon class or emoji
     */
    getPaymentMethodIcon(type) {
        const icons = {
            'card': '💳',
            'gcash': '📱',
            'grabpay': '🚗',
            'paymaya': '💰',
            'bank_transfer': '🏦'
        };
        return icons[type] || '💳';
    }

    /**
     * Validate card number (basic Luhn algorithm)
     * @param {string} cardNumber - Card number
     * @returns {boolean} Is valid
     */
    validateCardNumber(cardNumber) {
        const cleanNumber = cardNumber.replace(/\s/g, '');
        if (!/^\d{13,19}$/.test(cleanNumber)) return false;

        let sum = 0;
        let isEven = false;
        
        for (let i = cleanNumber.length - 1; i >= 0; i--) {
            let digit = parseInt(cleanNumber[i]);
            
            if (isEven) {
                digit *= 2;
                if (digit > 9) digit -= 9;
            }
            
            sum += digit;
            isEven = !isEven;
        }
        
        return sum % 10 === 0;
    }

    /**
     * Format card number for display
     * @param {string} cardNumber - Card number
     * @returns {string} Formatted card number
     */
    formatCardNumber(cardNumber) {
        const cleanNumber = cardNumber.replace(/\s/g, '');
        return cleanNumber.replace(/(.{4})/g, '$1 ').trim();
    }

    /**
     * Get card brand from number
     * @param {string} cardNumber - Card number
     * @returns {string} Card brand
     */
    getCardBrand(cardNumber) {
        const cleanNumber = cardNumber.replace(/\s/g, '');
        
        if (/^4/.test(cleanNumber)) return 'visa';
        if (/^5[1-5]/.test(cleanNumber)) return 'mastercard';
        if (/^3[47]/.test(cleanNumber)) return 'amex';
        if (/^6(?:011|5)/.test(cleanNumber)) return 'discover';
        
        return 'unknown';
    }
}

export default new PaymentService();
