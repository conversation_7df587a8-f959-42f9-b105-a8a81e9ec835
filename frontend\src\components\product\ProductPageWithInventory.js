import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import apiClient from '../../services/apiClient';
import InventoryStatus from './InventoryStatus';
import { useCart } from '../../hooks/useCart';
import { toast } from 'react-toastify';
// import './ProductPageWithInventory.css';

const ProductPageWithInventory = () => {
  const { id } = useParams();
  const { addToCart, isInCart, getItemQuantity } = useCart();
  
  const [product, setProduct] = useState(null);
  const [inventory, setInventory] = useState(null);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(true);
  const [addingToCart, setAddingToCart] = useState(false);
  const [stockInfo, setStockInfo] = useState(null);

  useEffect(() => {
    if (id) {
      fetchProduct();
      fetchInventory();
    }
  }, [id]);

  useEffect(() => {
    if (inventory && inventory.variants && inventory.variants.length > 0) {
      // Select first available variant by default
      const availableVariant = inventory.variants.find(v => v.inventory.inStock) || inventory.variants[0];
      setSelectedVariant(availableVariant);
    }
  }, [inventory]);

  const fetchProduct = async () => {
    try {
      const response = await apiClient.get(`/api/products/${id}`);
      if (response.success) {
        setProduct(response.data);
      } else {
        toast.error('Product not found');
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      toast.error('Error loading product');
    }
  };

  const fetchInventory = async () => {
    try {
      const response = await apiClient.get(`/api/products/${id}/inventory`);
      if (response.success) {
        setInventory(response.data);
      } else {
        console.warn('Inventory data not available');
      }
    } catch (error) {
      console.error('Error fetching inventory:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVariantChange = (variant) => {
    setSelectedVariant(variant);
    setQuantity(1); // Reset quantity when variant changes
  };

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity < 1) return;
    
    if (stockInfo && newQuantity > stockInfo.availableStock) {
      toast.warning(`Only ${stockInfo.availableStock} items available`);
      return;
    }
    
    setQuantity(newQuantity);
  };

  const handleStockChange = (stockData) => {
    setStockInfo(stockData);
  };

  const handleAddToCart = async () => {
    if (!selectedVariant) {
      toast.error('Please select a variant');
      return;
    }

    if (!stockInfo || !stockInfo.available) {
      toast.error('Item is not available');
      return;
    }

    setAddingToCart(true);
    try {
      const result = await addToCart({
        variantId: selectedVariant.variantId,
        productName: product.name,
        variantName: selectedVariant.variantName,
        price: selectedVariant.price,
        quantity: quantity,
        imageUrl: product.imageUrl
      });

      if (result.success) {
        // Success message is handled by the cart service
      } else {
        toast.error(result.message || 'Failed to add to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast.error('Error adding to cart');
    } finally {
      setAddingToCart(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(price);
  };

  if (loading) {
    return (
      <div className="product-page loading">
        <div className="loading-spinner"></div>
        <p>Loading product...</p>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="product-page error">
        <h2>Product Not Found</h2>
        <p>The requested product could not be found.</p>
      </div>
    );
  }

  const currentCartQuantity = selectedVariant ? getItemQuantity(selectedVariant.variantId) : 0;
  const isInCartAlready = selectedVariant ? isInCart(selectedVariant.variantId) : false;

  return (
    <div className="product-page">
      <div className="product-container">
        <div className="product-image-section">
          <div className="product-image">
            {product.imageUrl ? (
              <img src={product.imageUrl} alt={product.name} />
            ) : (
              <div className="image-placeholder">
                <span>No Image Available</span>
              </div>
            )}
          </div>
        </div>

        <div className="product-details-section">
          <div className="product-header">
            <h1 className="product-title">{product.name}</h1>
            <p className="product-description">{product.description}</p>
          </div>

          {/* Variant Selection */}
          {inventory && inventory.variants && inventory.variants.length > 1 && (
            <div className="variant-selection">
              <h3>Select Variant:</h3>
              <div className="variants-grid">
                {inventory.variants.map((variant) => (
                  <button
                    key={variant.variantId}
                    className={`variant-option ${selectedVariant?.variantId === variant.variantId ? 'selected' : ''} ${!variant.inventory.inStock ? 'out-of-stock' : ''}`}
                    onClick={() => handleVariantChange(variant)}
                    disabled={!variant.inventory.inStock}
                  >
                    <div className="variant-info">
                      <span className="variant-name">{variant.variantName}</span>
                      <span className="variant-price">{formatPrice(variant.price)}</span>
                      <span className="variant-stock">
                        {variant.inventory.inStock 
                          ? `${variant.inventory.quantityOnHand} available` 
                          : 'Out of stock'
                        }
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Price Display */}
          {selectedVariant && (
            <div className="price-section">
              <span className="current-price">{formatPrice(selectedVariant.price)}</span>
              {selectedVariant.originalPrice && selectedVariant.originalPrice > selectedVariant.price && (
                <span className="original-price">{formatPrice(selectedVariant.originalPrice)}</span>
              )}
            </div>
          )}

          {/* Inventory Status */}
          {selectedVariant && (
            <div className="inventory-section">
              <InventoryStatus
                variantId={selectedVariant.variantId}
                quantity={quantity}
                onStockChange={handleStockChange}
              />
            </div>
          )}

          {/* Quantity and Add to Cart */}
          {selectedVariant && stockInfo && stockInfo.available && (
            <div className="purchase-section">
              <div className="quantity-section">
                <label htmlFor="quantity">Quantity:</label>
                <div className="quantity-controls">
                  <button
                    type="button"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                  >
                    −
                  </button>
                  <input
                    id="quantity"
                    type="number"
                    min="1"
                    max={stockInfo.availableStock}
                    value={quantity}
                    onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                  />
                  <button
                    type="button"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= stockInfo.availableStock}
                  >
                    +
                  </button>
                </div>
              </div>

              <button
                className="add-to-cart-btn"
                onClick={handleAddToCart}
                disabled={addingToCart || !stockInfo.available}
              >
                {addingToCart ? 'Adding...' : isInCartAlready ? `Update Cart (${currentCartQuantity})` : 'Add to Cart'}
              </button>
            </div>
          )}

          {/* Out of Stock Message */}
          {selectedVariant && stockInfo && !stockInfo.available && (
            <div className="out-of-stock-message">
              <h3>Currently Unavailable</h3>
              <p>This item is currently out of stock. Please check back later or contact us for availability.</p>
            </div>
          )}

          {/* Product Summary */}
          {inventory && inventory.summary && (
            <div className="product-summary">
              <h3>Product Availability Summary</h3>
              <div className="summary-stats">
                <div className="stat">
                  <span className="stat-label">Total Variants:</span>
                  <span className="stat-value">{inventory.summary.totalVariants}</span>
                </div>
                <div className="stat">
                  <span className="stat-label">In Stock:</span>
                  <span className="stat-value">{inventory.summary.inStockVariants}</span>
                </div>
                <div className="stat">
                  <span className="stat-label">Total Available:</span>
                  <span className="stat-value">{inventory.summary.totalAvailable}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductPageWithInventory;
