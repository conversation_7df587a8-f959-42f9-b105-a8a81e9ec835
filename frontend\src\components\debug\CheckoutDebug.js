import React, { useState, useEffect } from 'react';
import { useCart } from '../../contexts/CartContext';
import paymentService from '../../services/paymentService';
import apiConfig from '../../services/apiConfig';

const CheckoutDebug = () => {
  const { cartItems, cartTotal } = useCart();
  const [debugInfo, setDebugInfo] = useState({});
  const [testResult, setTestResult] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Gather debug information
    const info = {
      cartItems: cartItems?.length || 0,
      cartTotal: cartTotal || 0,
      apiUrl: apiConfig.getApiUrl(),
      paymentEnabled: apiConfig.isFeatureEnabled('paymentProcessing'),
      paymongoKey: apiConfig.getPaymentConfig().paymongoPublicKey ? 'Configured' : 'Missing',
      environment: process.env.REACT_APP_ENVIRONMENT,
      debugMode: process.env.REACT_APP_DEBUG_MODE
    };
    setDebugInfo(info);
  }, [cartItems, cartTotal]);

  const testPaymentService = async () => {
    setLoading(true);
    setTestResult(null);

    try {
      // Test backend health
      const healthResponse = await fetch(`${apiConfig.getApiUrl()}/api/health`);
      const healthData = await healthResponse.json();

      // Test payment link creation
      const testOrderData = {
        orderId: 'debug-test-' + Date.now(),
        totalAmount: 10000,
        currency: 'PHP',
        description: 'Debug Test Order',
        customerInfo: {
          firstName: 'Debug',
          lastName: 'Test',
          email: '<EMAIL>',
          phone: '+639123456789'
        },
        items: [
          {
            name: 'Test Item',
            quantity: 1,
            amount: 10000
          }
        ],
        metadata: {
          orderId: 'debug-test-' + Date.now(),
          source: 'debug-test'
        }
      };

      const paymentResult = await paymentService.createPaymentLink(testOrderData);

      setTestResult({
        success: true,
        health: healthData,
        payment: paymentResult
      });

    } catch (error) {
      setTestResult({
        success: false,
        error: error.message,
        stack: error.stack
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '400px',
      background: 'white',
      border: '2px solid #F0B21B',
      borderRadius: '8px',
      padding: '16px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      zIndex: 9999,
      fontSize: '12px',
      maxHeight: '80vh',
      overflowY: 'auto'
    }}>
      <h3 style={{ margin: '0 0 12px 0', color: '#F0B21B' }}>🔧 Checkout Debug</h3>
      
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0' }}>Configuration:</h4>
        <div>Cart Items: {debugInfo.cartItems}</div>
        <div>Cart Total: ₱{debugInfo.cartTotal?.toLocaleString()}</div>
        <div>API URL: {debugInfo.apiUrl}</div>
        <div>Payment Enabled: {debugInfo.paymentEnabled ? '✅' : '❌'}</div>
        <div>PayMongo Key: {debugInfo.paymongoKey}</div>
        <div>Environment: {debugInfo.environment}</div>
        <div>Debug Mode: {debugInfo.debugMode}</div>
      </div>

      <button
        onClick={testPaymentService}
        disabled={loading}
        style={{
          background: '#F0B21B',
          color: 'white',
          border: 'none',
          padding: '8px 16px',
          borderRadius: '4px',
          cursor: loading ? 'not-allowed' : 'pointer',
          width: '100%',
          marginBottom: '12px'
        }}
      >
        {loading ? 'Testing...' : 'Test Payment Service'}
      </button>

      {testResult && (
        <div style={{
          background: testResult.success ? '#d4edda' : '#f8d7da',
          border: `1px solid ${testResult.success ? '#c3e6cb' : '#f5c6cb'}`,
          borderRadius: '4px',
          padding: '8px',
          marginTop: '8px'
        }}>
          <h4 style={{ margin: '0 0 8px 0' }}>
            {testResult.success ? '✅ Test Results' : '❌ Test Failed'}
          </h4>
          
          {testResult.success ? (
            <div>
              <div><strong>Backend Health:</strong> {testResult.health?.success ? '✅' : '❌'}</div>
              <div><strong>PayMongo Config:</strong> {testResult.health?.paymongo?.configured ? '✅' : '❌'}</div>
              <div><strong>Webhook Config:</strong> {testResult.health?.paymongo?.webhook_configured ? '✅' : '❌'}</div>
              <div><strong>Payment Link:</strong> {testResult.payment?.success ? '✅' : '❌'}</div>
              {testResult.payment?.data?.paymentLink && (
                <div><strong>Link ID:</strong> {testResult.payment.data.paymentLink.id}</div>
              )}
            </div>
          ) : (
            <div>
              <div><strong>Error:</strong> {testResult.error}</div>
              {testResult.stack && (
                <details style={{ marginTop: '8px' }}>
                  <summary>Stack Trace</summary>
                  <pre style={{ fontSize: '10px', overflow: 'auto' }}>
                    {testResult.stack}
                  </pre>
                </details>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CheckoutDebug;
