import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import paymentService from '../../services/paymentService';
import PaymentForm from './PaymentForm';
import BankPaymentForm from './BankPaymentForm';
import OrderSummary from './OrderSummary';
import PaymentMethods from './PaymentMethods';
import SecurityBadges from './SecurityBadges';
import LoadingSpinner from '../common/LoadingSpinner';
import './ModernPaymentPage.css';

const ModernPaymentPage = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const { items, getTotal, getSubtotal, getTax, getShipping, clearCart } = useCart();
    
    // State management
    const [currentStep, setCurrentStep] = useState(1);
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('card');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState(false);
    const [paymentFees, setPaymentFees] = useState(null);
    const [orderData, setOrderData] = useState(null);
    
    // Form data
    const [billingInfo, setBillingInfo] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'Philippines'
    });
    
    const [cardInfo, setCardInfo] = useState({
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        cardName: ''
    });

    // Bank payment specific state
    const [selectedBank, setSelectedBank] = useState('');
    const [bankCustomerInfo, setBankCustomerInfo] = useState({
        accountName: '',
        accountNumber: '',
        email: '',
        phone: ''
    });

    // Initialize order data from location state or cart
    useEffect(() => {
        const orderFromState = location.state?.orderData;
        if (orderFromState) {
            setOrderData(orderFromState);
        } else if (items.length > 0) {
            // Create order data from cart
            const newOrderData = {
                items: items,
                subtotal: getSubtotal(),
                tax: getTax(getSubtotal()),
                shipping: getShipping(getSubtotal()),
                total: getTotal(),
                orderId: `ORDER_${Date.now()}`,
                currency: 'PHP'
            };
            setOrderData(newOrderData);
        } else {
            // No items in cart, redirect to cart page
            navigate('/cart');
        }
    }, [items, location.state, navigate, getSubtotal, getTax, getShipping, getTotal]);

    // Calculate payment fees when payment method or bank changes
    useEffect(() => {
        if (orderData && selectedPaymentMethod) {
            calculatePaymentFees();
        }
    }, [selectedPaymentMethod, selectedBank, orderData]);

    const calculatePaymentFees = async () => {
        try {
            const totalInCentavos = Math.round(orderData.total * 100);
            const bankCode = selectedPaymentMethod === 'bank' ? selectedBank : null;
            const feeResult = await paymentService.calculateFees(totalInCentavos, selectedPaymentMethod, bankCode);

            if (feeResult.success) {
                setPaymentFees(feeResult.data);
            }
        } catch (error) {
            console.error('Error calculating fees:', error);
        }
    };

    const handlePaymentMethodChange = (method) => {
        setSelectedPaymentMethod(method);
        setError('');

        // Reset bank selection when switching away from bank payment
        if (method !== 'bank') {
            setSelectedBank('');
            setBankCustomerInfo({
                accountName: '',
                accountNumber: '',
                email: '',
                phone: ''
            });
        }
    };

    const handleBillingInfoChange = (field, value) => {
        setBillingInfo(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleCardInfoChange = (field, value) => {
        setCardInfo(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleBankPaymentSubmit = async (bankPaymentData) => {
        setLoading(true);
        setError('');

        try {
            // Create payment link for bank transfer
            const paymentLinkResult = await paymentService.createPaymentLink({
                ...bankPaymentData,
                totalAmount: Math.round(bankPaymentData.fees.totalAmount * 100), // Convert to centavos
                orderId: orderData.orderId,
                paymentMethod: 'bank',
                bankCode: bankPaymentData.bankCode
            });

            if (paymentLinkResult.success) {
                // Store order and payment info
                const orderResult = await paymentService.createOrder({
                    ...orderData,
                    paymentMethod: 'bank',
                    bankCode: bankPaymentData.bankCode,
                    bankName: bankPaymentData.bankName,
                    customerInfo: bankPaymentData.customerInfo,
                    paymentLinkId: paymentLinkResult.paymentLink.id,
                    totalAmount: bankPaymentData.fees.totalAmount,
                    fees: bankPaymentData.fees
                });

                if (orderResult.success) {
                    // Clear cart
                    clearCart();

                    // In a real implementation, redirect to bank's online banking portal
                    // For demo purposes, simulate bank redirect and callback
                    const bankRedirectUrl = paymentLinkResult.paymentLink.url;

                    // Simulate bank payment processing
                    setTimeout(() => {
                        // Simulate successful payment callback
                        const callbackUrl = `/payment/bank-callback?payment_id=${paymentLinkResult.paymentLink.id}&order_id=${orderResult.data.orderId}&status=paid&bank_code=${bankPaymentData.bankCode}`;
                        navigate(callbackUrl);
                    }, 2000);
                } else {
                    throw new Error(orderResult.message || 'Failed to create order');
                }
            } else {
                throw new Error(paymentLinkResult.message || 'Failed to create payment link');
            }
        } catch (error) {
            console.error('Bank payment error:', error);
            setError(error.message || 'Bank payment failed. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const validateForm = () => {
        // For bank payments, validation is handled in BankPaymentForm
        if (selectedPaymentMethod === 'bank') {
            return true;
        }

        // Validate billing information for other payment methods
        const requiredBillingFields = ['firstName', 'lastName', 'email', 'address', 'city', 'zipCode'];
        for (const field of requiredBillingFields) {
            if (!billingInfo[field].trim()) {
                setError(`Please fill in your ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
                return false;
            }
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(billingInfo.email)) {
            setError('Please enter a valid email address');
            return false;
        }

        // Validate card information for card payments
        if (selectedPaymentMethod === 'card') {
            if (!cardInfo.cardNumber || !cardInfo.expiryDate || !cardInfo.cvv || !cardInfo.cardName) {
                setError('Please fill in all card details');
                return false;
            }

            // Validate card number using Luhn algorithm
            if (!paymentService.validateCardNumber(cardInfo.cardNumber)) {
                setError('Please enter a valid card number');
                return false;
            }
        }

        return true;
    };

    const handleSubmitPayment = async () => {
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        setError('');

        try {
            // Prepare payment data
            const paymentData = {
                orderId: orderData.orderId,
                totalAmount: paymentFees ? Math.round(paymentFees.totalAmount * 100) : Math.round(orderData.total * 100),
                paymentMethod: selectedPaymentMethod,
                billingInfo: billingInfo,
                cardInfo: selectedPaymentMethod === 'card' ? cardInfo : null,
                items: orderData.items,
                metadata: {
                    subtotal: orderData.subtotal,
                    tax: orderData.tax,
                    shipping: orderData.shipping,
                    fees: paymentFees?.totalFee || 0
                }
            };

            // Create payment link with PayMongo
            const result = await paymentService.createPaymentLink(paymentData);

            if (result.success) {
                // Clear cart on successful payment initiation
                clearCart();
                
                // Set success state
                setSuccess(true);
                
                // Redirect to success page after a short delay
                setTimeout(() => {
                    navigate('/order-success', {
                        state: {
                            orderData: paymentData,
                            paymentLink: result.paymentLink
                        }
                    });
                }, 2000);
            } else {
                throw new Error(result.error || 'Payment processing failed');
            }
        } catch (error) {
            console.error('Payment error:', error);
            setError(error.message || 'An error occurred while processing your payment. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const nextStep = () => {
        if (currentStep === 1 && !selectedPaymentMethod) {
            setError('Please select a payment method');
            return;
        }

        // For bank payments, skip to step 3 (bank payment form handles its own flow)
        if (selectedPaymentMethod === 'bank' && currentStep === 1) {
            setCurrentStep(3);
            setError('');
            return;
        }

        if (currentStep < 3) {
            setCurrentStep(currentStep + 1);
            setError('');
        }
    };

    const prevStep = () => {
        if (currentStep > 1) {
            // For bank payments, go back to step 1 from step 3
            if (selectedPaymentMethod === 'bank' && currentStep === 3) {
                setCurrentStep(1);
            } else {
                setCurrentStep(currentStep - 1);
            }
            setError('');
        }
    };

    if (!orderData) {
        return <LoadingSpinner />;
    }

    if (success) {
        return (
            <div className="modern-payment-page">
                <div className="payment-container">
                    <div className="payment-success-message">
                        <div className="success-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12" cy="12" r="10" fill="#10B981"/>
                                <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                        </div>
                        <h2>Payment Initiated Successfully!</h2>
                        <p>Redirecting you to complete your payment...</p>
                        <LoadingSpinner />
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="modern-payment-page">
            <div className="payment-container">
                {/* Header */}
                <div className="payment-header">
                    <h1>Secure Payment</h1>
                    <div className="payment-steps">
                        <div className={`step ${currentStep >= 1 ? 'active' : ''}`}>
                            <span className="step-number">1</span>
                            <span className="step-label">Payment Method</span>
                        </div>
                        <div className={`step ${currentStep >= 2 ? 'active' : ''}`}>
                            <span className="step-number">2</span>
                            <span className="step-label">Billing Info</span>
                        </div>
                        <div className={`step ${currentStep >= 3 ? 'active' : ''}`}>
                            <span className="step-number">3</span>
                            <span className="step-label">Review & Pay</span>
                        </div>
                    </div>
                </div>

                {/* Error Message */}
                {error && (
                    <div className="error-message">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12" cy="12" r="10" fill="#EF4444"/>
                            <path d="M15 9L9 15M9 9L15 15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        <span>{error}</span>
                    </div>
                )}

                <div className="payment-content">
                    {/* Left Column - Forms */}
                    <div className="payment-forms">
                        {currentStep === 1 && (
                            <PaymentMethods
                                selectedMethod={selectedPaymentMethod}
                                onMethodChange={handlePaymentMethodChange}
                                fees={paymentFees}
                            />
                        )}

                        {currentStep === 2 && (
                            <PaymentForm
                                paymentMethod={selectedPaymentMethod}
                                billingInfo={billingInfo}
                                cardInfo={cardInfo}
                                onBillingInfoChange={handleBillingInfoChange}
                                onCardInfoChange={handleCardInfoChange}
                            />
                        )}

                        {currentStep === 3 && selectedPaymentMethod === 'bank' && (
                            <BankPaymentForm
                                orderData={orderData}
                                onPaymentSubmit={handleBankPaymentSubmit}
                                onBack={prevStep}
                                isLoading={loading}
                                fees={paymentFees}
                            />
                        )}

                        {currentStep === 3 && selectedPaymentMethod !== 'bank' && (
                            <div className="payment-review">
                                <h3>Review Your Order</h3>
                                <div className="review-section">
                                    <h4>Payment Method</h4>
                                    <p>{paymentService.getPaymentMethodDisplayName(selectedPaymentMethod)}</p>
                                </div>
                                <div className="review-section">
                                    <h4>Billing Information</h4>
                                    <p>{billingInfo.firstName} {billingInfo.lastName}</p>
                                    <p>{billingInfo.email}</p>
                                    <p>{billingInfo.address}, {billingInfo.city}</p>
                                </div>
                            </div>
                        )}

                        {/* Navigation Buttons - Only show for non-bank payments */}
                        {selectedPaymentMethod !== 'bank' && (
                            <div className="payment-navigation">
                                {currentStep > 1 && (
                                    <button
                                        type="button"
                                        className="btn btn-secondary"
                                        onClick={prevStep}
                                        disabled={loading}
                                    >
                                        Back
                                    </button>
                                )}

                                {currentStep < 3 ? (
                                    <button
                                        type="button"
                                        className="btn btn-primary"
                                        onClick={nextStep}
                                        disabled={loading}
                                    >
                                        Continue
                                    </button>
                                ) : (
                                <button
                                    type="button"
                                    className="btn btn-primary btn-pay"
                                    onClick={handleSubmitPayment}
                                    disabled={loading}
                                >
                                    {loading ? (
                                        <>
                                            <LoadingSpinner size="small" />
                                            Processing...
                                        </>
                                    ) : (
                                        <>
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="currentColor"/>
                                                <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                            Pay {paymentFees ? paymentService.formatAmount(paymentFees.totalAmount) : paymentService.formatAmount(orderData.total)}
                                        </>
                                    )}
                                </button>
                            )}
                            </div>
                        )}
                    </div>

                    {/* Right Column - Order Summary */}
                    <div className="payment-sidebar">
                        <OrderSummary
                            orderData={orderData}
                            fees={paymentFees}
                            paymentMethod={selectedPaymentMethod}
                        />
                        <SecurityBadges />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ModernPaymentPage;
