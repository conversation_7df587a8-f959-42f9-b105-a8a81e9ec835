import React, { useState, useEffect } from 'react';
import { useCart } from '../../hooks/useCart';
import './InventoryValidation.css';

const InventoryValidation = ({ onValidationComplete, autoValidate = true }) => {
  const { cart, validateCart, loading } = useCart();
  const [validationResult, setValidationResult] = useState(null);
  const [validating, setValidating] = useState(false);

  useEffect(() => {
    if (autoValidate && cart.items.length > 0) {
      performValidation();
    }
  }, [cart.items, autoValidate]);

  const performValidation = async () => {
    setValidating(true);
    try {
      const result = await validateCart();
      setValidationResult(result);
      
      if (onValidationComplete) {
        onValidationComplete(result);
      }
    } catch (error) {
      console.error('Error validating cart:', error);
      setValidationResult({
        valid: false,
        issues: ['Error validating cart inventory']
      });
    } finally {
      setValidating(false);
    }
  };

  const handleRetryValidation = () => {
    performValidation();
  };

  if (cart.items.length === 0) {
    return (
      <div className="inventory-validation empty-cart">
        <div className="validation-message">
          <span className="info-icon">ℹ</span>
          <span>Your cart is empty</span>
        </div>
      </div>
    );
  }

  if (validating || loading) {
    return (
      <div className="inventory-validation validating">
        <div className="validation-spinner">
          <div className="spinner"></div>
        </div>
        <div className="validation-message">
          <span>Validating inventory availability...</span>
        </div>
      </div>
    );
  }

  if (!validationResult) {
    return (
      <div className="inventory-validation no-result">
        <div className="validation-message">
          <span className="warning-icon">⚠</span>
          <span>Inventory validation required</span>
        </div>
        <button 
          className="validate-btn"
          onClick={performValidation}
          disabled={validating}
        >
          Validate Cart
        </button>
      </div>
    );
  }

  if (validationResult.valid) {
    return (
      <div className="inventory-validation valid">
        <div className="validation-message success">
          <span className="success-icon">✓</span>
          <span>All items are available</span>
        </div>
        
        {validationResult.summary && (
          <div className="validation-summary">
            <div className="summary-item">
              <span className="summary-label">Items:</span>
              <span className="summary-value">{validationResult.summary.totalItems}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Available:</span>
              <span className="summary-value">{validationResult.summary.availableItems}</span>
            </div>
          </div>
        )}
        
        <button 
          className="revalidate-btn"
          onClick={handleRetryValidation}
          disabled={validating}
        >
          Recheck Availability
        </button>
      </div>
    );
  }

  // Validation failed - show issues
  return (
    <div className="inventory-validation invalid">
      <div className="validation-message error">
        <span className="error-icon">✗</span>
        <span>Some items are not available</span>
      </div>

      {validationResult.issues && validationResult.issues.length > 0 && (
        <div className="validation-issues">
          <h4>Issues Found:</h4>
          <ul className="issues-list">
            {validationResult.issues.map((issue, index) => (
              <li key={index} className="issue-item">
                <div className="issue-header">
                  <span className="issue-icon">⚠</span>
                  <span className="issue-product">
                    {issue.productName} {issue.variantName && `(${issue.variantName})`}
                  </span>
                </div>
                <div className="issue-details">
                  <span className="issue-message">{issue.message}</span>
                  <div className="issue-quantities">
                    <span className="requested">Requested: {issue.requestedQuantity}</span>
                    <span className="available">Available: {issue.availableStock}</span>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {validationResult.summary && (
        <div className="validation-summary error">
          <div className="summary-item">
            <span className="summary-label">Total Items:</span>
            <span className="summary-value">{validationResult.summary.totalItems}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Available:</span>
            <span className="summary-value success">{validationResult.summary.availableItems}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Unavailable:</span>
            <span className="summary-value error">{validationResult.summary.unavailableItems}</span>
          </div>
        </div>
      )}

      <div className="validation-actions">
        <button 
          className="revalidate-btn"
          onClick={handleRetryValidation}
          disabled={validating}
        >
          Check Again
        </button>
        
        <div className="action-note">
          Please adjust quantities or remove unavailable items from your cart to continue.
        </div>
      </div>
    </div>
  );
};

export default InventoryValidation;
