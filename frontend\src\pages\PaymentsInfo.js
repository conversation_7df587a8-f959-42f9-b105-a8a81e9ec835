import React, { useState } from 'react';
import paymentService from '../services/paymentService';
import './PaymentsInfo.css';

const PaymentsInfo = () => {
    const [activeTab, setActiveTab] = useState('overview');

    const supportedBanks = paymentService.getSupportedBanks();

    const paymentMethods = [
        {
            id: 'card',
            name: 'Credit/Debit Cards',
            description: 'Secure card payments with instant processing',
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="2" y="4" width="20" height="16" rx="3" fill="#F0B21B"/>
                    <rect x="2" y="8" width="20" height="2" fill="white"/>
                    <rect x="4" y="12" width="4" height="1" fill="white"/>
                    <rect x="4" y="14" width="6" height="1" fill="white"/>
                </svg>
            ),
            features: ['Instant processing', '3D Secure protection', 'Save for future use', 'Visa, Mastercard, AMEX'],
            processingTime: 'Instant',
            fees: '3.5% + ₱15'
        },
        {
            id: 'bank',
            name: 'Online Banking',
            description: 'Direct bank transfer from your bank account',
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 21H21M5 21V7L12 3L19 7V21M9 9H10M14 9H15M9 13H10M14 13H15M9 17H10M14 17H15" stroke="#F0B21B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            features: ['Bank-level security', 'No card required', 'Direct transfer', 'Lower fees'],
            processingTime: '1-3 business days',
            fees: '1.0% - 1.5% + ₱5-₱15'
        },
        {
            id: 'gcash',
            name: 'GCash',
            description: 'Pay using your GCash mobile wallet',
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="5" y="4" width="14" height="16" rx="2" fill="#007DFF"/>
                    <circle cx="12" cy="12" r="3" fill="white"/>
                    <path d="M12 9V15M9 12H15" stroke="#007DFF" strokeWidth="1" strokeLinecap="round"/>
                </svg>
            ),
            features: ['Mobile wallet', 'QR code payment', 'Instant processing', 'Wide acceptance'],
            processingTime: 'Instant',
            fees: '2.5% + ₱10'
        },
        {
            id: 'paymaya',
            name: 'PayMaya',
            description: 'Digital wallet payments made easy',
            icon: (
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="5" y="4" width="14" height="16" rx="2" fill="#00D632"/>
                    <circle cx="12" cy="12" r="3" fill="white"/>
                    <path d="M10 10L12 12L14 10" stroke="#00D632" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            features: ['Digital wallet', 'Quick payments', 'Mobile app integration', 'Secure transactions'],
            processingTime: 'Instant',
            fees: '2.5% + ₱10'
        }
    ];

    const securityFeatures = [
        {
            title: 'PCI DSS Compliance',
            description: 'PayMongo is PCI DSS Level 1 certified, ensuring the highest level of security for card data.',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" fill="#10B981"/>
                    <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            )
        },
        {
            title: '3D Secure Authentication',
            description: 'Additional layer of security for card transactions with bank verification.',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="11" width="18" height="10" rx="2" ry="2" fill="#3B82F6"/>
                    <circle cx="12" cy="7" r="4" stroke="#3B82F6" strokeWidth="2" fill="none"/>
                    <path d="M12 13V17M10 15H14" stroke="white" strokeWidth="2" strokeLinecap="round"/>
                </svg>
            )
        },
        {
            title: 'SSL Encryption',
            description: 'All data transmitted is encrypted using industry-standard SSL/TLS protocols.',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#F59E0B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            )
        },
        {
            title: 'Fraud Detection',
            description: 'Advanced machine learning algorithms monitor transactions for suspicious activity.',
            icon: (
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#EF4444"/>
                    <path d="M2 17L12 22L22 17M2 12L12 17L22 12" stroke="#EF4444" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            )
        }
    ];

    const tabs = [
        { id: 'overview', label: 'Payment Methods', icon: '💳' },
        { id: 'banking', label: 'Bank Transfers', icon: '🏦' },
        { id: 'paymongo', label: 'PayMongo Gateway', icon: '🔒' },
        { id: 'security', label: 'Security & Trust', icon: '🛡️' }
    ];

    return (
        <div className="payments-info-page">
            <div className="container">
                {/* Header Section */}
                <div className="payments-header">
                    <h1>Payment Information</h1>
                    <p className="payments-subtitle">
                        Secure, fast, and reliable payment options for your Design Excellence purchases
                    </p>
                </div>

                {/* Navigation Tabs */}
                <div className="payments-tabs">
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            <span className="tab-icon">{tab.icon}</span>
                            <span className="tab-label">{tab.label}</span>
                        </button>
                    ))}
                </div>

                {/* Tab Content */}
                <div className="tab-content">
                    {/* Payment Methods Overview */}
                    {activeTab === 'overview' && (
                        <div className="tab-panel">
                            <h2>Available Payment Methods</h2>
                            <p className="section-description">
                                Choose from our wide range of secure payment options designed for your convenience.
                            </p>
                            
                            <div className="payment-methods-grid">
                                {paymentMethods.map(method => (
                                    <div key={method.id} className="payment-method-card">
                                        <div className="method-header">
                                            <div className="method-icon">{method.icon}</div>
                                            <div className="method-info">
                                                <h3>{method.name}</h3>
                                                <p>{method.description}</p>
                                            </div>
                                        </div>
                                        
                                        <div className="method-details">
                                            <div className="detail-row">
                                                <span className="detail-label">Processing Time:</span>
                                                <span className="detail-value">{method.processingTime}</span>
                                            </div>
                                            <div className="detail-row">
                                                <span className="detail-label">Fees:</span>
                                                <span className="detail-value">{method.fees}</span>
                                            </div>
                                        </div>
                                        
                                        <div className="method-features">
                                            <h4>Features:</h4>
                                            <ul>
                                                {method.features.map((feature, index) => (
                                                    <li key={index}>{feature}</li>
                                                ))}
                                            </ul>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Bank Transfer Information */}
                    {activeTab === 'banking' && (
                        <div className="tab-panel">
                            <h2>Bank Transfer Information</h2>
                            <p className="section-description">
                                Pay directly from your bank account using our secure online banking integration.
                            </p>
                            
                            <div className="banking-info">
                                <div className="supported-banks">
                                    <h3>Supported Philippine Banks</h3>
                                    <div className="banks-grid">
                                        {supportedBanks.map(bank => (
                                            <div key={bank.id} className="bank-card">
                                                <div className="bank-header">
                                                    <div 
                                                        className="bank-logo"
                                                        style={{ backgroundColor: bank.color }}
                                                    >
                                                        {bank.shortName}
                                                    </div>
                                                    <div className="bank-info">
                                                        <h4>{bank.shortName}</h4>
                                                        <p>{bank.name}</p>
                                                    </div>
                                                </div>
                                                
                                                <div className="bank-details">
                                                    <div className="detail-row">
                                                        <span className="detail-label">Processing:</span>
                                                        <span className="detail-value">{bank.processingTime}</span>
                                                    </div>
                                                    <div className="detail-row">
                                                        <span className="detail-label">Fee:</span>
                                                        <span className="detail-value">
                                                            {paymentService.getBankTransferFees(bank.id).percentage * 100}% + ₱{paymentService.getBankTransferFees(bank.id).fixed}
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                <div className="bank-features">
                                                    <ul>
                                                        {bank.features.map((feature, index) => (
                                                            <li key={index}>{feature}</li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* PayMongo Gateway Information */}
                    {activeTab === 'paymongo' && (
                        <div className="tab-panel">
                            <h2>PayMongo Payment Gateway</h2>
                            <p className="section-description">
                                Powered by PayMongo, the Philippines' leading payment gateway trusted by thousands of businesses.
                            </p>

                            <div className="paymongo-info">
                                <div className="gateway-overview">
                                    <div className="overview-card">
                                        <h3>About PayMongo</h3>
                                        <p>
                                            PayMongo is a Philippine-based payment gateway that enables businesses to accept
                                            online payments securely and efficiently. Licensed by the Bangko Sentral ng Pilipinas (BSP),
                                            PayMongo provides world-class payment infrastructure for Filipino businesses.
                                        </p>
                                    </div>

                                    <div className="overview-card">
                                        <h3>Why PayMongo?</h3>
                                        <ul>
                                            <li>BSP-licensed and regulated payment service provider</li>
                                            <li>PCI DSS Level 1 certified for maximum security</li>
                                            <li>Local support and Filipino-first approach</li>
                                            <li>Competitive rates and transparent pricing</li>
                                            <li>Real-time transaction monitoring and reporting</li>
                                        </ul>
                                    </div>
                                </div>

                                <div className="fee-structure">
                                    <h3>Fee Structure</h3>
                                    <div className="fees-grid">
                                        <div className="fee-card">
                                            <h4>Credit/Debit Cards</h4>
                                            <div className="fee-amount">3.5% + ₱15</div>
                                            <p>Per successful transaction</p>
                                        </div>
                                        <div className="fee-card">
                                            <h4>Online Banking</h4>
                                            <div className="fee-amount">1.0% - 1.5%</div>
                                            <p>+ ₱5-₱15 per transaction</p>
                                        </div>
                                        <div className="fee-card">
                                            <h4>E-Wallets</h4>
                                            <div className="fee-amount">2.5% + ₱10</div>
                                            <p>GCash, PayMaya, GrabPay</p>
                                        </div>
                                    </div>
                                </div>

                                <div className="payment-flow">
                                    <h3>How Payment Processing Works</h3>
                                    <div className="flow-steps">
                                        <div className="flow-step">
                                            <div className="step-number">1</div>
                                            <div className="step-content">
                                                <h4>Select Payment Method</h4>
                                                <p>Choose your preferred payment option during checkout</p>
                                            </div>
                                        </div>
                                        <div className="flow-step">
                                            <div className="step-number">2</div>
                                            <div className="step-content">
                                                <h4>Secure Processing</h4>
                                                <p>Your payment is processed through PayMongo's secure gateway</p>
                                            </div>
                                        </div>
                                        <div className="flow-step">
                                            <div className="step-number">3</div>
                                            <div className="step-content">
                                                <h4>Instant Confirmation</h4>
                                                <p>Receive immediate confirmation and order processing begins</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Security Information */}
                    {activeTab === 'security' && (
                        <div className="tab-panel">
                            <h2>Security & Trust</h2>
                            <p className="section-description">
                                Your security is our top priority. We use industry-leading security measures to protect your payment information.
                            </p>

                            <div className="security-features">
                                <div className="features-grid">
                                    {securityFeatures.map((feature, index) => (
                                        <div key={index} className="security-feature">
                                            <div className="feature-icon">{feature.icon}</div>
                                            <div className="feature-content">
                                                <h3>{feature.title}</h3>
                                                <p>{feature.description}</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>

                                <div className="trust-indicators">
                                    <h3>Trust & Compliance</h3>
                                    <div className="indicators-grid">
                                        <div className="indicator">
                                            <div className="indicator-badge">BSP</div>
                                            <p>Licensed by Bangko Sentral ng Pilipinas</p>
                                        </div>
                                        <div className="indicator">
                                            <div className="indicator-badge">PCI</div>
                                            <p>PCI DSS Level 1 Certified</p>
                                        </div>
                                        <div className="indicator">
                                            <div className="indicator-badge">SSL</div>
                                            <p>256-bit SSL Encryption</p>
                                        </div>
                                        <div className="indicator">
                                            <div className="indicator-badge">ISO</div>
                                            <p>ISO 27001 Compliant</p>
                                        </div>
                                    </div>
                                </div>

                                <div className="security-tips">
                                    <h3>Payment Security Tips</h3>
                                    <div className="tips-list">
                                        <div className="tip">
                                            <div className="tip-icon">🔒</div>
                                            <div className="tip-content">
                                                <h4>Secure Connection</h4>
                                                <p>Always ensure you see the padlock icon in your browser's address bar</p>
                                            </div>
                                        </div>
                                        <div className="tip">
                                            <div className="tip-icon">👀</div>
                                            <div className="tip-content">
                                                <h4>Verify URL</h4>
                                                <p>Check that you're on the official Design Excellence website</p>
                                            </div>
                                        </div>
                                        <div className="tip">
                                            <div className="tip-icon">📱</div>
                                            <div className="tip-content">
                                                <h4>Use Trusted Networks</h4>
                                                <p>Avoid making payments on public or unsecured Wi-Fi networks</p>
                                            </div>
                                        </div>
                                        <div className="tip">
                                            <div className="tip-icon">💳</div>
                                            <div className="tip-content">
                                                <h4>Monitor Statements</h4>
                                                <p>Regularly check your bank and card statements for any unauthorized transactions</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default PaymentsInfo;
