{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;GAIG;AACH,OAAO,EACL,qBAAqB,GAItB,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EACL,8BAA8B,EAC9B,gCAAgC,EAIhC,oBAAoB,GACrB,MAAM,0CAA0C,CAAC;AAGlD,OAAO,EAAE,0BAA0B,EAAE,MAAM,sCAAsC,CAAC;AAClF,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAC3D,OAAO,EAOL,iBAAiB,GAClB,MAAM,WAAW,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * A Shim Library that provides compatibility between Core V1 & V2 Packages.\n *\n * @packageDocumentation\n */\nexport {\n  ExtendedServiceClient,\n  ExtendedServiceClientOptions,\n  ExtendedCommonClientOptions,\n  ExtendedClientOptions,\n} from \"./extendedClient.js\";\nexport { CompatResponse } from \"./response.js\";\nexport {\n  requestPolicyFactoryPolicyName,\n  createRequestPolicyFactoryPolicy,\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptionsLike,\n  HttpPipelineLogLevel,\n} from \"./policies/requestPolicyFactoryPolicy.js\";\nexport { KeepAliveOptions } from \"./policies/keepAliveOptions.js\";\nexport { RedirectOptions } from \"./policies/redirectOptions.js\";\nexport { disableKeepAlivePolicyName } from \"./policies/disableKeepAlivePolicy.js\";\nexport { convertHttpClient } from \"./httpClientAdapter.js\";\nexport {\n  Agent,\n  WebResourceLike,\n  HttpHeadersLike,\n  RawHttpHeaders,\n  HttpHeader,\n  TransferProgressEvent,\n  toHttpHeadersLike,\n} from \"./util.js\";\n"]}