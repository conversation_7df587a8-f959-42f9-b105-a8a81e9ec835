import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import paymentService from '../../services/paymentService';
import LoadingSpinner from '../common/LoadingSpinner';
import './BankPaymentCallback.css';

const BankPaymentCallback = () => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const [status, setStatus] = useState('processing');
    const [orderData, setOrderData] = useState(null);
    const [error, setError] = useState('');

    useEffect(() => {
        handlePaymentCallback();
    }, []);

    const handlePaymentCallback = async () => {
        try {
            // Get payment parameters from URL
            const paymentId = searchParams.get('payment_id');
            const orderId = searchParams.get('order_id');
            const paymentStatus = searchParams.get('status');
            const bankCode = searchParams.get('bank_code');

            if (!paymentId || !orderId) {
                throw new Error('Missing payment information');
            }

            // Simulate payment status check (in real implementation, call PayMongo API)
            const paymentResult = await paymentService.getBankPaymentStatuses(paymentId, bankCode);
            
            if (paymentResult.success) {
                const order = paymentResult.data;
                setOrderData(order);
                
                if (paymentStatus === 'paid' || paymentResult.data.status === 'completed') {
                    setStatus('success');
                    
                    // Redirect to success page after 3 seconds
                    setTimeout(() => {
                        navigate('/order-success', {
                            state: {
                                orderData: order,
                                paymentMethod: 'bank',
                                bankCode: bankCode
                            }
                        });
                    }, 3000);
                } else if (paymentStatus === 'failed' || paymentResult.data.status === 'failed') {
                    setStatus('failed');
                } else {
                    setStatus('pending');
                }
            } else {
                throw new Error(paymentResult.message || 'Payment verification failed');
            }
        } catch (error) {
            console.error('Payment callback error:', error);
            setError(error.message || 'Payment verification failed');
            setStatus('error');
        }
    };

    const handleRetryPayment = () => {
        navigate('/payment', {
            state: { orderData: orderData }
        });
    };

    const handleGoHome = () => {
        navigate('/');
    };

    return (
        <div className="bank-payment-callback">
            <div className="callback-container">
                <div className="callback-content">
                    {status === 'processing' && (
                        <div className="callback-status processing">
                            <LoadingSpinner size="large" />
                            <h2>Processing Payment</h2>
                            <p>Please wait while we verify your bank payment...</p>
                        </div>
                    )}

                    {status === 'success' && (
                        <div className="callback-status success">
                            <div className="success-icon">
                                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" fill="#10B981"/>
                                    <path d="M9 12L11 14L15 10" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            <h2>Payment Successful!</h2>
                            <p>Your bank payment has been processed successfully.</p>
                            <p className="redirect-notice">Redirecting to order confirmation...</p>
                        </div>
                    )}

                    {status === 'failed' && (
                        <div className="callback-status failed">
                            <div className="error-icon">
                                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" fill="#EF4444"/>
                                    <path d="M15 9L9 15M9 9L15 15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            <h2>Payment Failed</h2>
                            <p>Your bank payment could not be processed.</p>
                            <div className="callback-actions">
                                <button 
                                    className="btn btn-primary"
                                    onClick={handleRetryPayment}
                                >
                                    Try Again
                                </button>
                                <button 
                                    className="btn btn-secondary"
                                    onClick={handleGoHome}
                                >
                                    Go Home
                                </button>
                            </div>
                        </div>
                    )}

                    {status === 'pending' && (
                        <div className="callback-status pending">
                            <div className="pending-icon">
                                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" fill="#F59E0B"/>
                                    <path d="M12 6V12L16 14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            <h2>Payment Pending</h2>
                            <p>Your bank payment is being processed. This may take 1-3 business days.</p>
                            <p>You will receive an email confirmation once the payment is completed.</p>
                            <div className="callback-actions">
                                <button 
                                    className="btn btn-primary"
                                    onClick={handleGoHome}
                                >
                                    Continue Shopping
                                </button>
                            </div>
                        </div>
                    )}

                    {status === 'error' && (
                        <div className="callback-status error">
                            <div className="error-icon">
                                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="10" fill="#EF4444"/>
                                    <path d="M12 8V12M12 16H12.01" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            <h2>Verification Error</h2>
                            <p>{error}</p>
                            <div className="callback-actions">
                                <button 
                                    className="btn btn-primary"
                                    onClick={handleRetryPayment}
                                >
                                    Try Again
                                </button>
                                <button 
                                    className="btn btn-secondary"
                                    onClick={handleGoHome}
                                >
                                    Go Home
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default BankPaymentCallback;
