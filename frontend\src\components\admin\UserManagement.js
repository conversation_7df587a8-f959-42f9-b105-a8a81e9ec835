import React, { useState, useEffect } from 'react';
import apiClient from '../../services/apiClient';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);

  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/api/admin/users');
      if (response.data.success) {
        setUsers(response.data.users);
      } else {
        setError('Failed to fetch users');
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const response = await apiClient.get('/api/admin/roles');
      if (response.data.success) {
        setRoles(response.data.roles);
      }
    } catch (err) {
      console.error('Error fetching roles:', err);
    }
  };

  const handleToggleActive = async (userId, currentStatus) => {
    try {
      const user = users.find(u => u.id === userId);
      const response = await apiClient.put(`/api/admin/users/${userId}`, {
        ...user,
        isActive: !currentStatus
      });

      if (response.data.success) {
        await fetchUsers(); // Refresh the list
      } else {
        setError('Failed to update user status');
      }
    } catch (err) {
      console.error('Error updating user:', err);
      setError('Failed to update user status');
    }
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to deactivate this user?')) {
      try {
        const response = await apiClient.delete(`/api/admin/users/${userId}`);
        if (response.data.success) {
          await fetchUsers(); // Refresh the list
        } else {
          setError('Failed to delete user');
        }
      } catch (err) {
        console.error('Error deleting user:', err);
        setError('Failed to delete user');
      }
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-PH');
  };

  const getRoleColor = (role) => {
    switch (role.toLowerCase()) {
      case 'admin': return '#e74c3c';
      case 'inventorymanager': return '#3498db';
      case 'transactionmanager': return '#9b59b6';
      case 'usermanager': return '#f39c12';
      case 'ordersupport': return '#27ae60';
      default: return '#95a5a6';
    }
  };

  if (loading) {
    return (
      <div className="admin-loading">
        <div className="loading-spinner"></div>
        <p>Loading users...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="admin-error">
        <p>Error: {error}</p>
        <button onClick={fetchUsers} className="admin-btn admin-btn-primary">
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="user-management">
      <div className="admin-card-header">
        <h1 className="admin-card-title">User Management</h1>
        <div className="admin-card-actions">
          <button
            className="admin-btn admin-btn-primary"
            onClick={() => setShowAddModal(true)}
          >
            Add New User
          </button>
          <button
            className="admin-btn admin-btn-secondary"
            onClick={fetchUsers}
          >
            Refresh
          </button>
        </div>
      </div>

      <div className="admin-card">
        <div className="table-container">
          <table className="admin-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Role</th>
                <th>Status</th>
                <th>Last Login</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map(user => (
                <tr key={user.id}>
                  <td>{user.fullName}</td>
                  <td>{user.email}</td>
                  <td>
                    <span
                      className="status-badge"
                      style={{ backgroundColor: getRoleColor(user.role) }}
                    >
                      {user.role}
                    </span>
                  </td>
                  <td>
                    <span
                      className="status-badge"
                      style={{ backgroundColor: user.isActive ? '#27ae60' : '#e74c3c' }}
                    >
                      {user.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td>{user.lastLogin ? formatDate(user.lastLogin) : 'Never'}</td>
                  <td>
                    <div className="action-buttons">
                      <button
                        className="admin-btn admin-btn-secondary btn-small"
                        onClick={() => setEditingUser(user)}
                        title="Edit User"
                      >
                        Edit
                      </button>
                      <button
                        className="admin-btn admin-btn-warning btn-small"
                        onClick={() => handleToggleActive(user.id, user.isActive)}
                        title={user.isActive ? 'Deactivate User' : 'Activate User'}
                      >
                        {user.isActive ? 'Deactivate' : 'Activate'}
                      </button>
                      <button
                        className="admin-btn admin-btn-danger btn-small"
                        onClick={() => handleDeleteUser(user.id)}
                        title="Delete User"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default UserManagement;
