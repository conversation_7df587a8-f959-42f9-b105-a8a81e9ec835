# Fix database user permissions

Write-Host "=== Fixing DesignXcel Database User ===" -ForegroundColor Green

try {
    $connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=master;Integrated Security=True;TrustServerCertificate=True;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to SQL Server" -ForegroundColor Green
    
    # Check if login exists
    $command = $connection.CreateCommand()
    $command.CommandText = "SELECT name, is_disabled FROM sys.server_principals WHERE name = 'DesignXcel'"
    $reader = $command.ExecuteReader()
    
    if ($reader.Read()) {
        $loginName = $reader["name"]
        $isDisabled = $reader["is_disabled"]
        Write-Host "✅ Login '$loginName' exists, Disabled: $isDisabled" -ForegroundColor Green
    } else {
        Write-Host "❌ Login 'DesignXcel' does not exist" -ForegroundColor Red
    }
    $reader.Close()
    
    # Drop and recreate the login to ensure it's properly configured
    Write-Host "Recreating login..." -ForegroundColor Cyan
    
    $command.CommandText = "IF EXISTS (SELECT name FROM sys.server_principals WHERE name = 'DesignXcel') DROP LOGIN [DesignXcel]"
    $command.ExecuteNonQuery() | Out-Null
    
    $command.CommandText = @"
CREATE LOGIN [DesignXcel] WITH PASSWORD = '****************', 
DEFAULT_DATABASE = [DesignXcelDB], 
CHECK_EXPIRATION = OFF, 
CHECK_POLICY = OFF
"@
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✅ Login recreated" -ForegroundColor Green
    
    # Switch to DesignXcelDB and fix user
    $command.CommandText = "USE [DesignXcelDB]"
    $command.ExecuteNonQuery() | Out-Null
    
    # Drop existing user if it exists
    $command.CommandText = "IF EXISTS (SELECT name FROM sys.database_principals WHERE name = 'DesignXcel') DROP USER [DesignXcel]"
    $command.ExecuteNonQuery() | Out-Null
    
    # Create user and assign permissions
    $command.CommandText = @"
CREATE USER [DesignXcel] FOR LOGIN [DesignXcel];
ALTER ROLE [db_owner] ADD MEMBER [DesignXcel];
"@
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✅ Database user recreated with db_owner permissions" -ForegroundColor Green
    
    $connection.Close()
    
    # Test the connection
    Write-Host ""
    Write-Host "Testing SQL Server authentication..." -ForegroundColor Cyan
    
    $testConnectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;User Id=DesignXcel;Password=****************;TrustServerCertificate=True;"
    $testConnection = New-Object System.Data.SqlClient.SqlConnection($testConnectionString)
    $testConnection.Open()
    
    $testCommand = $testConnection.CreateCommand()
    $testCommand.CommandText = "SELECT DB_NAME() as DatabaseName, USER_NAME() as UserName, SYSTEM_USER as SystemUser"
    $testReader = $testCommand.ExecuteReader()
    
    if ($testReader.Read()) {
        Write-Host "✅ SQL Server authentication successful!" -ForegroundColor Green
        Write-Host "  Database: $($testReader['DatabaseName'])" -ForegroundColor Green
        Write-Host "  User: $($testReader['UserName'])" -ForegroundColor Green
        Write-Host "  System User: $($testReader['SystemUser'])" -ForegroundColor Green
    }
    
    $testReader.Close()
    $testConnection.Close()
    
    Write-Host ""
    Write-Host "🎉 Database connection is now ready!" -ForegroundColor Green
    Write-Host "You can now start your DesignXcel backend application." -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Fix Complete ===" -ForegroundColor Green
