# Clean up and recreate database user properly

Write-Host "=== Cleaning Up and Recreating Database User ===" -ForegroundColor Green

try {
    # Connect with Windows Authentication to master database
    $connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=master;Integrated Security=True;TrustServerCertificate=True;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to SQL Server with Windows Authentication" -ForegroundColor Green
    
    $command = $connection.CreateCommand()
    
    # First, let's see what users exist in the DesignXcelDB database
    Write-Host "Checking existing users in DesignXcelDB..." -ForegroundColor Cyan
    $command.CommandText = @"
USE [DesignXcelDB];
SELECT 
    dp.name AS principal_name,
    dp.type_desc AS principal_type,
    dp.sid AS principal_sid
FROM sys.database_principals dp
WHERE dp.name = 'DesignXcel' OR dp.sid IN (
    SELECT sid FROM sys.server_principals WHERE name = 'DesignXcel'
)
"@
    $reader = $command.ExecuteReader()
    
    $foundUsers = @()
    while ($reader.Read()) {
        $foundUsers += @{
            Name = $reader["principal_name"]
            Type = $reader["principal_type"]
            SID = $reader["principal_sid"]
        }
        Write-Host "  Found user: $($reader['principal_name']) ($($reader['principal_type']))" -ForegroundColor Yellow
    }
    $reader.Close()
    
    # Drop any existing database users that might be orphaned
    if ($foundUsers.Count -gt 0) {
        Write-Host "Cleaning up existing database users..." -ForegroundColor Cyan
        foreach ($user in $foundUsers) {
            try {
                $command.CommandText = "USE [DesignXcelDB]; DROP USER [$($user.Name)]"
                $command.ExecuteNonQuery() | Out-Null
                Write-Host "  Dropped user: $($user.Name)" -ForegroundColor Green
            } catch {
                Write-Host "  Could not drop user $($user.Name): $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    }
    
    # Check if login exists and get its SID
    Write-Host "Checking login status..." -ForegroundColor Cyan
    $command.CommandText = "SELECT name, sid, is_disabled FROM sys.server_principals WHERE name = 'DesignXcel'"
    $reader = $command.ExecuteReader()
    
    $loginExists = $false
    if ($reader.Read()) {
        $loginExists = $true
        $loginSID = $reader["sid"]
        $isDisabled = $reader["is_disabled"]
        Write-Host "  Login exists, SID: $loginSID, Disabled: $isDisabled" -ForegroundColor Green
    }
    $reader.Close()
    
    if (-not $loginExists) {
        Write-Host "Creating new login..." -ForegroundColor Cyan
        $command.CommandText = @"
CREATE LOGIN [DesignXcel] WITH PASSWORD = '****************', 
DEFAULT_DATABASE = [DesignXcelDB], 
CHECK_EXPIRATION = OFF, 
CHECK_POLICY = OFF
"@
        $command.ExecuteNonQuery() | Out-Null
        Write-Host "✅ Login created successfully" -ForegroundColor Green
    }
    
    # Now create the database user properly
    Write-Host "Creating database user..." -ForegroundColor Cyan
    $command.CommandText = @"
USE [DesignXcelDB];
CREATE USER [DesignXcel] FOR LOGIN [DesignXcel];
ALTER ROLE [db_owner] ADD MEMBER [DesignXcel];
"@
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✅ Database user created with db_owner permissions" -ForegroundColor Green
    
    $connection.Close()
    
    # Test the connection
    Write-Host ""
    Write-Host "Testing the new configuration..." -ForegroundColor Cyan
    
    $testConnectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;User Id=DesignXcel;Password=****************;TrustServerCertificate=True;"
    $testConnection = New-Object System.Data.SqlClient.SqlConnection($testConnectionString)
    $testConnection.Open()
    
    $testCommand = $testConnection.CreateCommand()
    $testCommand.CommandText = @"
SELECT 
    DB_NAME() as DatabaseName, 
    USER_NAME() as UserName, 
    SYSTEM_USER as SystemUser,
    IS_MEMBER('db_owner') as IsDbOwner
"@
    $testReader = $testCommand.ExecuteReader()
    
    if ($testReader.Read()) {
        Write-Host "✅ SQL Server authentication test successful!" -ForegroundColor Green
        Write-Host "  Database: $($testReader['DatabaseName'])" -ForegroundColor Green
        Write-Host "  User: $($testReader['UserName'])" -ForegroundColor Green
        Write-Host "  System User: $($testReader['SystemUser'])" -ForegroundColor Green
        Write-Host "  Is DB Owner: $($testReader['IsDbOwner'])" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "🎉 Database connection is now working!" -ForegroundColor Green
        Write-Host "Your DesignXcel backend application should now be able to connect successfully." -ForegroundColor Green
    }
    
    $testReader.Close()
    $testConnection.Close()
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Cleanup Complete ===" -ForegroundColor Green
