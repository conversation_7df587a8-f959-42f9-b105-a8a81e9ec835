import { useState, useEffect, useCallback } from 'react';
import cartService from '../services/cartService';

export const useCart = () => {
  const [cart, setCart] = useState(cartService.getCart());
  const [loading, setLoading] = useState(false);
  const [validationResult, setValidationResult] = useState(null);

  // Update cart state when cart service changes
  useEffect(() => {
    const handleCartChange = (updatedCart) => {
      setCart(updatedCart);
    };

    cartService.addListener(handleCartChange);

    return () => {
      cartService.removeListener(handleCartChange);
    };
  }, []);

  // Add item to cart
  const addToCart = useCallback(async (item) => {
    setLoading(true);
    try {
      const result = await cartService.addToCart(item);
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update item quantity
  const updateQuantity = useCallback(async (variantId, quantity) => {
    setLoading(true);
    try {
      const result = await cartService.updateQuantity(variantId, quantity);
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  // Remove item from cart
  const removeFromCart = useCallback((variantId) => {
    return cartService.removeFromCart(variantId);
  }, []);

  // Clear entire cart
  const clearCart = useCallback(() => {
    return cartService.clearCart();
  }, []);

  // Validate cart against current inventory
  const validateCart = useCallback(async () => {
    setLoading(true);
    try {
      const result = await cartService.validateCart();
      setValidationResult(result);
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  // Reserve inventory for checkout
  const reserveInventory = useCallback(async () => {
    setLoading(true);
    try {
      const result = await cartService.reserveInventory();
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  // Release reserved inventory
  const releaseReservedInventory = useCallback(async () => {
    setLoading(true);
    try {
      const result = await cartService.releaseReservedInventory();
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get cart statistics
  const getCartStats = useCallback(() => {
    return {
      itemCount: cartService.getItemCount(),
      total: cartService.getTotal(),
      isEmpty: cart.items.length === 0,
      hasReservation: !!cart.reservationId,
      reservedAt: cart.reservedAt
    };
  }, [cart]);

  // Check if specific item is in cart
  const isInCart = useCallback((variantId) => {
    return cart.items.some(item => item.variantId === variantId);
  }, [cart.items]);

  // Get quantity of specific item in cart
  const getItemQuantity = useCallback((variantId) => {
    const item = cart.items.find(item => item.variantId === variantId);
    return item ? item.quantity : 0;
  }, [cart.items]);

  return {
    cart,
    loading,
    validationResult,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    validateCart,
    reserveInventory,
    releaseReservedInventory,
    getCartStats,
    isInCart,
    getItemQuantity
  };
};

export default useCart;
