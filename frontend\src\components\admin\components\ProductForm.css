/* Product Form Component */
.product-form {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.pf-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.pf-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.pf-header-actions {
  display: flex;
  gap: 12px;
}

.pf-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pf-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.pf-btn-primary {
  background: #F0B21B;
  color: white;
  box-shadow: 0 2px 4px rgba(240, 178, 27, 0.3);
}

.pf-btn-primary:hover:not(:disabled) {
  background: #d49e17;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(240, 178, 27, 0.4);
}

.pf-btn-secondary {
  background: white;
  color: #666;
  border: 1px solid #e1e8ed;
}

.pf-btn-secondary:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #F0B21B;
  color: #F0B21B;
}

/* Tabs */
.pf-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e8ed;
}

.pf-tab-btn {
  flex: 1;
  padding: 16px 20px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-bottom: 3px solid transparent;
}

.pf-tab-btn:hover {
  background: #f1f3f4;
  color: #2c3e50;
}

.pf-tab-btn.active {
  background: white;
  color: #F0B21B;
  border-bottom-color: #F0B21B;
}

.pf-tab-icon {
  font-size: 16px;
}

/* Content */
.pf-content {
  padding: 24px;
}

.pf-tab-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Form Elements */
.pf-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.pf-form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.pf-form-group.pf-full-width {
  grid-column: 1 / -1;
}

.pf-label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.pf-input,
.pf-select,
.pf-textarea {
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
  font-family: inherit;
}

.pf-input:focus,
.pf-select:focus,
.pf-textarea:focus {
  outline: none;
  border-color: #F0B21B;
  box-shadow: 0 0 0 3px rgba(240, 178, 27, 0.1);
}

.pf-textarea {
  resize: vertical;
  min-height: 100px;
}

.pf-select {
  cursor: pointer;
}

/* Dimensions */
.pf-dimensions-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.pf-dimensions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.pf-dimension-input {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.pf-dimension-input label {
  font-size: 13px;
  font-weight: 500;
  color: #666;
}

/* Checkbox */
.pf-checkbox-group {
  margin-top: 8px;
}

.pf-checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #2c3e50;
}

.pf-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #F0B21B;
}

.pf-checkbox-text {
  font-weight: 500;
}

/* Upload Sections */
.pf-upload-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pf-section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  padding-bottom: 8px;
  border-bottom: 2px solid #F0B21B;
}

.pf-uploaded-files {
  margin-top: 16px;
}

.pf-uploaded-files h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.pf-file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pf-file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.pf-file-item:hover {
  background: #f1f3f4;
  border-color: #F0B21B;
}

.pf-file-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.pf-file-name {
  flex: 1;
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pf-file-remove {
  width: 24px;
  height: 24px;
  border: none;
  background: #ff6b6b;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.pf-file-remove:hover {
  background: #ff5252;
  transform: scale(1.1);
}

/* Preview Section */
.pf-preview-section {
  min-height: 400px;
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pf-no-preview {
  text-align: center;
  color: #666;
}

.pf-no-preview-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.pf-no-preview h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #2c3e50;
}

.pf-no-preview p {
  margin: 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1199px) {
  .pf-form-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .pf-dimensions-grid {
    gap: 12px;
  }
}

@media (max-width: 767px) {
  .pf-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .pf-header-actions {
    justify-content: center;
  }
  
  .pf-tabs {
    flex-direction: column;
  }
  
  .pf-tab-btn {
    justify-content: flex-start;
    padding: 12px 20px;
  }
  
  .pf-content {
    padding: 16px;
  }
  
  .pf-form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .pf-dimensions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .pf-btn {
    min-height: 44px;
  }
}

/* Touch targets for mobile */
@media (max-width: 767px) {
  .pf-input,
  .pf-select,
  .pf-textarea {
    min-height: 44px;
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .pf-tab-btn {
    min-height: 44px;
  }
}
