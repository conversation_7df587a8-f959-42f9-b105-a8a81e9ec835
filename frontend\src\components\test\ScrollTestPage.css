.scroll-test-page {
    min-height: 300vh;
    padding: 0;
    margin: 0;
}

.test-section {
    min-height: 100vh;
    padding: 4rem 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.hero-section {
    background: linear-gradient(135deg, #F0B21B 0%, #d4a017 100%);
    color: white;
    position: relative;
}

.hero-section h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-section p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.content-section {
    background: #f8f9fa;
    color: #333;
}

.content-section:nth-child(even) {
    background: white;
}

.content-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.content-section p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 600px;
    line-height: 1.6;
}

.content-block {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    text-align: left;
}

.content-section:nth-child(even) .content-block {
    background: #f8f9fa;
}

.content-block h3 {
    color: #F0B21B;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.content-block ul {
    list-style: none;
    padding: 0;
}

.content-block li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 1rem;
    line-height: 1.5;
}

.content-block li:last-child {
    border-bottom: none;
}

.content-block strong {
    color: #2c3e50;
    font-weight: 600;
}

.final-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
}

.final-section h2 {
    color: white;
    font-size: 2.5rem;
}

.scroll-indicator {
    margin-top: 2rem;
    animation: bounce 2s infinite;
}

.scroll-indicator span {
    font-size: 1.25rem;
    font-weight: 600;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .test-section {
        padding: 2rem 1rem;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .content-section h2 {
        font-size: 1.75rem;
    }
    
    .content-block {
        padding: 1.5rem;
        margin: 0 1rem;
    }
}
