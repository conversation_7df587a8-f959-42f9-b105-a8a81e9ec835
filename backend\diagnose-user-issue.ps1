# Diagnose and fix user mapping issue

Write-Host "=== Diagnosing User Mapping Issue ===" -ForegroundColor Green

try {
    $connectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;Integrated Security=True;TrustServerCertificate=True;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to DesignXcelDB" -ForegroundColor Green
    
    $command = $connection.CreateCommand()
    
    # Check all database users and their login mappings
    Write-Host "Checking all database users and their login mappings..." -ForegroundColor Cyan
    $command.CommandText = @"
SELECT 
    dp.name AS database_user,
    dp.type_desc AS user_type,
    sp.name AS server_login,
    dp.sid AS user_sid,
    sp.sid AS login_sid
FROM sys.database_principals dp
LEFT JOIN sys.server_principals sp ON dp.sid = sp.sid
WHERE dp.type IN ('S', 'U')  -- SQL users and Windows users
ORDER BY dp.name
"@
    $reader = $command.ExecuteReader()
    
    Write-Host "Database Users and Login Mappings:" -ForegroundColor Yellow
    while ($reader.Read()) {
        $dbUser = $reader["database_user"]
        $userType = $reader["user_type"]
        $serverLogin = if ($reader["server_login"] -eq [DBNull]::Value) { "NULL" } else { $reader["server_login"] }
        Write-Host "  DB User: $dbUser ($userType) -> Server Login: $serverLogin" -ForegroundColor White
    }
    $reader.Close()
    
    # Check if DesignXcel login is mapped to dbo
    Write-Host ""
    Write-Host "Checking if DesignXcel login is mapped to dbo..." -ForegroundColor Cyan
    $command.CommandText = @"
SELECT 
    dp.name AS database_user,
    sp.name AS server_login
FROM sys.database_principals dp
JOIN sys.server_principals sp ON dp.sid = sp.sid
WHERE sp.name = 'DesignXcel'
"@
    $reader = $command.ExecuteReader()
    
    $mappedUser = $null
    if ($reader.Read()) {
        $mappedUser = $reader["database_user"]
        Write-Host "  DesignXcel login is mapped to database user: $mappedUser" -ForegroundColor Yellow
    } else {
        Write-Host "  DesignXcel login is not mapped to any database user" -ForegroundColor Green
    }
    $reader.Close()
    
    if ($mappedUser -eq "dbo") {
        Write-Host ""
        Write-Host "Issue found: DesignXcel login is mapped to 'dbo' user!" -ForegroundColor Red
        Write-Host "This happens when the login was the database owner when the database was created." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Testing if we can connect as dbo..." -ForegroundColor Cyan
        
        $connection.Close()
        
        # Test connection with DesignXcel login (should work as dbo)
        $testConnectionString = "Server=DESKTOP-F4OI6BT\SQLEXPRESS;Database=DesignXcelDB;User Id=DesignXcel;Password=****************;TrustServerCertificate=True;"
        $testConnection = New-Object System.Data.SqlClient.SqlConnection($testConnectionString)
        $testConnection.Open()
        
        $testCommand = $testConnection.CreateCommand()
        $testCommand.CommandText = @"
SELECT 
    DB_NAME() as DatabaseName, 
    USER_NAME() as UserName, 
    SYSTEM_USER as SystemUser,
    IS_MEMBER('db_owner') as IsDbOwner
"@
        $testReader = $testCommand.ExecuteReader()
        
        if ($testReader.Read()) {
            Write-Host "✅ Connection successful!" -ForegroundColor Green
            Write-Host "  Database: $($testReader['DatabaseName'])" -ForegroundColor Green
            Write-Host "  User: $($testReader['UserName'])" -ForegroundColor Green
            Write-Host "  System User: $($testReader['SystemUser'])" -ForegroundColor Green
            Write-Host "  Is DB Owner: $($testReader['IsDbOwner'])" -ForegroundColor Green
            
            Write-Host ""
            Write-Host "🎉 The connection actually works!" -ForegroundColor Green
            Write-Host "The DesignXcel login is mapped to the 'dbo' user, which has full permissions." -ForegroundColor Green
            Write-Host "This is actually a valid configuration for your application." -ForegroundColor Green
        }
        
        $testReader.Close()
        $testConnection.Close()
        
    } else {
        Write-Host "The login mapping looks correct. There might be another issue." -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Diagnosis Complete ===" -ForegroundColor Green
